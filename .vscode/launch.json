{
    // 使用 IntelliSense 了解相关属性。 
    // 悬停以查看现有属性的描述。
    // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "precaching",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/fpack_cache_latents.py",
            "python": "/media/miniconda3/envs/musubi_copy/bin/python",
            "console": "integratedTerminal",
            "args": [
                "--dataset_config", "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_config_10.toml",
                "--vae", "/media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/vae/diffusion_pytorch_model.safetensors",
                "--image_encoder", "/media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--lllyasviel--flux_redux_bfl/snapshots/45b801affc54ff2af4e5daf1b282e0921901db87/image_encoder/model.safetensors",
                "--vae_chunk_size", "32",
                "--vae_spatial_tile_sample_min_size", "128",
                "--f1"
              ]
        },
        {
            "name": "Text Encoder Output Pre-caching",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/fpack_cache_text_encoder_outputs.py",
            "python": "/media/miniconda3/envs/musubi_copy/bin/python",
            "console": "integratedTerminal",
            "args": [
              "--dataset_config", "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_config_10.toml",
              "--text_encoder1", "/media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/text_encoder",
              "--text_encoder2", "/media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/text_encoder_2",
              "--batch_size", "16"]
        },
        {
            "name": "Train FramePack-F1 LoRA",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/fpack_train_network.py",
            "python": "/media/miniconda3/envs/musubi_copy/bin/python",
            "console": "integratedTerminal",
            "args": [
              "--dit", "/media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7",
              "--vae", "/media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/vae/diffusion_pytorch_model.safetensors",
              "--text_encoder1", "/media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/text_encoder",
              "--text_encoder2", "/media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/text_encoder_2",
              "--image_encoder", "path/to/image_encoder_model.safetensors",
              "--dataset_config", "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_config.toml",
              "--sdpa",
              "--mixed_precision", "bf16",
              "--optimizer_type", "adamw8bit",
              "--learning_rate", "2e-4",
              "--gradient_checkpointing",
              "--timestep_sampling", "shift",
              "--weighting_scheme", "none",
              "--discrete_flow_shift", "3.0",
              "--max_data_loader_n_workers", "8",//尝试增加线程
              "--persistent_data_loader_workers",
              "--network_module", "networks.lora_framepack",
              "--network_dim", "32",
              "--max_train_epochs", "16",
              "--save_every_n_epochs", "1",
              "--seed", "42",
              "--output_dir", "/media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/block_handover_D435_full",
              "--output_name", "train_lora"//记得修改
            ],
            "justMyCode": false
        },
        {
            "name": "lora_infer",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/fpack_generate_video.py",
            "python": "/media/miniconda3/envs/musubi_copy/bin/python",
            "console": "integratedTerminal",
            "env": {
              "CUDA_VISIBLE_DEVICES": "3"  
            },
            "args": [
              "--dit", "/media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7",
              "--vae", "/media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/vae/diffusion_pytorch_model.safetensors",
              "--text_encoder1", "/media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/text_encoder",
              "--text_encoder2", "/media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/text_encoder_2",
              "--image_encoder", "/media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--lllyasviel--flux_redux_bfl/snapshots/45b801affc54ff2af4e5daf1b282e0921901db87/image_encoder/model.safetensors",
              "--image_path", "/media/jiayueru/yuwanhe/musubi-tuner/test_robotwin_10/iter/img/0s.jpg",
              "--prompt", "Use arm movement to pass block to other arm at handover, then place on target.",
              "--video_size", "512", "768",
              "--video_seconds", "1",
              "--fps", "30",
              "--infer_steps", "25",
              "--attn_mode", "sdpa",
              "--fp8_scaled",
              "--vae_chunk_size", "32",
              "--vae_spatial_tile_sample_min_size", "128",
              "--save_path", "/media/jiayueru/yuwanhe/musubi-tuner/test_robotwin_10/iter/output",
              "--output_type", "both",
              "--seed", "1234",
              "--lora_multiplier", "1.0",
              "--lora_weight", "/media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/block_handover_D435_10/train_lora.safetensors",
              "--f1"
            ]
          },
          {
            "name": "Dataset Main",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/dataset/preprocess_rdt.py",
            "console": "integratedTerminal",
            "justMyCode": false,
            "env": {
                "PYTHONPATH": "${workspaceFolder}",
                "SHELL": "/usr/bin/zsh"
            },
            "args": [
                "block_handover",
                "D435",
                "50",
                "--T",
                "9"
            ],
            "python": "/media/miniconda3/envs/musubi_copy/bin/python",
        }
    ]
}
