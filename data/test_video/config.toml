# Common parameters (resolution, caption_extension, batch_size, num_repeats, enable_bucket, bucket_no_upscale) 
# can be set in either general or datasets sections
# Video-specific parameters (target_frames, frame_extraction, frame_stride, frame_sample, max_frames, source_fps)
# must be set in each datasets section

# caption_extension is not required for metadata jsonl file
# cache_directory is required for each dataset with metadata jsonl file

# general configurations
[general]
resolution = [1146, 720]
batch_size = 1
enable_bucket = true
bucket_no_upscale = false

[[datasets]]
video_jsonl_file = "/media/jiayueru/yuwanhe/musubi-tuner/data/test_video/metadata.jsonl"
frame_extraction = "full"
max_frames = 300
cache_directory = "/media/jiayueru/yuwanhe/musubi-tuner/data/test_cache"
source_fps = 30.0 # optional, source fps for videos in the jsonl file
# same metadata jsonl file can be used for multiple datasets

# other datasets can be added here. each dataset can have different configurations