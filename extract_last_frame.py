import cv2
import os
import argparse

def extract_last_frame(video_path, output_path, output_filename):
    # 检查视频文件是否存在
    if not os.path.exists(video_path):
        print(f"错误：视频文件 {video_path} 不存在。")
        return False

    # 打开视频文件
    cap = cv2.VideoCapture(video_path)

    # 检查视频是否成功打开
    if not cap.isOpened():
        print(f"错误：无法打开视频文件 {video_path}。")
        return False

    # 获取视频的总帧数
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    # 如果视频没有帧，直接返回
    if total_frames == 0:
        print(f"错误：视频文件 {video_path} 不包含任何帧。")
        cap.release()
        return False

    # 设置要读取的帧位置为最后一帧
    cap.set(cv2.CAP_PROP_POS_FRAMES, total_frames - 1)

    # 读取最后一帧
    ret, frame = cap.read()

    # 释放视频捕获对象
    cap.release()

    # 检查是否成功读取帧
    if not ret or frame is None:
        print(f"错误：无法读取视频 {video_path} 的最后一帧。")
        return False

    # 构建完整的输出路径
    if not os.path.exists(output_path):
        os.makedirs(output_path)
    output_file = os.path.join(output_path, output_filename)

    # 保存帧为图像文件
    try:
        cv2.imwrite(output_file, frame)
        print(f"成功将最后一帧保存为 {output_file}")
        return True
    except Exception as e:
        print(f"错误：保存图像时出错 - {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser(description='提取视频的最后一帧并保存为图片')
    parser.add_argument('--video_path', required=True, help='视频文件的路径')
    parser.add_argument('--output_path', required=True, help='保存图片的路径')
    parser.add_argument('--output_filename', required=True, help='保存的文件名，包括扩展名（如0.jpg或1.png）')

    args = parser.parse_args()

    extract_last_frame(args.video_path, args.output_path, args.output_filename)

if __name__ == "__main__":
    main()    