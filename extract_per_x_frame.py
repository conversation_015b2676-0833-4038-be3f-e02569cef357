import os
import cv2
import argparse

def process_video(input_path, output_path, x):
    """
    处理单个视频文件，按间隔x抽取帧并合成新视频
        
    参数:
        input_path: 输入视频文件路径
        output_path: 输出视频文件夹路径
        x: 帧抽取间隔
                        """
    # 打开视频文件
    cap = cv2.VideoCapture(input_path)
        
    # 检查视频是否成功打开
    if not cap.isOpened():
        print(f"无法打开视频文件: {input_path}")
        return
        
    # 获取视频基本信息
    fps = cap.get(cv2.CAP_PROP_FPS)
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
    # 生成输出文件名
    filename = os.path.basename(input_path)
    name, ext = os.path.splitext(filename)
    output_filename = f"{name}_{x}{ext}"
    output_filepath = os.path.join(output_path, output_filename)
        
    # 定义视频编码器和创建VideoWriter对象
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')  # 用于MP4格式
    out = cv2.VideoWriter(output_filepath, fourcc, fps, (width, height))
        
    frame_count = 0
    saved_frames = 0
        
    while frame_count < total_frames:
        # 移动到指定帧
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_count)
            
        # 读取帧
        ret, frame = cap.read()
        if not ret:
            break
            
        # 保存帧（第一帧和每隔x帧保存一次）
        if frame_count % x == 0:
            out.write(frame)
            saved_frames += 1
            
        # 跳到下一需要处理的帧
        frame_count += x
        
    # 释放资源
    cap.release()
    out.release()
        
    print(f"处理完成: {filename}")
    print(f"原始帧数: {total_frames}, 保存帧数: {saved_frames}")
    print(f"输出文件: {output_filepath}\n")

def process_all_videos(input_dir, output_dir, x):
    """
        处理输入目录中所有符合条件的视频文件
        
        参数:
            input_dir: 输入视频文件夹路径
            output_dir: 输出视频文件夹路径
            x: 帧抽取间隔
    """
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
        
    # 遍历输入目录中的所有文件
    for filename in os.listdir(input_dir):
        # 检查文件是否为MP4视频且符合命名格式
        if filename.endswith('.mp4') and filename.startswith('episode_'):
            input_path = os.path.join(input_dir, filename)
            process_video(input_path, output_dir, x)

if __name__ == "__main__":
    # 设置命令行参数
    parser = argparse.ArgumentParser(description='按指定间隔从视频中抽取帧并合成新视频')
    parser.add_argument('input_dir', help='输入视频文件夹路径')
    parser.add_argument('output_dir', help='输出视频文件夹路径')
    parser.add_argument('x', type=int, help='帧抽取间隔')
        
    # 解析参数
    args = parser.parse_args()
        
    # 执行处理
    process_all_videos(args.input_dir, args.output_dir, args.x)
    