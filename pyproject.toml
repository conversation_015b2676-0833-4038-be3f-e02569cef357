[project]
name = "musubi-tuner"
version = "0.1.0"
description = "<PERSON><PERSON><PERSON> by kohya_ss"
readme = "README.md"
requires-python = ">=3.10, <3.11"
dependencies = [
    "accelerate>=1.6.0",
    "ascii-magic==2.3.0",
    "av==14.0.1",
    "bitsandbytes>=0.45.0",
    "diffusers>=0.32.1",
    "easydict==1.13",
    "einops>=0.7.0",
    "ftfy==6.3.1",
    "huggingface-hub>=0.30.0",
    "matplotlib>=3.10.0",
    "opencv-python>=*********",
    "pillow>=10.2.0",
    "safetensors>=0.4.5",
    "sageattention>=1.0.6",
    "tensorboard>=2.18.0",
    "toml>=0.10.2",
    "torch>=2.5.1",
    "torchvision>=0.20.1",
    "tqdm>=4.66.5",
    "transformers>=4.46.3",
    "voluptuous>=0.15.2",
]

[tool.uv.sources]
torch = [
  { index = "pytorch-cu124" },
]
torchvision = [
  { index = "pytorch-cu124" },
]

[[tool.uv.index]]
name = "pytorch-cu124"
url = "https://download.pytorch.org/whl/cu124"
explicit = true
