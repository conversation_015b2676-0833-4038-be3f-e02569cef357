import torch
import cv2
import numpy as np
import sys
import pickle
import os
import argparse
import json
sys.path.append('/media/jiayueru/Codes/RoboTwin/policy/RDT')

def episode_to_video_tensor(subfolder_path,episode_idx=0):
    """
    将robotwin中一个episode数据转成一个video_tensor

    参数：
    - subfolder_path: 存放episode数据的子文件夹路径
    - episode_idx: episode的索引

    返回：
    - video_tensor: 转换后的视频张量，维度为 (B=1, C, T, H, W)
    """
    pkl_files = [f for f in os.listdir(subfolder_path) if f.endswith('.pkl')]
    pkl_files = sorted(pkl_files, key=lambda x: int(x.split('.')[0]))

    # 加载整个episode的图像
    cam_high_list = []
    width, height = 768, 512
    for j in range(len(pkl_files)):
        pkl_file_path = os.path.join(subfolder_path, f'{j}.pkl')
        with open(pkl_file_path, 'rb') as pkl_f:
            data = pickle.load(pkl_f)
        camera_high = data['observation']['head_camera']['rgb']
        camera_high = camera_high[:,:,::-1]  # BGR to RGB
        camera_high_resized = cv2.resize(camera_high, (width, height)) # (H, W, 3)
        cam_high_list.append(camera_high_resized)

    # 将图像转换为video tensor格式: (B, C, T, H, W)
    episode_images = []
    for idx, img in enumerate(cam_high_list):
        # from debug.utils import debug_plot_and_save_image
        # debug_plot_and_save_image(img, save_dir="./debug_outputs", name=f"image_{idx}")
        from dataset import image_video_dataset
        img_np = image_video_dataset.resize_image_to_bucket(img, (width, height))
        image_tensor = torch.from_numpy(img_np).float() / 127.5 - 1.0  # -1 to 1.0, HWC
        image_tensor = image_tensor.permute(2, 0, 1)
        episode_images.append(image_tensor)
    video_tensor = torch.stack(episode_images, dim=1)  # (C, T, H, W)
    video_tensor = video_tensor.unsqueeze(0)  # (1, C, T, H, W)
    return video_tensor


def tensor_to_video(video_tensor, output_path, fps=30.0, episode_idx=0):
    """
    将PyTorch视频张量转换为MP4视频文件并保存到output_path
    
    参数:
    - video_tensor: 视频张量，维度为 (B=1, C, T, H, W)
    - output_path: 输出视频文件路径
    - fps: 视频帧率
    """
    # 确保输入张量维度正确
    assert video_tensor.dim() == 5 and video_tensor.shape[0] == 1, "张量维度应为 (1, C, T, H, W)"
    
    # 分离各维度
    _, C, T, H, W = video_tensor.shape
    
    # 创建视频写入器
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    video_writer = cv2.VideoWriter(output_path, fourcc, fps, (W, H))
    
    try:
        # 将张量从GPU移到CPU（如果需要）并转换为numpy数组
        frames = video_tensor[0].permute(1, 2, 3, 0).cpu().numpy()  # (T, H, W, C)
        
        # 处理每一帧
        for frame in frames:
            # 确保像素值在0-255范围内
            if frame.dtype != np.uint8:
                # 如果是浮点数，假设范围是[0,1]或[-1,1]，将其转换为[0,255]
                if frame.min() < 0:
                    frame = (frame + 1) / 2 * 255  # 将[-1,1]映射到[0,255]
                else:
                    frame = frame * 255  # 将[0,1]映射到[0,255]
                frame = np.clip(frame, 0, 255).astype(np.uint8)
            
            # 如果是RGB格式，转换为BGR（OpenCV默认格式）
            if C == 3 and frame.shape[2] == 3:
                frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
            
            # 写入视频
            video_writer.write(frame)
            
        print(f"视频已成功保存到 {output_path}")
    finally:
        # 释放资源
        video_writer.release()

def append_to_jsonl(video_path, caption, output_file):
    """
    将视频路径和描述按JSONL格式追加到指定文件
    
    参数:
    - video_path: 视频文件路径
    - caption: 视频描述文本
    - output_file: 输出JSONL文件路径
    """
    # 创建数据字典
    data = {
        "video_path": video_path,
        "caption": caption
    }
    
    # 将数据字典转换为JSON字符串并写入文件
    with open(output_file, 'a', encoding='utf-8') as f:
        json_line = json.dumps(data)
        f.write(json_line + '\n')


# 使用示例
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='make data for framepack')
    parser.add_argument('task_name', type=str, default='block_handover', help='The name of the task (e.g., block_handover)')
    parser.add_argument('head_camera_type', type=str, default='D435', help='camera type')
    parser.add_argument('expert_data_num', type=int, default=100, help='Number of episodes to process (e.g., 50)')
    args = parser.parse_args()

    task_name = args.task_name
    head_camera_type = args.head_camera_type
    num = args.expert_data_num

    data_path_name = task_name+"_"+head_camera_type+"_pkl"
    print(f'read data from path:{os.path.join("data/", data_path_name)}')

    # 设备配置
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"Using device: {device}")

    # 读取所有instructions（假设为统一json文件）   
    # root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    root_path = "/media/jiayueru/Codes/RoboTwin"
    instruction_path = f"{root_path}/data/instructions/{task_name}.json"
    with open(instruction_path, 'r') as f_instr:
        instruction_dict = json.load(f_instr)
    instructions = instruction_dict['instructions']

    # save_path = f"./policy/RDT/processed_data/{task_name}_{head_camera_type}_{num}"
    save_path = f"{root_path}/policy/RDT/processed_data/{task_name}_{head_camera_type}_{num}"

    for i in range(num):
        print(f"\n=== Processing episode {i} ===")
        subfolder_name = f"episode{i}"
        subfolder_path = os.path.join(f"{root_path}/data/", data_path_name, subfolder_name)
        if not os.path.isdir(subfolder_path):
            print(f"[Warning] {subfolder_path} does not exist, skipping.")
            continue

        # 取每个episode的instruction
        if i < len(instructions):
            instruction = instructions[i]
        else:
            instruction = instructions[-1]  # fallback

        #取每个episode的video_tensor
        video_tensor = episode_to_video_tensor(subfolder_path, episode_idx=i)
        #取每个episode对应的video（mp4文件）
        video_path_name = task_name+"_"+head_camera_type
        video_path = f"./data/robotwin/{video_path_name}"
        os.makedirs(video_path, exist_ok=True)  # exist_ok=True 避免目录已存在时出错
        tensor_to_video(video_tensor, output_path=f"{video_path}/episode_{i}.mp4", fps=30, episode_idx=i)

        #将一个episode的video和caption写到jsonl中,在miusubi-tuner目录下跑
        metadata_path_name= task_name+"_"+head_camera_type+"_metadata.jsonl"
        append_to_jsonl(f"{video_path}/episode_{i}.mp4", instruction, "./data/robotwin/"+metadata_path_name)