nohup: ignoring input
The following values were not passed to `accelerate launch` and had defaults used instead:
	`--num_machines` was set to a value of `1`
	`--mixed_precision` was set to a value of `'no'`
	`--dynamo_backend` was set to a value of `'no'`
To avoid this warning pass in values for each of the problematic parameters or run `accelerate config`.
Xformers is not installed!Xformers is not installed!Xformers is not installed!Xformers is not installed!Xformers is not installed!Xformers is not installed!Xformers is not installed!






Flash Attn is not installed!
Flash Attn is not installed!
Flash Attn is not installed!
Flash Attn is not installed!
Flash Attn is not installed!
Flash Attn is not installed!
Flash Attn is not installed!
Xformers is not installed!
Sage Attn is not installed!Sage Attn is not installed!

Sage Attn is not installed!
Sage Attn is not installed!
Sage Attn is not installed!
Sage Attn is not installed!
Sage Attn is not installed!
Flash Attn is not installed!
Sage Attn is not installed!
Trying to import sageattentionTrying to import sageattentionTrying to import sageattentionTrying to import sageattentionTrying to import sageattention
Trying to import sageattention


Trying to import sageattention


Failed to import sageattention
Failed to import sageattention
Failed to import sageattention
Failed to import sageattention
Trying to import sageattention
Failed to import sageattention
Failed to import sageattention
Failed to import sageattention
Failed to import sageattention
INFO:hv_train_network:Load dataset config from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_config.toml
INFO:hv_train_network:Load dataset config from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_config.toml
INFO:hv_train_network:Load dataset config from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_config.toml
INFO:hv_train_network:Load dataset config from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_config.toml
INFO:hv_train_network:Load dataset config from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_config.toml
INFO:hv_train_network:Load dataset config from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_config.toml
INFO:hv_train_network:Load dataset config from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_config.toml
INFO:hv_train_network:Load dataset config from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_config.toml
INFO:dataset.image_video_dataset:load video jsonl from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl
INFO:dataset.image_video_dataset:load video jsonl from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl
INFO:dataset.image_video_dataset:load video jsonl from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl
INFO:dataset.image_video_dataset:load video jsonl from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl
INFO:dataset.image_video_dataset:load video jsonl from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl
INFO:dataset.image_video_dataset:load video jsonl from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl
INFO:dataset.image_video_dataset:load video jsonl from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl
INFO:dataset.image_video_dataset:load video jsonl from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl
INFO:dataset.image_video_dataset:loaded 100 videos
INFO:dataset.image_video_dataset:loaded 100 videos
INFO:dataset.image_video_dataset:loaded 100 videos
INFO:dataset.image_video_dataset:loaded 100 videos
INFO:dataset.image_video_dataset:loaded 100 videos
INFO:dataset.image_video_dataset:loaded 100 videos
INFO:dataset.image_video_dataset:loaded 100 videos
INFO:dataset.config_utils:[Dataset 0]
  is_image_dataset: False
  resolution: (768, 512)
  batch_size: 1
  num_repeats: 1
  caption_extension: "None"
  enable_bucket: True
  bucket_no_upscale: False
  cache_directory: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/cache_full"
  debug_dataset: False
    video_directory: "None"
    video_jsonl_file: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl"
    control_directory: "None"
    target_frames: (1,)
    frame_extraction: full
    frame_stride: 1
    frame_sample: 1
    max_frames: 129
    source_fps: 30.0


INFO:dataset.config_utils:[Dataset 0]
  is_image_dataset: False
  resolution: (768, 512)
  batch_size: 1
  num_repeats: 1
  caption_extension: "None"
  enable_bucket: True
  bucket_no_upscale: False
  cache_directory: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/cache_full"
  debug_dataset: False
    video_directory: "None"
    video_jsonl_file: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl"
    control_directory: "None"
    target_frames: (1,)
    frame_extraction: full
    frame_stride: 1
    frame_sample: 1
    max_frames: 129
    source_fps: 30.0


INFO:dataset.config_utils:[Dataset 0]
  is_image_dataset: False
  resolution: (768, 512)
  batch_size: 1
  num_repeats: 1
  caption_extension: "None"
  enable_bucket: True
  bucket_no_upscale: False
  cache_directory: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/cache_full"
  debug_dataset: False
    video_directory: "None"
    video_jsonl_file: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl"
    control_directory: "None"
    target_frames: (1,)
    frame_extraction: full
    frame_stride: 1
    frame_sample: 1
    max_frames: 129
    source_fps: 30.0


INFO:dataset.config_utils:[Dataset 0]
  is_image_dataset: False
  resolution: (768, 512)
  batch_size: 1
  num_repeats: 1
  caption_extension: "None"
  enable_bucket: True
  bucket_no_upscale: False
  cache_directory: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/cache_full"
  debug_dataset: False
    video_directory: "None"
    video_jsonl_file: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl"
    control_directory: "None"
    target_frames: (1,)
    frame_extraction: full
    frame_stride: 1
    frame_sample: 1
    max_frames: 129
    source_fps: 30.0


INFO:dataset.config_utils:[Dataset 0]
  is_image_dataset: False
  resolution: (768, 512)
  batch_size: 1
  num_repeats: 1
  caption_extension: "None"
  enable_bucket: True
  bucket_no_upscale: False
  cache_directory: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/cache_full"
  debug_dataset: False
    video_directory: "None"
    video_jsonl_file: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl"
    control_directory: "None"
    target_frames: (1,)
    frame_extraction: full
    frame_stride: 1
    frame_sample: 1
    max_frames: 129
    source_fps: 30.0


INFO:dataset.config_utils:[Dataset 0]
  is_image_dataset: False
  resolution: (768, 512)
  batch_size: 1
  num_repeats: 1
  caption_extension: "None"
  enable_bucket: True
  bucket_no_upscale: False
  cache_directory: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/cache_full"
  debug_dataset: False
    video_directory: "None"
    video_jsonl_file: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl"
    control_directory: "None"
    target_frames: (1,)
    frame_extraction: full
    frame_stride: 1
    frame_sample: 1
    max_frames: 129
    source_fps: 30.0


INFO:dataset.config_utils:[Dataset 0]
  is_image_dataset: False
  resolution: (768, 512)
  batch_size: 1
  num_repeats: 1
  caption_extension: "None"
  enable_bucket: True
  bucket_no_upscale: False
  cache_directory: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/cache_full"
  debug_dataset: False
    video_directory: "None"
    video_jsonl_file: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl"
    control_directory: "None"
    target_frames: (1,)
    frame_extraction: full
    frame_stride: 1
    frame_sample: 1
    max_frames: 129
    source_fps: 30.0


INFO:dataset.image_video_dataset:loaded 100 videos
INFO:dataset.config_utils:[Dataset 0]
  is_image_dataset: False
  resolution: (768, 512)
  batch_size: 1
  num_repeats: 1
  caption_extension: "None"
  enable_bucket: True
  bucket_no_upscale: False
  cache_directory: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/cache_full"
  debug_dataset: False
    video_directory: "None"
    video_jsonl_file: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl"
    control_directory: "None"
    target_frames: (1,)
    frame_extraction: full
    frame_stride: 1
    frame_sample: 1
    max_frames: 129
    source_fps: 30.0


INFO:dataset.image_video_dataset:bucket: (768, 512, 129), count: 300
INFO:dataset.image_video_dataset:bucket: (768, 512, 129), count: 300
INFO:dataset.image_video_dataset:total batches: 300
INFO:dataset.image_video_dataset:bucket: (768, 512, 129), count: 300
INFO:dataset.image_video_dataset:total batches: 300
INFO:dataset.image_video_dataset:bucket: (768, 512, 129), count: 300
INFO:dataset.image_video_dataset:bucket: (768, 512, 129), count: 300
INFO:dataset.image_video_dataset:total batches: 300
INFO:dataset.image_video_dataset:bucket: (768, 512, 129), count: 300
INFO:dataset.image_video_dataset:bucket: (768, 512, 129), count: 300
INFO:dataset.image_video_dataset:total batches: 300
INFO:dataset.image_video_dataset:bucket: (768, 512, 129), count: 300
INFO:dataset.image_video_dataset:total batches: 300
INFO:dataset.image_video_dataset:total batches: 300
INFO:dataset.image_video_dataset:total batches: 300
INFO:dataset.image_video_dataset:total batches: 300
INFO:hv_train_network:preparing accelerator
INFO:hv_train_network:preparing accelerator
INFO:hv_train_network:preparing accelerator
INFO:hv_train_network:preparing accelerator
INFO:hv_train_network:preparing accelerator
INFO:hv_train_network:preparing accelerator
INFO:hv_train_network:preparing accelerator
INFO:hv_train_network:preparing accelerator
accelerator device: cuda:0
INFO:hv_train_network:DiT precision: torch.bfloat16, weight precision: torch.bfloat16
INFO:hv_train_network:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:__main__:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:frame_pack.hunyuan_video_packed:Creating HunyuanVideoTransformer3DModelPacked
INFO:frame_pack.hunyuan_video_packed:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, device=cuda:0
accelerator device: cuda:7
INFO:hv_train_network:DiT precision: torch.bfloat16, weight precision: torch.bfloat16
INFO:hv_train_network:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:__main__:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:frame_pack.hunyuan_video_packed:Creating HunyuanVideoTransformer3DModelPacked
accelerator device: cuda:1
INFO:hv_train_network:DiT precision: torch.bfloat16, weight precision: torch.bfloat16
INFO:hv_train_network:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:__main__:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:frame_pack.hunyuan_video_packed:Creating HunyuanVideoTransformer3DModelPacked
accelerator device: cuda:5
INFO:hv_train_network:DiT precision: torch.bfloat16, weight precision: torch.bfloat16
accelerator device: cuda:4
INFO:hv_train_network:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:__main__:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:hv_train_network:DiT precision: torch.bfloat16, weight precision: torch.bfloat16
INFO:hv_train_network:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:__main__:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:frame_pack.hunyuan_video_packed:Creating HunyuanVideoTransformer3DModelPacked
INFO:frame_pack.hunyuan_video_packed:Creating HunyuanVideoTransformer3DModelPacked
accelerator device: cuda:2
INFO:hv_train_network:DiT precision: torch.bfloat16, weight precision: torch.bfloat16
INFO:hv_train_network:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:__main__:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:frame_pack.hunyuan_video_packed:Creating HunyuanVideoTransformer3DModelPacked
accelerator device: cuda:3
INFO:hv_train_network:DiT precision: torch.bfloat16, weight precision: torch.bfloat16
accelerator device: cuda:6
INFO:hv_train_network:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:__main__:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:hv_train_network:DiT precision: torch.bfloat16, weight precision: torch.bfloat16
INFO:hv_train_network:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:__main__:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:frame_pack.hunyuan_video_packed:Creating HunyuanVideoTransformer3DModelPacked
INFO:frame_pack.hunyuan_video_packed:Creating HunyuanVideoTransformer3DModelPacked
INFO:frame_pack.hunyuan_video_packed:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, device=cuda:7
INFO:frame_pack.hunyuan_video_packed:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, device=cuda:1
INFO:frame_pack.hunyuan_video_packed:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, device=cuda:4
INFO:frame_pack.hunyuan_video_packed:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, device=cuda:5
INFO:frame_pack.hunyuan_video_packed:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, device=cuda:2
INFO:frame_pack.hunyuan_video_packed:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, device=cuda:3
INFO:frame_pack.hunyuan_video_packed:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, device=cuda:6
INFO:frame_pack.hunyuan_video_packed:Loaded DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, info=<All keys matched successfully>
INFO:frame_pack.hunyuan_video_packed:Loaded DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, info=<All keys matched successfully>
INFO:frame_pack.hunyuan_video_packed:Loaded DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, info=<All keys matched successfully>
INFO:frame_pack.hunyuan_video_packed:Loaded DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, info=<All keys matched successfully>
INFO:frame_pack.hunyuan_video_packed:Loaded DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, info=<All keys matched successfully>
INFO:frame_pack.hunyuan_video_packed:Loaded DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, info=<All keys matched successfully>
INFO:frame_pack.hunyuan_video_packed:Loaded DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, info=<All keys matched successfully>
INFO:networks.lora:create LoRA network. base dim (rank): 32, alpha: 1
INFO:networks.lora:neuron dropout: p=None, rank dropout: p=None, module dropout: p=None
INFO:networks.lora:create LoRA network. base dim (rank): 32, alpha: 1
INFO:networks.lora:create LoRA network. base dim (rank): 32, alpha: 1
INFO:networks.lora:create LoRA network. base dim (rank): 32, alpha: 1
INFO:networks.lora:neuron dropout: p=None, rank dropout: p=None, module dropout: p=None
INFO:networks.lora:neuron dropout: p=None, rank dropout: p=None, module dropout: p=None
INFO:networks.lora:neuron dropout: p=None, rank dropout: p=None, module dropout: p=None
INFO:networks.lora:create LoRA network. base dim (rank): 32, alpha: 1
INFO:networks.lora:neuron dropout: p=None, rank dropout: p=None, module dropout: p=None
INFO:frame_pack.hunyuan_video_packed:Loaded DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, info=<All keys matched successfully>
INFO:networks.lora:create LoRA network. base dim (rank): 32, alpha: 1
INFO:networks.lora:neuron dropout: p=None, rank dropout: p=None, module dropout: p=None
INFO:networks.lora:create LoRA network. base dim (rank): 32, alpha: 1
INFO:networks.lora:neuron dropout: p=None, rank dropout: p=None, module dropout: p=None
import network module: networks.lora_framepack
INFO:networks.lora:create LoRA network. base dim (rank): 32, alpha: 1
INFO:networks.lora:neuron dropout: p=None, rank dropout: p=None, module dropout: p=None
INFO:networks.lora:create LoRA for U-Net/DiT: 440 modules.
INFO:networks.lora:enable LoRA for U-Net: 440 modules
INFO:networks.lora:create LoRA for U-Net/DiT: 440 modules.
Gradient checkpointing enabled for HunyuanVideoTransformer3DModelPacked.
INFO:networks.lora:enable LoRA for U-Net: 440 modules
INFO:networks.lora:create LoRA for U-Net/DiT: 440 modules.
INFO:networks.lora:enable LoRA for U-Net: 440 modules
Gradient checkpointing enabled for HunyuanVideoTransformer3DModelPacked.
Gradient checkpointing enabled for HunyuanVideoTransformer3DModelPacked.
INFO:hv_train_network:use 8-bit AdamW optimizer | {}
INFO:hv_train_network:use 8-bit AdamW optimizer | {}
INFO:hv_train_network:use 8-bit AdamW optimizer | {}
INFO:networks.lora:create LoRA for U-Net/DiT: 440 modules.
INFO:networks.lora:enable LoRA for U-Net: 440 modules
Gradient checkpointing enabled for HunyuanVideoTransformer3DModelPacked.
INFO:hv_train_network:use 8-bit AdamW optimizer | {}
INFO:networks.lora:create LoRA for U-Net/DiT: 440 modules.
INFO:networks.lora:enable LoRA for U-Net: 440 modules
INFO:networks.lora:create LoRA for U-Net/DiT: 440 modules.
INFO:networks.lora:enable LoRA for U-Net: 440 modules
INFO:networks.lora:create LoRA for U-Net/DiT: 440 modules.
INFO:networks.lora:enable LoRA for U-Net: 440 modules
Gradient checkpointing enabled for HunyuanVideoTransformer3DModelPacked.
Gradient checkpointing enabled for HunyuanVideoTransformer3DModelPacked.
Gradient checkpointing enabled for HunyuanVideoTransformer3DModelPacked.
INFO:hv_train_network:use 8-bit AdamW optimizer | {}
INFO:hv_train_network:use 8-bit AdamW optimizer | {}
INFO:hv_train_network:use 8-bit AdamW optimizer | {}
INFO:networks.lora:create LoRA for U-Net/DiT: 440 modules.
INFO:networks.lora:enable LoRA for U-Net: 440 modules
Gradient checkpointing enabled for HunyuanVideoTransformer3DModelPacked.
prepare optimizer, data loader etc.
INFO:hv_train_network:use 8-bit AdamW optimizer | {}
override steps. steps for 16 epochs is / 指定エポックまでのステップ数: 608
running training / 学習開始
  num train items / 学習画像、動画数: 300
  num batches per epoch / 1epochのバッチ数: 38
  num epochs / epoch数: 16
  batch size per device / バッチサイズ: 1
  gradient accumulation steps / 勾配を合計するステップ数 = 1
  total optimization steps / 学習ステップ数: 608
INFO:hv_train_network:set DiT model name for metadata: /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:hv_train_network:set DiT model name for metadata: /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:hv_train_network:set DiT model name for metadata: /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:hv_train_network:set DiT model name for metadata: /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:hv_train_network:set DiT model name for metadata: /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:hv_train_network:set DiT model name for metadata: /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:hv_train_network:set DiT model name for metadata: /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:hv_train_network:set DiT model name for metadata: /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:hv_train_network:set VAE model name for metadata: /media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/vae/diffusion_pytorch_model.safetensors
INFO:hv_train_network:set VAE model name for metadata: /media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/vae/diffusion_pytorch_model.safetensors
INFO:hv_train_network:set VAE model name for metadata: /media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/vae/diffusion_pytorch_model.safetensors
INFO:hv_train_network:set VAE model name for metadata: /media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/vae/diffusion_pytorch_model.safetensors
INFO:hv_train_network:set VAE model name for metadata: /media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/vae/diffusion_pytorch_model.safetensors
INFO:hv_train_network:set VAE model name for metadata: /media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/vae/diffusion_pytorch_model.safetensors
INFO:hv_train_network:set VAE model name for metadata: /media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/vae/diffusion_pytorch_model.safetensors
INFO:hv_train_network:set VAE model name for metadata: /media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/vae/diffusion_pytorch_model.safetensors
INFO:hv_train_network:DiT dtype: torch.bfloat16, device: cuda:2
INFO:hv_train_network:DiT dtype: torch.bfloat16, device: cuda:5
INFO:hv_train_network:DiT dtype: torch.bfloat16, device: cuda:1
INFO:hv_train_network:DiT dtype: torch.bfloat16, device: cuda:3
INFO:hv_train_network:DiT dtype: torch.bfloat16, device: cuda:6

steps:   0%|          | 0/608 [00:00<?, ?it/s]INFO:hv_train_network:DiT dtype: torch.bfloat16, device: cuda:4
INFO:hv_train_network:DiT dtype: torch.bfloat16, device: cuda:7
INFO:hv_train_network:DiT dtype: torch.bfloat16, device: cuda:0

epoch 1/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1

steps:   0%|          | 1/608 [00:15<2:32:54, 15.11s/it]
steps:   0%|          | 1/608 [00:15<2:32:54, 15.11s/it, avr_loss=0.029]
steps:   0%|          | 2/608 [00:27<2:20:41, 13.93s/it, avr_loss=0.029]
steps:   0%|          | 2/608 [00:27<2:20:41, 13.93s/it, avr_loss=0.0275]
steps:   0%|          | 3/608 [00:40<2:16:36, 13.55s/it, avr_loss=0.0275]
steps:   0%|          | 3/608 [00:40<2:16:36, 13.55s/it, avr_loss=0.0276]
steps:   1%|          | 4/608 [00:53<2:14:38, 13.38s/it, avr_loss=0.0276]
steps:   1%|          | 4/608 [00:53<2:14:38, 13.38s/it, avr_loss=0.028] 
steps:   1%|          | 5/608 [01:06<2:13:08, 13.25s/it, avr_loss=0.028]
steps:   1%|          | 5/608 [01:06<2:13:08, 13.25s/it, avr_loss=0.0267]
steps:   1%|          | 6/608 [01:18<2:11:59, 13.16s/it, avr_loss=0.0267]
steps:   1%|          | 6/608 [01:18<2:11:59, 13.16s/it, avr_loss=0.0261]
steps:   1%|          | 7/608 [01:31<2:11:08, 13.09s/it, avr_loss=0.0261]
steps:   1%|          | 7/608 [01:31<2:11:08, 13.09s/it, avr_loss=0.0256]
steps:   1%|▏         | 8/608 [01:44<2:10:28, 13.05s/it, avr_loss=0.0256]
steps:   1%|▏         | 8/608 [01:44<2:10:28, 13.05s/it, avr_loss=0.0258]
steps:   1%|▏         | 9/608 [01:57<2:09:54, 13.01s/it, avr_loss=0.0258]
steps:   1%|▏         | 9/608 [01:57<2:09:54, 13.01s/it, avr_loss=0.026] 
steps:   2%|▏         | 10/608 [02:09<2:09:25, 12.99s/it, avr_loss=0.026]
steps:   2%|▏         | 10/608 [02:09<2:09:25, 12.99s/it, avr_loss=0.0259]
steps:   2%|▏         | 11/608 [02:22<2:08:59, 12.96s/it, avr_loss=0.0259]
steps:   2%|▏         | 11/608 [02:22<2:08:59, 12.96s/it, avr_loss=0.0254]
steps:   2%|▏         | 12/608 [02:35<2:08:38, 12.95s/it, avr_loss=0.0254]
steps:   2%|▏         | 12/608 [02:35<2:08:38, 12.95s/it, avr_loss=0.0263]
steps:   2%|▏         | 13/608 [02:48<2:08:18, 12.94s/it, avr_loss=0.0263]
steps:   2%|▏         | 13/608 [02:48<2:08:18, 12.94s/it, avr_loss=0.0262]
steps:   2%|▏         | 14/608 [03:01<2:07:59, 12.93s/it, avr_loss=0.0262]
steps:   2%|▏         | 14/608 [03:01<2:07:59, 12.93s/it, avr_loss=0.0257]
steps:   2%|▏         | 15/608 [03:13<2:07:40, 12.92s/it, avr_loss=0.0257]
steps:   2%|▏         | 15/608 [03:13<2:07:40, 12.92s/it, avr_loss=0.0258]
steps:   3%|▎         | 16/608 [03:26<2:07:20, 12.91s/it, avr_loss=0.0258]
steps:   3%|▎         | 16/608 [03:26<2:07:20, 12.91s/it, avr_loss=0.0254]
steps:   3%|▎         | 17/608 [03:39<2:07:00, 12.89s/it, avr_loss=0.0254]
steps:   3%|▎         | 17/608 [03:39<2:07:00, 12.89s/it, avr_loss=0.0253]
steps:   3%|▎         | 18/608 [03:51<2:06:42, 12.88s/it, avr_loss=0.0253]
steps:   3%|▎         | 18/608 [03:51<2:06:42, 12.88s/it, avr_loss=0.0251]
steps:   3%|▎         | 19/608 [04:04<2:06:24, 12.88s/it, avr_loss=0.0251]
steps:   3%|▎         | 19/608 [04:04<2:06:24, 12.88s/it, avr_loss=0.026] 
steps:   3%|▎         | 20/608 [04:17<2:06:08, 12.87s/it, avr_loss=0.026]
steps:   3%|▎         | 20/608 [04:17<2:06:08, 12.87s/it, avr_loss=0.0258]
steps:   3%|▎         | 21/608 [04:30<2:05:53, 12.87s/it, avr_loss=0.0258]
steps:   3%|▎         | 21/608 [04:30<2:05:53, 12.87s/it, avr_loss=0.0277]
steps:   4%|▎         | 22/608 [04:42<2:05:37, 12.86s/it, avr_loss=0.0277]
steps:   4%|▎         | 22/608 [04:42<2:05:37, 12.86s/it, avr_loss=0.0274]
steps:   4%|▍         | 23/608 [04:55<2:05:22, 12.86s/it, avr_loss=0.0274]
steps:   4%|▍         | 23/608 [04:55<2:05:22, 12.86s/it, avr_loss=0.0278]
steps:   4%|▍         | 24/608 [05:08<2:05:08, 12.86s/it, avr_loss=0.0278]
steps:   4%|▍         | 24/608 [05:08<2:05:08, 12.86s/it, avr_loss=0.0277]
steps:   4%|▍         | 25/608 [05:21<2:04:54, 12.85s/it, avr_loss=0.0277]
steps:   4%|▍         | 25/608 [05:21<2:04:54, 12.85s/it, avr_loss=0.0274]
steps:   4%|▍         | 26/608 [05:34<2:04:40, 12.85s/it, avr_loss=0.0274]
steps:   4%|▍         | 26/608 [05:34<2:04:40, 12.85s/it, avr_loss=0.0272]
steps:   4%|▍         | 27/608 [05:46<2:04:26, 12.85s/it, avr_loss=0.0272]
steps:   4%|▍         | 27/608 [05:46<2:04:26, 12.85s/it, avr_loss=0.029] 
steps:   5%|▍         | 28/608 [05:59<2:04:12, 12.85s/it, avr_loss=0.029]
steps:   5%|▍         | 28/608 [05:59<2:04:12, 12.85s/it, avr_loss=0.0289]
steps:   5%|▍         | 29/608 [06:12<2:03:58, 12.85s/it, avr_loss=0.0289]
steps:   5%|▍         | 29/608 [06:12<2:03:58, 12.85s/it, avr_loss=0.0289]
steps:   5%|▍         | 30/608 [06:25<2:03:44, 12.85s/it, avr_loss=0.0289]
steps:   5%|▍         | 30/608 [06:25<2:03:44, 12.85s/it, avr_loss=0.0293]
steps:   5%|▌         | 31/608 [06:38<2:03:29, 12.84s/it, avr_loss=0.0293]
steps:   5%|▌         | 31/608 [06:38<2:03:29, 12.84s/it, avr_loss=0.0293]
steps:   5%|▌         | 32/608 [06:50<2:03:14, 12.84s/it, avr_loss=0.0293]
steps:   5%|▌         | 32/608 [06:50<2:03:14, 12.84s/it, avr_loss=0.0294]
steps:   5%|▌         | 33/608 [07:03<2:02:59, 12.83s/it, avr_loss=0.0294]
steps:   5%|▌         | 33/608 [07:03<2:02:59, 12.83s/it, avr_loss=0.0293]
steps:   6%|▌         | 34/608 [07:16<2:02:44, 12.83s/it, avr_loss=0.0293]
steps:   6%|▌         | 34/608 [07:16<2:02:44, 12.83s/it, avr_loss=0.0293]
steps:   6%|▌         | 35/608 [07:28<2:02:29, 12.83s/it, avr_loss=0.0293]
steps:   6%|▌         | 35/608 [07:28<2:02:29, 12.83s/it, avr_loss=0.029] 
steps:   6%|▌         | 36/608 [07:41<2:02:16, 12.83s/it, avr_loss=0.029]
steps:   6%|▌         | 36/608 [07:41<2:02:16, 12.83s/it, avr_loss=0.0288]
steps:   6%|▌         | 37/608 [07:54<2:02:02, 12.82s/it, avr_loss=0.0288]
steps:   6%|▌         | 37/608 [07:54<2:02:02, 12.82s/it, avr_loss=0.0287]
steps:   6%|▋         | 38/608 [08:07<2:01:49, 12.82s/it, avr_loss=0.0287]
steps:   6%|▋         | 38/608 [08:07<2:01:49, 12.82s/it, avr_loss=0.0284]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/block_handover_D435_full/train_lora-000001.safetensors

epoch 2/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2

steps:   6%|▋         | 39/608 [08:21<2:02:03, 12.87s/it, avr_loss=0.0284]
steps:   6%|▋         | 39/608 [08:21<2:02:03, 12.87s/it, avr_loss=0.0281]
steps:   7%|▋         | 40/608 [08:34<2:01:49, 12.87s/it, avr_loss=0.0281]
steps:   7%|▋         | 40/608 [08:34<2:01:49, 12.87s/it, avr_loss=0.028] 
steps:   7%|▋         | 41/608 [08:47<2:01:34, 12.87s/it, avr_loss=0.028]
steps:   7%|▋         | 41/608 [08:47<2:01:34, 12.87s/it, avr_loss=0.0277]
steps:   7%|▋         | 42/608 [09:00<2:01:20, 12.86s/it, avr_loss=0.0277]
steps:   7%|▋         | 42/608 [09:00<2:01:20, 12.86s/it, avr_loss=0.0277]
steps:   7%|▋         | 43/608 [09:12<2:01:05, 12.86s/it, avr_loss=0.0277]
steps:   7%|▋         | 43/608 [09:12<2:01:05, 12.86s/it, avr_loss=0.028] 
steps:   7%|▋         | 44/608 [09:25<2:00:51, 12.86s/it, avr_loss=0.028]
steps:   7%|▋         | 44/608 [09:25<2:00:51, 12.86s/it, avr_loss=0.0279]
steps:   7%|▋         | 45/608 [09:38<2:00:37, 12.85s/it, avr_loss=0.0279]
steps:   7%|▋         | 45/608 [09:38<2:00:37, 12.85s/it, avr_loss=0.0279]
steps:   8%|▊         | 46/608 [09:51<2:00:23, 12.85s/it, avr_loss=0.0279]
steps:   8%|▊         | 46/608 [09:51<2:00:23, 12.85s/it, avr_loss=0.0291]
steps:   8%|▊         | 47/608 [10:04<2:00:10, 12.85s/it, avr_loss=0.0291]
steps:   8%|▊         | 47/608 [10:04<2:00:10, 12.85s/it, avr_loss=0.0297]
steps:   8%|▊         | 48/608 [10:16<1:59:57, 12.85s/it, avr_loss=0.0297]
steps:   8%|▊         | 48/608 [10:16<1:59:57, 12.85s/it, avr_loss=0.0297]
steps:   8%|▊         | 49/608 [10:29<1:59:44, 12.85s/it, avr_loss=0.0297]
steps:   8%|▊         | 49/608 [10:29<1:59:44, 12.85s/it, avr_loss=0.0297]
steps:   8%|▊         | 50/608 [10:42<1:59:30, 12.85s/it, avr_loss=0.0297]
steps:   8%|▊         | 50/608 [10:42<1:59:30, 12.85s/it, avr_loss=0.0293]
steps:   8%|▊         | 51/608 [10:55<1:59:17, 12.85s/it, avr_loss=0.0293]
steps:   8%|▊         | 51/608 [10:55<1:59:17, 12.85s/it, avr_loss=0.0292]
steps:   9%|▊         | 52/608 [11:08<1:59:04, 12.85s/it, avr_loss=0.0292]
steps:   9%|▊         | 52/608 [11:08<1:59:04, 12.85s/it, avr_loss=0.0292]
steps:   9%|▊         | 53/608 [11:20<1:58:50, 12.85s/it, avr_loss=0.0292]
steps:   9%|▊         | 53/608 [11:20<1:58:50, 12.85s/it, avr_loss=0.0291]
steps:   9%|▉         | 54/608 [11:33<1:58:37, 12.85s/it, avr_loss=0.0291]
steps:   9%|▉         | 54/608 [11:33<1:58:37, 12.85s/it, avr_loss=0.0292]
steps:   9%|▉         | 55/608 [11:46<1:58:24, 12.85s/it, avr_loss=0.0292]
steps:   9%|▉         | 55/608 [11:46<1:58:24, 12.85s/it, avr_loss=0.0293]
steps:   9%|▉         | 56/608 [11:59<1:58:11, 12.85s/it, avr_loss=0.0293]
steps:   9%|▉         | 56/608 [11:59<1:58:11, 12.85s/it, avr_loss=0.0293]
steps:   9%|▉         | 57/608 [12:12<1:57:58, 12.85s/it, avr_loss=0.0293]
steps:   9%|▉         | 57/608 [12:12<1:57:58, 12.85s/it, avr_loss=0.0288]
steps:  10%|▉         | 58/608 [12:25<1:57:45, 12.85s/it, avr_loss=0.0288]
steps:  10%|▉         | 58/608 [12:25<1:57:45, 12.85s/it, avr_loss=0.029] 
steps:  10%|▉         | 59/608 [12:37<1:57:32, 12.85s/it, avr_loss=0.029]
steps:  10%|▉         | 59/608 [12:37<1:57:32, 12.85s/it, avr_loss=0.028]
steps:  10%|▉         | 60/608 [12:50<1:57:18, 12.84s/it, avr_loss=0.028]
steps:  10%|▉         | 60/608 [12:50<1:57:18, 12.84s/it, avr_loss=0.0283]
steps:  10%|█         | 61/608 [13:03<1:57:04, 12.84s/it, avr_loss=0.0283]
steps:  10%|█         | 61/608 [13:03<1:57:04, 12.84s/it, avr_loss=0.0277]
steps:  10%|█         | 62/608 [13:16<1:56:50, 12.84s/it, avr_loss=0.0277]
steps:  10%|█         | 62/608 [13:16<1:56:50, 12.84s/it, avr_loss=0.0277]
steps:  10%|█         | 63/608 [13:28<1:56:36, 12.84s/it, avr_loss=0.0277]
steps:  10%|█         | 63/608 [13:28<1:56:36, 12.84s/it, avr_loss=0.0278]
steps:  11%|█         | 64/608 [13:41<1:56:23, 12.84s/it, avr_loss=0.0278]
steps:  11%|█         | 64/608 [13:41<1:56:23, 12.84s/it, avr_loss=0.0281]
steps:  11%|█         | 65/608 [13:54<1:56:09, 12.84s/it, avr_loss=0.0281]
steps:  11%|█         | 65/608 [13:54<1:56:09, 12.84s/it, avr_loss=0.0265]
steps:  11%|█         | 66/608 [14:07<1:55:56, 12.83s/it, avr_loss=0.0265]
steps:  11%|█         | 66/608 [14:07<1:55:56, 12.83s/it, avr_loss=0.0264]
steps:  11%|█         | 67/608 [14:19<1:55:43, 12.83s/it, avr_loss=0.0264]
steps:  11%|█         | 67/608 [14:19<1:55:43, 12.83s/it, avr_loss=0.0262]
steps:  11%|█         | 68/608 [14:32<1:55:30, 12.83s/it, avr_loss=0.0262]
steps:  11%|█         | 68/608 [14:32<1:55:30, 12.83s/it, avr_loss=0.0257]
steps:  11%|█▏        | 69/608 [14:45<1:55:17, 12.83s/it, avr_loss=0.0257]
steps:  11%|█▏        | 69/608 [14:45<1:55:17, 12.83s/it, avr_loss=0.0255]
steps:  12%|█▏        | 70/608 [14:58<1:55:04, 12.83s/it, avr_loss=0.0255]
steps:  12%|█▏        | 70/608 [14:58<1:55:04, 12.83s/it, avr_loss=0.0253]
steps:  12%|█▏        | 71/608 [15:11<1:54:51, 12.83s/it, avr_loss=0.0253]
steps:  12%|█▏        | 71/608 [15:11<1:54:51, 12.83s/it, avr_loss=0.0253]
steps:  12%|█▏        | 72/608 [15:23<1:54:38, 12.83s/it, avr_loss=0.0253]
steps:  12%|█▏        | 72/608 [15:23<1:54:38, 12.83s/it, avr_loss=0.025] 
steps:  12%|█▏        | 73/608 [15:36<1:54:24, 12.83s/it, avr_loss=0.025]
steps:  12%|█▏        | 73/608 [15:36<1:54:24, 12.83s/it, avr_loss=0.0249]
steps:  12%|█▏        | 74/608 [15:49<1:54:10, 12.83s/it, avr_loss=0.0249]
steps:  12%|█▏        | 74/608 [15:49<1:54:10, 12.83s/it, avr_loss=0.0248]
steps:  12%|█▏        | 75/608 [16:02<1:53:57, 12.83s/it, avr_loss=0.0248]
steps:  12%|█▏        | 75/608 [16:02<1:53:57, 12.83s/it, avr_loss=0.0248]
steps:  12%|█▎        | 76/608 [16:14<1:53:43, 12.83s/it, avr_loss=0.0248]
steps:  12%|█▎        | 76/608 [16:14<1:53:43, 12.83s/it, avr_loss=0.0248]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/block_handover_D435_full/train_lora-000002.safetensors

epoch 3/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3

steps:  13%|█▎        | 77/608 [16:29<1:53:42, 12.85s/it, avr_loss=0.0248]
steps:  13%|█▎        | 77/608 [16:29<1:53:42, 12.85s/it, avr_loss=0.0248]
steps:  13%|█▎        | 78/608 [16:42<1:53:29, 12.85s/it, avr_loss=0.0248]
steps:  13%|█▎        | 78/608 [16:42<1:53:29, 12.85s/it, avr_loss=0.0257]
steps:  13%|█▎        | 79/608 [16:54<1:53:16, 12.85s/it, avr_loss=0.0257]
steps:  13%|█▎        | 79/608 [16:54<1:53:16, 12.85s/it, avr_loss=0.0257]
steps:  13%|█▎        | 80/608 [17:07<1:53:02, 12.85s/it, avr_loss=0.0257]
steps:  13%|█▎        | 80/608 [17:07<1:53:02, 12.85s/it, avr_loss=0.0274]
steps:  13%|█▎        | 81/608 [17:20<1:52:48, 12.84s/it, avr_loss=0.0274]
steps:  13%|█▎        | 81/608 [17:20<1:52:48, 12.84s/it, avr_loss=0.0272]
steps:  13%|█▎        | 82/608 [17:33<1:52:35, 12.84s/it, avr_loss=0.0272]
steps:  13%|█▎        | 82/608 [17:33<1:52:35, 12.84s/it, avr_loss=0.0276]
steps:  14%|█▎        | 83/608 [17:45<1:52:21, 12.84s/it, avr_loss=0.0276]
steps:  14%|█▎        | 83/608 [17:45<1:52:21, 12.84s/it, avr_loss=0.0276]
steps:  14%|█▍        | 84/608 [17:58<1:52:08, 12.84s/it, avr_loss=0.0276]
steps:  14%|█▍        | 84/608 [17:58<1:52:08, 12.84s/it, avr_loss=0.0285]
steps:  14%|█▍        | 85/608 [18:11<1:51:55, 12.84s/it, avr_loss=0.0285]
steps:  14%|█▍        | 85/608 [18:11<1:51:55, 12.84s/it, avr_loss=0.0278]
steps:  14%|█▍        | 86/608 [18:24<1:51:42, 12.84s/it, avr_loss=0.0278]
steps:  14%|█▍        | 86/608 [18:24<1:51:42, 12.84s/it, avr_loss=0.0278]
steps:  14%|█▍        | 87/608 [18:37<1:51:29, 12.84s/it, avr_loss=0.0278]
steps:  14%|█▍        | 87/608 [18:37<1:51:29, 12.84s/it, avr_loss=0.028] 
steps:  14%|█▍        | 88/608 [18:49<1:51:17, 12.84s/it, avr_loss=0.028]
steps:  14%|█▍        | 88/608 [18:49<1:51:17, 12.84s/it, avr_loss=0.0281]
steps:  15%|█▍        | 89/608 [19:02<1:51:03, 12.84s/it, avr_loss=0.0281]
steps:  15%|█▍        | 89/608 [19:02<1:51:03, 12.84s/it, avr_loss=0.0281]
steps:  15%|█▍        | 90/608 [19:15<1:50:50, 12.84s/it, avr_loss=0.0281]
steps:  15%|█▍        | 90/608 [19:15<1:50:50, 12.84s/it, avr_loss=0.0295]
steps:  15%|█▍        | 91/608 [19:28<1:50:36, 12.84s/it, avr_loss=0.0295]
steps:  15%|█▍        | 91/608 [19:28<1:50:36, 12.84s/it, avr_loss=0.0294]
steps:  15%|█▌        | 92/608 [19:40<1:50:23, 12.84s/it, avr_loss=0.0294]
steps:  15%|█▌        | 92/608 [19:40<1:50:23, 12.84s/it, avr_loss=0.0293]
steps:  15%|█▌        | 93/608 [19:53<1:50:10, 12.84s/it, avr_loss=0.0293]
steps:  15%|█▌        | 93/608 [19:53<1:50:10, 12.84s/it, avr_loss=0.0296]
steps:  15%|█▌        | 94/608 [20:06<1:49:57, 12.84s/it, avr_loss=0.0296]
steps:  15%|█▌        | 94/608 [20:06<1:49:57, 12.84s/it, avr_loss=0.0312]
steps:  16%|█▌        | 95/608 [20:19<1:49:44, 12.84s/it, avr_loss=0.0312]
steps:  16%|█▌        | 95/608 [20:19<1:49:44, 12.84s/it, avr_loss=0.0316]
steps:  16%|█▌        | 96/608 [20:32<1:49:31, 12.84s/it, avr_loss=0.0316]
steps:  16%|█▌        | 96/608 [20:32<1:49:31, 12.84s/it, avr_loss=0.0314]
steps:  16%|█▌        | 97/608 [20:44<1:49:18, 12.83s/it, avr_loss=0.0314]
steps:  16%|█▌        | 97/608 [20:44<1:49:18, 12.83s/it, avr_loss=0.0312]
steps:  16%|█▌        | 98/608 [20:57<1:49:04, 12.83s/it, avr_loss=0.0312]
steps:  16%|█▌        | 98/608 [20:57<1:49:04, 12.83s/it, avr_loss=0.0312]
steps:  16%|█▋        | 99/608 [21:10<1:48:51, 12.83s/it, avr_loss=0.0312]
steps:  16%|█▋        | 99/608 [21:10<1:48:51, 12.83s/it, avr_loss=0.0312]
steps:  16%|█▋        | 100/608 [21:23<1:48:38, 12.83s/it, avr_loss=0.0312]
steps:  16%|█▋        | 100/608 [21:23<1:48:38, 12.83s/it, avr_loss=0.0312]
steps:  17%|█▋        | 101/608 [21:35<1:48:24, 12.83s/it, avr_loss=0.0312]
steps:  17%|█▋        | 101/608 [21:35<1:48:24, 12.83s/it, avr_loss=0.0309]
steps:  17%|█▋        | 102/608 [21:48<1:48:11, 12.83s/it, avr_loss=0.0309]
steps:  17%|█▋        | 102/608 [21:48<1:48:11, 12.83s/it, avr_loss=0.0306]
steps:  17%|█▋        | 103/608 [22:01<1:47:59, 12.83s/it, avr_loss=0.0306]
steps:  17%|█▋        | 103/608 [22:01<1:47:59, 12.83s/it, avr_loss=0.0312]
steps:  17%|█▋        | 104/608 [22:14<1:47:45, 12.83s/it, avr_loss=0.0312]
steps:  17%|█▋        | 104/608 [22:14<1:47:45, 12.83s/it, avr_loss=0.0313]
steps:  17%|█▋        | 105/608 [22:26<1:47:32, 12.83s/it, avr_loss=0.0313]
steps:  17%|█▋        | 105/608 [22:26<1:47:32, 12.83s/it, avr_loss=0.0314]
steps:  17%|█▋        | 106/608 [22:39<1:47:19, 12.83s/it, avr_loss=0.0314]
steps:  17%|█▋        | 106/608 [22:39<1:47:19, 12.83s/it, avr_loss=0.0314]
steps:  18%|█▊        | 107/608 [22:52<1:47:05, 12.83s/it, avr_loss=0.0314]
steps:  18%|█▊        | 107/608 [22:52<1:47:05, 12.83s/it, avr_loss=0.0319]
steps:  18%|█▊        | 108/608 [23:05<1:46:52, 12.83s/it, avr_loss=0.0319]
steps:  18%|█▊        | 108/608 [23:05<1:46:52, 12.83s/it, avr_loss=0.0317]
steps:  18%|█▊        | 109/608 [23:17<1:46:39, 12.83s/it, avr_loss=0.0317]
steps:  18%|█▊        | 109/608 [23:17<1:46:39, 12.83s/it, avr_loss=0.0317]
steps:  18%|█▊        | 110/608 [23:30<1:46:27, 12.83s/it, avr_loss=0.0317]
steps:  18%|█▊        | 110/608 [23:30<1:46:27, 12.83s/it, avr_loss=0.0318]
steps:  18%|█▊        | 111/608 [23:43<1:46:14, 12.83s/it, avr_loss=0.0318]
steps:  18%|█▊        | 111/608 [23:43<1:46:14, 12.83s/it, avr_loss=0.0319]
steps:  18%|█▊        | 112/608 [23:56<1:46:01, 12.83s/it, avr_loss=0.0319]
steps:  18%|█▊        | 112/608 [23:56<1:46:01, 12.83s/it, avr_loss=0.0323]
steps:  19%|█▊        | 113/608 [24:09<1:45:48, 12.82s/it, avr_loss=0.0323]
steps:  19%|█▊        | 113/608 [24:09<1:45:48, 12.82s/it, avr_loss=0.0327]
steps:  19%|█▉        | 114/608 [24:22<1:45:35, 12.82s/it, avr_loss=0.0327]
steps:  19%|█▉        | 114/608 [24:22<1:45:35, 12.82s/it, avr_loss=0.0326]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/block_handover_D435_full/train_lora-000003.safetensors

epoch 4/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4

steps:  19%|█▉        | 115/608 [24:36<1:45:29, 12.84s/it, avr_loss=0.0326]
steps:  19%|█▉        | 115/608 [24:36<1:45:29, 12.84s/it, avr_loss=0.0334]
steps:  19%|█▉        | 116/608 [24:49<1:45:16, 12.84s/it, avr_loss=0.0334]
steps:  19%|█▉        | 116/608 [24:49<1:45:16, 12.84s/it, avr_loss=0.0325]
steps:  19%|█▉        | 117/608 [25:02<1:45:03, 12.84s/it, avr_loss=0.0325]
steps:  19%|█▉        | 117/608 [25:02<1:45:03, 12.84s/it, avr_loss=0.0326]
steps:  19%|█▉        | 118/608 [25:14<1:44:50, 12.84s/it, avr_loss=0.0326]
steps:  19%|█▉        | 118/608 [25:14<1:44:50, 12.84s/it, avr_loss=0.0309]
steps:  20%|█▉        | 119/608 [25:27<1:44:37, 12.84s/it, avr_loss=0.0309]
steps:  20%|█▉        | 119/608 [25:27<1:44:37, 12.84s/it, avr_loss=0.0311]
steps:  20%|█▉        | 120/608 [25:40<1:44:23, 12.84s/it, avr_loss=0.0311]
steps:  20%|█▉        | 120/608 [25:40<1:44:23, 12.84s/it, avr_loss=0.0307]
steps:  20%|█▉        | 121/608 [25:53<1:44:10, 12.84s/it, avr_loss=0.0307]
steps:  20%|█▉        | 121/608 [25:53<1:44:10, 12.84s/it, avr_loss=0.0311]
steps:  20%|██        | 122/608 [26:06<1:43:58, 12.84s/it, avr_loss=0.0311]
steps:  20%|██        | 122/608 [26:06<1:43:58, 12.84s/it, avr_loss=0.0298]
steps:  20%|██        | 123/608 [26:19<1:43:46, 12.84s/it, avr_loss=0.0298]
steps:  20%|██        | 123/608 [26:19<1:43:46, 12.84s/it, avr_loss=0.0299]
steps:  20%|██        | 124/608 [26:34<1:43:42, 12.86s/it, avr_loss=0.0299]
steps:  20%|██        | 124/608 [26:34<1:43:42, 12.86s/it, avr_loss=0.0297]
steps:  21%|██        | 125/608 [26:49<1:43:39, 12.88s/it, avr_loss=0.0297]
steps:  21%|██        | 125/608 [26:49<1:43:39, 12.88s/it, avr_loss=0.0293]
steps:  21%|██        | 126/608 [27:09<1:43:55, 12.94s/it, avr_loss=0.0293]
steps:  21%|██        | 126/608 [27:09<1:43:55, 12.94s/it, avr_loss=0.0292]
steps:  21%|██        | 127/608 [27:25<1:43:51, 12.96s/it, avr_loss=0.0292]
steps:  21%|██        | 127/608 [27:25<1:43:51, 12.96s/it, avr_loss=0.0295]
steps:  21%|██        | 128/608 [27:45<1:44:07, 13.01s/it, avr_loss=0.0295]
steps:  21%|██        | 128/608 [27:45<1:44:07, 13.01s/it, avr_loss=0.0283]
steps:  21%|██        | 129/608 [28:01<1:44:02, 13.03s/it, avr_loss=0.0283]
steps:  21%|██        | 129/608 [28:01<1:44:02, 13.03s/it, avr_loss=0.0284]
steps:  21%|██▏       | 130/608 [28:18<1:44:06, 13.07s/it, avr_loss=0.0284]
steps:  21%|██▏       | 130/608 [28:18<1:44:06, 13.07s/it, avr_loss=0.0284]
steps:  22%|██▏       | 131/608 [28:36<1:44:11, 13.11s/it, avr_loss=0.0284]
steps:  22%|██▏       | 131/608 [28:36<1:44:11, 13.11s/it, avr_loss=0.0279]
steps:  22%|██▏       | 132/608 [28:52<1:44:06, 13.12s/it, avr_loss=0.0279]
steps:  22%|██▏       | 132/608 [28:52<1:44:06, 13.12s/it, avr_loss=0.0262]
steps:  22%|██▏       | 133/608 [29:13<1:44:21, 13.18s/it, avr_loss=0.0262]
steps:  22%|██▏       | 133/608 [29:13<1:44:21, 13.18s/it, avr_loss=0.0257]
steps:  22%|██▏       | 134/608 [29:28<1:44:15, 13.20s/it, avr_loss=0.0257]
steps:  22%|██▏       | 134/608 [29:28<1:44:15, 13.20s/it, avr_loss=0.0262]
steps:  22%|██▏       | 135/608 [29:49<1:44:28, 13.25s/it, avr_loss=0.0262]
steps:  22%|██▏       | 135/608 [29:49<1:44:28, 13.25s/it, avr_loss=0.026] 
steps:  22%|██▏       | 136/608 [30:04<1:44:22, 13.27s/it, avr_loss=0.026]
steps:  22%|██▏       | 136/608 [30:04<1:44:22, 13.27s/it, avr_loss=0.0256]
steps:  23%|██▎       | 137/608 [30:21<1:44:22, 13.30s/it, avr_loss=0.0256]
steps:  23%|██▎       | 137/608 [30:21<1:44:22, 13.30s/it, avr_loss=0.0256]
steps:  23%|██▎       | 138/608 [30:40<1:44:28, 13.34s/it, avr_loss=0.0256]
steps:  23%|██▎       | 138/608 [30:40<1:44:28, 13.34s/it, avr_loss=0.0257]
steps:  23%|██▎       | 139/608 [30:56<1:44:22, 13.35s/it, avr_loss=0.0257]
steps:  23%|██▎       | 139/608 [30:56<1:44:22, 13.35s/it, avr_loss=0.0257]
steps:  23%|██▎       | 140/608 [31:16<1:44:34, 13.41s/it, avr_loss=0.0257]
steps:  23%|██▎       | 140/608 [31:16<1:44:34, 13.41s/it, avr_loss=0.0257]
steps:  23%|██▎       | 141/608 [31:32<1:44:27, 13.42s/it, avr_loss=0.0257]
steps:  23%|██▎       | 141/608 [31:32<1:44:27, 13.42s/it, avr_loss=0.025] 
steps:  23%|██▎       | 142/608 [31:51<1:44:32, 13.46s/it, avr_loss=0.025]
steps:  23%|██▎       | 142/608 [31:51<1:44:32, 13.46s/it, avr_loss=0.0248]
steps:  24%|██▎       | 143/608 [32:08<1:44:30, 13.49s/it, avr_loss=0.0248]
steps:  24%|██▎       | 143/608 [32:08<1:44:30, 13.49s/it, avr_loss=0.0245]
steps:  24%|██▎       | 144/608 [32:23<1:44:23, 13.50s/it, avr_loss=0.0245]
steps:  24%|██▎       | 144/608 [32:23<1:44:23, 13.50s/it, avr_loss=0.0244]
steps:  24%|██▍       | 145/608 [32:44<1:44:33, 13.55s/it, avr_loss=0.0244]
steps:  24%|██▍       | 145/608 [32:44<1:44:33, 13.55s/it, avr_loss=0.024] 
steps:  24%|██▍       | 146/608 [33:00<1:44:25, 13.56s/it, avr_loss=0.024]
steps:  24%|██▍       | 146/608 [33:00<1:44:25, 13.56s/it, avr_loss=0.0243]
steps:  24%|██▍       | 147/608 [33:20<1:44:35, 13.61s/it, avr_loss=0.0243]
steps:  24%|██▍       | 147/608 [33:20<1:44:35, 13.61s/it, avr_loss=0.0241]
steps:  24%|██▍       | 148/608 [33:36<1:44:26, 13.62s/it, avr_loss=0.0241]
steps:  24%|██▍       | 148/608 [33:36<1:44:26, 13.62s/it, avr_loss=0.0239]
steps:  25%|██▍       | 149/608 [33:51<1:44:18, 13.64s/it, avr_loss=0.0239]
steps:  25%|██▍       | 149/608 [33:51<1:44:18, 13.64s/it, avr_loss=0.0239]
steps:  25%|██▍       | 150/608 [34:12<1:44:26, 13.68s/it, avr_loss=0.0239]
steps:  25%|██▍       | 150/608 [34:12<1:44:26, 13.68s/it, avr_loss=0.0241]
steps:  25%|██▍       | 151/608 [34:27<1:44:18, 13.69s/it, avr_loss=0.0241]
steps:  25%|██▍       | 151/608 [34:27<1:44:18, 13.69s/it, avr_loss=0.0237]
steps:  25%|██▌       | 152/608 [34:48<1:44:25, 13.74s/it, avr_loss=0.0237]
steps:  25%|██▌       | 152/608 [34:48<1:44:25, 13.74s/it, avr_loss=0.0238]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/block_handover_D435_full/train_lora-000004.safetensors

epoch 5/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5

steps:  25%|██▌       | 153/608 [35:11<1:44:38, 13.80s/it, avr_loss=0.0238]
steps:  25%|██▌       | 153/608 [35:11<1:44:38, 13.80s/it, avr_loss=0.0231]
steps:  25%|██▌       | 154/608 [35:26<1:44:29, 13.81s/it, avr_loss=0.0231]
steps:  25%|██▌       | 154/608 [35:26<1:44:29, 13.81s/it, avr_loss=0.0231]
steps:  25%|██▌       | 155/608 [35:47<1:44:36, 13.86s/it, avr_loss=0.0231]
steps:  25%|██▌       | 155/608 [35:47<1:44:36, 13.86s/it, avr_loss=0.0233]
steps:  26%|██▌       | 156/608 [36:02<1:44:26, 13.86s/it, avr_loss=0.0233]
steps:  26%|██▌       | 156/608 [36:02<1:44:26, 13.86s/it, avr_loss=0.0235]
steps:  26%|██▌       | 157/608 [36:21<1:44:25, 13.89s/it, avr_loss=0.0235]
steps:  26%|██▌       | 157/608 [36:21<1:44:25, 13.89s/it, avr_loss=0.0231]
steps:  26%|██▌       | 158/608 [36:39<1:44:23, 13.92s/it, avr_loss=0.0231]
steps:  26%|██▌       | 158/608 [36:39<1:44:23, 13.92s/it, avr_loss=0.0236]
steps:  26%|██▌       | 159/608 [36:54<1:44:13, 13.93s/it, avr_loss=0.0236]
steps:  26%|██▌       | 159/608 [36:54<1:44:13, 13.93s/it, avr_loss=0.0231]
steps:  26%|██▋       | 160/608 [37:15<1:44:19, 13.97s/it, avr_loss=0.0231]
steps:  26%|██▋       | 160/608 [37:15<1:44:19, 13.97s/it, avr_loss=0.0221]
steps:  26%|██▋       | 161/608 [37:30<1:44:09, 13.98s/it, avr_loss=0.0221]
steps:  26%|██▋       | 161/608 [37:30<1:44:09, 13.98s/it, avr_loss=0.0221]
steps:  27%|██▋       | 162/608 [37:51<1:44:13, 14.02s/it, avr_loss=0.0221]
steps:  27%|██▋       | 162/608 [37:51<1:44:13, 14.02s/it, avr_loss=0.0237]
steps:  27%|██▋       | 163/608 [38:06<1:44:03, 14.03s/it, avr_loss=0.0237]
steps:  27%|██▋       | 163/608 [38:06<1:44:03, 14.03s/it, avr_loss=0.0238]
steps:  27%|██▋       | 164/608 [38:22<1:43:52, 14.04s/it, avr_loss=0.0238]
steps:  27%|██▋       | 164/608 [38:22<1:43:52, 14.04s/it, avr_loss=0.0238]
steps:  27%|██▋       | 165/608 [38:43<1:43:57, 14.08s/it, avr_loss=0.0238]
steps:  27%|██▋       | 165/608 [38:43<1:43:57, 14.08s/it, avr_loss=0.0237]
steps:  27%|██▋       | 166/608 [38:58<1:43:46, 14.09s/it, avr_loss=0.0237]
steps:  27%|██▋       | 166/608 [38:58<1:43:46, 14.09s/it, avr_loss=0.0236]
steps:  27%|██▋       | 167/608 [39:19<1:43:50, 14.13s/it, avr_loss=0.0236]
steps:  27%|██▋       | 167/608 [39:19<1:43:50, 14.13s/it, avr_loss=0.0233]
steps:  28%|██▊       | 168/608 [39:34<1:43:39, 14.13s/it, avr_loss=0.0233]
steps:  28%|██▊       | 168/608 [39:34<1:43:39, 14.13s/it, avr_loss=0.0234]
steps:  28%|██▊       | 169/608 [39:49<1:43:26, 14.14s/it, avr_loss=0.0234]
steps:  28%|██▊       | 169/608 [39:49<1:43:26, 14.14s/it, avr_loss=0.024] 
steps:  28%|██▊       | 170/608 [40:01<1:43:08, 14.13s/it, avr_loss=0.024]
steps:  28%|██▊       | 170/608 [40:01<1:43:08, 14.13s/it, avr_loss=0.0239]
steps:  28%|██▊       | 171/608 [40:14<1:42:50, 14.12s/it, avr_loss=0.0239]
steps:  28%|██▊       | 171/608 [40:14<1:42:50, 14.12s/it, avr_loss=0.0241]
steps:  28%|██▊       | 172/608 [40:27<1:42:33, 14.11s/it, avr_loss=0.0241]
steps:  28%|██▊       | 172/608 [40:27<1:42:33, 14.11s/it, avr_loss=0.0237]
steps:  28%|██▊       | 173/608 [40:40<1:42:15, 14.10s/it, avr_loss=0.0237]
steps:  28%|██▊       | 173/608 [40:40<1:42:15, 14.10s/it, avr_loss=0.0237]
steps:  29%|██▊       | 174/608 [40:52<1:41:58, 14.10s/it, avr_loss=0.0237]
steps:  29%|██▊       | 174/608 [40:52<1:41:58, 14.10s/it, avr_loss=0.0246]
steps:  29%|██▉       | 175/608 [41:05<1:41:40, 14.09s/it, avr_loss=0.0246]
steps:  29%|██▉       | 175/608 [41:05<1:41:40, 14.09s/it, avr_loss=0.0246]
steps:  29%|██▉       | 176/608 [41:18<1:41:23, 14.08s/it, avr_loss=0.0246]
steps:  29%|██▉       | 176/608 [41:18<1:41:23, 14.08s/it, avr_loss=0.025] 
steps:  29%|██▉       | 177/608 [41:31<1:41:06, 14.07s/it, avr_loss=0.025]
steps:  29%|██▉       | 177/608 [41:31<1:41:06, 14.07s/it, avr_loss=0.0251]
steps:  29%|██▉       | 178/608 [41:44<1:40:49, 14.07s/it, avr_loss=0.0251]
steps:  29%|██▉       | 178/608 [41:44<1:40:49, 14.07s/it, avr_loss=0.0251]
steps:  29%|██▉       | 179/608 [41:56<1:40:31, 14.06s/it, avr_loss=0.0251]
steps:  29%|██▉       | 179/608 [41:56<1:40:31, 14.06s/it, avr_loss=0.0253]
steps:  30%|██▉       | 180/608 [42:09<1:40:14, 14.05s/it, avr_loss=0.0253]
steps:  30%|██▉       | 180/608 [42:09<1:40:14, 14.05s/it, avr_loss=0.0269]
steps:  30%|██▉       | 181/608 [42:22<1:39:57, 14.05s/it, avr_loss=0.0269]
steps:  30%|██▉       | 181/608 [42:22<1:39:57, 14.05s/it, avr_loss=0.027] 
steps:  30%|██▉       | 182/608 [42:35<1:39:40, 14.04s/it, avr_loss=0.027]
steps:  30%|██▉       | 182/608 [42:35<1:39:40, 14.04s/it, avr_loss=0.0288]
steps:  30%|███       | 183/608 [42:48<1:39:24, 14.03s/it, avr_loss=0.0288]
steps:  30%|███       | 183/608 [42:48<1:39:24, 14.03s/it, avr_loss=0.0289]
steps:  30%|███       | 184/608 [43:00<1:39:07, 14.03s/it, avr_loss=0.0289]
steps:  30%|███       | 184/608 [43:00<1:39:07, 14.03s/it, avr_loss=0.0294]
steps:  30%|███       | 185/608 [43:13<1:38:50, 14.02s/it, avr_loss=0.0294]
steps:  30%|███       | 185/608 [43:13<1:38:50, 14.02s/it, avr_loss=0.0327]
steps:  31%|███       | 186/608 [43:26<1:38:33, 14.01s/it, avr_loss=0.0327]
steps:  31%|███       | 186/608 [43:26<1:38:33, 14.01s/it, avr_loss=0.0326]
steps:  31%|███       | 187/608 [43:39<1:38:16, 14.01s/it, avr_loss=0.0326]
steps:  31%|███       | 187/608 [43:39<1:38:16, 14.01s/it, avr_loss=0.0326]
steps:  31%|███       | 188/608 [43:51<1:37:59, 14.00s/it, avr_loss=0.0326]
steps:  31%|███       | 188/608 [43:51<1:37:59, 14.00s/it, avr_loss=0.0335]
steps:  31%|███       | 189/608 [44:04<1:37:43, 13.99s/it, avr_loss=0.0335]
steps:  31%|███       | 189/608 [44:04<1:37:43, 13.99s/it, avr_loss=0.0334]
steps:  31%|███▏      | 190/608 [44:17<1:37:26, 13.99s/it, avr_loss=0.0334]
steps:  31%|███▏      | 190/608 [44:17<1:37:26, 13.99s/it, avr_loss=0.0334]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/block_handover_D435_full/train_lora-000005.safetensors

epoch 6/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6

steps:  31%|███▏      | 191/608 [44:32<1:37:13, 13.99s/it, avr_loss=0.0334]
steps:  31%|███▏      | 191/608 [44:32<1:37:13, 13.99s/it, avr_loss=0.0333]
steps:  32%|███▏      | 192/608 [44:44<1:36:57, 13.98s/it, avr_loss=0.0333]
steps:  32%|███▏      | 192/608 [44:44<1:36:57, 13.98s/it, avr_loss=0.0333]
steps:  32%|███▏      | 193/608 [44:57<1:36:40, 13.98s/it, avr_loss=0.0333]
steps:  32%|███▏      | 193/608 [44:57<1:36:40, 13.98s/it, avr_loss=0.0332]
steps:  32%|███▏      | 194/608 [45:10<1:36:23, 13.97s/it, avr_loss=0.0332]
steps:  32%|███▏      | 194/608 [45:10<1:36:23, 13.97s/it, avr_loss=0.0327]
steps:  32%|███▏      | 195/608 [45:23<1:36:07, 13.96s/it, avr_loss=0.0327]
steps:  32%|███▏      | 195/608 [45:23<1:36:07, 13.96s/it, avr_loss=0.0329]
steps:  32%|███▏      | 196/608 [45:35<1:35:50, 13.96s/it, avr_loss=0.0329]
steps:  32%|███▏      | 196/608 [45:35<1:35:50, 13.96s/it, avr_loss=0.0327]
steps:  32%|███▏      | 197/608 [45:48<1:35:34, 13.95s/it, avr_loss=0.0327]
steps:  32%|███▏      | 197/608 [45:48<1:35:34, 13.95s/it, avr_loss=0.0327]
steps:  33%|███▎      | 198/608 [46:01<1:35:17, 13.95s/it, avr_loss=0.0327]
steps:  33%|███▎      | 198/608 [46:01<1:35:17, 13.95s/it, avr_loss=0.0327]
steps:  33%|███▎      | 199/608 [46:14<1:35:01, 13.94s/it, avr_loss=0.0327]
steps:  33%|███▎      | 199/608 [46:14<1:35:01, 13.94s/it, avr_loss=0.0326]
steps:  33%|███▎      | 200/608 [46:26<1:34:45, 13.93s/it, avr_loss=0.0326]
steps:  33%|███▎      | 200/608 [46:26<1:34:45, 13.93s/it, avr_loss=0.0311]
steps:  33%|███▎      | 201/608 [46:39<1:34:28, 13.93s/it, avr_loss=0.0311]
steps:  33%|███▎      | 201/608 [46:39<1:34:28, 13.93s/it, avr_loss=0.031] 
steps:  33%|███▎      | 202/608 [46:52<1:34:12, 13.92s/it, avr_loss=0.031]
steps:  33%|███▎      | 202/608 [46:52<1:34:12, 13.92s/it, avr_loss=0.0313]
steps:  33%|███▎      | 203/608 [47:05<1:33:56, 13.92s/it, avr_loss=0.0313]
steps:  33%|███▎      | 203/608 [47:05<1:33:56, 13.92s/it, avr_loss=0.0333]
steps:  34%|███▎      | 204/608 [47:18<1:33:40, 13.91s/it, avr_loss=0.0333]
steps:  34%|███▎      | 204/608 [47:18<1:33:40, 13.91s/it, avr_loss=0.0331]
steps:  34%|███▎      | 205/608 [47:30<1:33:24, 13.91s/it, avr_loss=0.0331]
steps:  34%|███▎      | 205/608 [47:30<1:33:24, 13.91s/it, avr_loss=0.0332]
steps:  34%|███▍      | 206/608 [47:43<1:33:08, 13.90s/it, avr_loss=0.0332]
steps:  34%|███▍      | 206/608 [47:43<1:33:08, 13.90s/it, avr_loss=0.0332]
steps:  34%|███▍      | 207/608 [47:56<1:32:52, 13.90s/it, avr_loss=0.0332]
steps:  34%|███▍      | 207/608 [47:56<1:32:52, 13.90s/it, avr_loss=0.0327]
steps:  34%|███▍      | 208/608 [48:09<1:32:35, 13.89s/it, avr_loss=0.0327]
steps:  34%|███▍      | 208/608 [48:09<1:32:35, 13.89s/it, avr_loss=0.0326]
steps:  34%|███▍      | 209/608 [48:21<1:32:19, 13.88s/it, avr_loss=0.0326]
steps:  34%|███▍      | 209/608 [48:21<1:32:19, 13.88s/it, avr_loss=0.0326]
steps:  35%|███▍      | 210/608 [48:34<1:32:03, 13.88s/it, avr_loss=0.0326]
steps:  35%|███▍      | 210/608 [48:34<1:32:03, 13.88s/it, avr_loss=0.0324]
steps:  35%|███▍      | 211/608 [48:47<1:31:47, 13.87s/it, avr_loss=0.0324]
steps:  35%|███▍      | 211/608 [48:47<1:31:47, 13.87s/it, avr_loss=0.0326]
steps:  35%|███▍      | 212/608 [49:00<1:31:32, 13.87s/it, avr_loss=0.0326]
steps:  35%|███▍      | 212/608 [49:00<1:31:32, 13.87s/it, avr_loss=0.0349]
steps:  35%|███▌      | 213/608 [49:13<1:31:16, 13.86s/it, avr_loss=0.0349]
steps:  35%|███▌      | 213/608 [49:13<1:31:16, 13.86s/it, avr_loss=0.0351]
steps:  35%|███▌      | 214/608 [49:25<1:31:00, 13.86s/it, avr_loss=0.0351]
steps:  35%|███▌      | 214/608 [49:25<1:31:00, 13.86s/it, avr_loss=0.0346]
steps:  35%|███▌      | 215/608 [49:38<1:30:44, 13.85s/it, avr_loss=0.0346]
steps:  35%|███▌      | 215/608 [49:38<1:30:44, 13.85s/it, avr_loss=0.0345]
steps:  36%|███▌      | 216/608 [49:51<1:30:28, 13.85s/it, avr_loss=0.0345]
steps:  36%|███▌      | 216/608 [49:51<1:30:28, 13.85s/it, avr_loss=0.0346]
steps:  36%|███▌      | 217/608 [50:04<1:30:13, 13.84s/it, avr_loss=0.0346]
steps:  36%|███▌      | 217/608 [50:04<1:30:13, 13.84s/it, avr_loss=0.0347]
steps:  36%|███▌      | 218/608 [50:17<1:29:57, 13.84s/it, avr_loss=0.0347]
steps:  36%|███▌      | 218/608 [50:17<1:29:57, 13.84s/it, avr_loss=0.0332]
steps:  36%|███▌      | 219/608 [50:29<1:29:41, 13.83s/it, avr_loss=0.0332]
steps:  36%|███▌      | 219/608 [50:29<1:29:41, 13.83s/it, avr_loss=0.0334]
steps:  36%|███▌      | 220/608 [50:42<1:29:26, 13.83s/it, avr_loss=0.0334]
steps:  36%|███▌      | 220/608 [50:42<1:29:26, 13.83s/it, avr_loss=0.0318]
steps:  36%|███▋      | 221/608 [50:55<1:29:10, 13.83s/it, avr_loss=0.0318]
steps:  36%|███▋      | 221/608 [50:55<1:29:10, 13.83s/it, avr_loss=0.0318]
steps:  37%|███▋      | 222/608 [51:08<1:28:54, 13.82s/it, avr_loss=0.0318]
steps:  37%|███▋      | 222/608 [51:08<1:28:54, 13.82s/it, avr_loss=0.0309]
steps:  37%|███▋      | 223/608 [51:21<1:28:39, 13.82s/it, avr_loss=0.0309]
steps:  37%|███▋      | 223/608 [51:21<1:28:39, 13.82s/it, avr_loss=0.0277]
steps:  37%|███▋      | 224/608 [51:33<1:28:23, 13.81s/it, avr_loss=0.0277]
steps:  37%|███▋      | 224/608 [51:33<1:28:23, 13.81s/it, avr_loss=0.0284]
steps:  37%|███▋      | 225/608 [51:46<1:28:08, 13.81s/it, avr_loss=0.0284]
steps:  37%|███▋      | 225/608 [51:46<1:28:08, 13.81s/it, avr_loss=0.0289]
steps:  37%|███▋      | 226/608 [51:59<1:27:52, 13.80s/it, avr_loss=0.0289]
steps:  37%|███▋      | 226/608 [51:59<1:27:52, 13.80s/it, avr_loss=0.0279]
steps:  37%|███▋      | 227/608 [52:12<1:27:37, 13.80s/it, avr_loss=0.0279]
steps:  37%|███▋      | 227/608 [52:12<1:27:37, 13.80s/it, avr_loss=0.0284]
steps:  38%|███▊      | 228/608 [52:25<1:27:21, 13.79s/it, avr_loss=0.0284]
steps:  38%|███▊      | 228/608 [52:25<1:27:21, 13.79s/it, avr_loss=0.0286]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/block_handover_D435_full/train_lora-000006.safetensors

epoch 7/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7

steps:  38%|███▊      | 229/608 [52:39<1:27:09, 13.80s/it, avr_loss=0.0286]
steps:  38%|███▊      | 229/608 [52:39<1:27:09, 13.80s/it, avr_loss=0.0285]
steps:  38%|███▊      | 230/608 [52:52<1:26:54, 13.79s/it, avr_loss=0.0285]
steps:  38%|███▊      | 230/608 [52:52<1:26:54, 13.79s/it, avr_loss=0.0283]
steps:  38%|███▊      | 231/608 [53:05<1:26:38, 13.79s/it, avr_loss=0.0283]
steps:  38%|███▊      | 231/608 [53:05<1:26:38, 13.79s/it, avr_loss=0.0281]
steps:  38%|███▊      | 232/608 [53:18<1:26:23, 13.79s/it, avr_loss=0.0281]
steps:  38%|███▊      | 232/608 [53:18<1:26:23, 13.79s/it, avr_loss=0.0281]
steps:  38%|███▊      | 233/608 [53:30<1:26:07, 13.78s/it, avr_loss=0.0281]
steps:  38%|███▊      | 233/608 [53:30<1:26:07, 13.78s/it, avr_loss=0.0279]
steps:  38%|███▊      | 234/608 [53:43<1:25:52, 13.78s/it, avr_loss=0.0279]
steps:  38%|███▊      | 234/608 [53:43<1:25:52, 13.78s/it, avr_loss=0.0273]
steps:  39%|███▊      | 235/608 [53:56<1:25:36, 13.77s/it, avr_loss=0.0273]
steps:  39%|███▊      | 235/608 [53:56<1:25:36, 13.77s/it, avr_loss=0.0274]
steps:  39%|███▉      | 236/608 [54:09<1:25:21, 13.77s/it, avr_loss=0.0274]
steps:  39%|███▉      | 236/608 [54:09<1:25:21, 13.77s/it, avr_loss=0.0276]
steps:  39%|███▉      | 237/608 [54:21<1:25:06, 13.76s/it, avr_loss=0.0276]
steps:  39%|███▉      | 237/608 [54:21<1:25:06, 13.76s/it, avr_loss=0.0276]
steps:  39%|███▉      | 238/608 [54:34<1:24:51, 13.76s/it, avr_loss=0.0276]
steps:  39%|███▉      | 238/608 [54:34<1:24:51, 13.76s/it, avr_loss=0.0275]
steps:  39%|███▉      | 239/608 [54:47<1:24:35, 13.76s/it, avr_loss=0.0275]
steps:  39%|███▉      | 239/608 [54:47<1:24:35, 13.76s/it, avr_loss=0.0275]
steps:  39%|███▉      | 240/608 [55:00<1:24:20, 13.75s/it, avr_loss=0.0275]
steps:  39%|███▉      | 240/608 [55:00<1:24:20, 13.75s/it, avr_loss=0.0275]
steps:  40%|███▉      | 241/608 [55:12<1:24:05, 13.75s/it, avr_loss=0.0275]
steps:  40%|███▉      | 241/608 [55:12<1:24:05, 13.75s/it, avr_loss=0.0255]
steps:  40%|███▉      | 242/608 [55:25<1:23:49, 13.74s/it, avr_loss=0.0255]
steps:  40%|███▉      | 242/608 [55:25<1:23:49, 13.74s/it, avr_loss=0.0258]
steps:  40%|███▉      | 243/608 [55:38<1:23:34, 13.74s/it, avr_loss=0.0258]
steps:  40%|███▉      | 243/608 [55:38<1:23:34, 13.74s/it, avr_loss=0.0259]
steps:  40%|████      | 244/608 [55:51<1:23:19, 13.73s/it, avr_loss=0.0259]
steps:  40%|████      | 244/608 [55:51<1:23:19, 13.73s/it, avr_loss=0.0264]
steps:  40%|████      | 245/608 [56:04<1:23:04, 13.73s/it, avr_loss=0.0264]
steps:  40%|████      | 245/608 [56:04<1:23:04, 13.73s/it, avr_loss=0.0262]
steps:  40%|████      | 246/608 [56:16<1:22:49, 13.73s/it, avr_loss=0.0262]
steps:  40%|████      | 246/608 [56:16<1:22:49, 13.73s/it, avr_loss=0.0263]
steps:  41%|████      | 247/608 [56:29<1:22:34, 13.72s/it, avr_loss=0.0263]
steps:  41%|████      | 247/608 [56:29<1:22:34, 13.72s/it, avr_loss=0.0261]
steps:  41%|████      | 248/608 [56:42<1:22:18, 13.72s/it, avr_loss=0.0261]
steps:  41%|████      | 248/608 [56:42<1:22:18, 13.72s/it, avr_loss=0.0261]
steps:  41%|████      | 249/608 [56:55<1:22:03, 13.72s/it, avr_loss=0.0261]
steps:  41%|████      | 249/608 [56:55<1:22:03, 13.72s/it, avr_loss=0.026] 
steps:  41%|████      | 250/608 [57:07<1:21:48, 13.71s/it, avr_loss=0.026]
steps:  41%|████      | 250/608 [57:07<1:21:48, 13.71s/it, avr_loss=0.0229]
steps:  41%|████▏     | 251/608 [57:20<1:21:33, 13.71s/it, avr_loss=0.0229]
steps:  41%|████▏     | 251/608 [57:20<1:21:33, 13.71s/it, avr_loss=0.0226]
steps:  41%|████▏     | 252/608 [57:33<1:21:18, 13.70s/it, avr_loss=0.0226]
steps:  41%|████▏     | 252/608 [57:33<1:21:18, 13.70s/it, avr_loss=0.0227]
steps:  42%|████▏     | 253/608 [57:46<1:21:03, 13.70s/it, avr_loss=0.0227]
steps:  42%|████▏     | 253/608 [57:46<1:21:03, 13.70s/it, avr_loss=0.0227]
steps:  42%|████▏     | 254/608 [57:59<1:20:48, 13.70s/it, avr_loss=0.0227]
steps:  42%|████▏     | 254/608 [57:59<1:20:48, 13.70s/it, avr_loss=0.0227]
steps:  42%|████▏     | 255/608 [58:11<1:20:33, 13.69s/it, avr_loss=0.0227]
steps:  42%|████▏     | 255/608 [58:11<1:20:33, 13.69s/it, avr_loss=0.0227]
steps:  42%|████▏     | 256/608 [58:24<1:20:18, 13.69s/it, avr_loss=0.0227]
steps:  42%|████▏     | 256/608 [58:24<1:20:18, 13.69s/it, avr_loss=0.0226]
steps:  42%|████▏     | 257/608 [58:37<1:20:03, 13.69s/it, avr_loss=0.0226]
steps:  42%|████▏     | 257/608 [58:37<1:20:03, 13.69s/it, avr_loss=0.0224]
steps:  42%|████▏     | 258/608 [58:50<1:19:48, 13.68s/it, avr_loss=0.0224]
steps:  42%|████▏     | 258/608 [58:50<1:19:48, 13.68s/it, avr_loss=0.0222]
steps:  43%|████▎     | 259/608 [59:02<1:19:33, 13.68s/it, avr_loss=0.0222]
steps:  43%|████▎     | 259/608 [59:02<1:19:33, 13.68s/it, avr_loss=0.0218]
steps:  43%|████▎     | 260/608 [59:15<1:19:18, 13.67s/it, avr_loss=0.0218]
steps:  43%|████▎     | 260/608 [59:15<1:19:18, 13.67s/it, avr_loss=0.0218]
steps:  43%|████▎     | 261/608 [59:28<1:19:03, 13.67s/it, avr_loss=0.0218]
steps:  43%|████▎     | 261/608 [59:28<1:19:03, 13.67s/it, avr_loss=0.0219]
steps:  43%|████▎     | 262/608 [59:41<1:18:49, 13.67s/it, avr_loss=0.0219]
steps:  43%|████▎     | 262/608 [59:41<1:18:49, 13.67s/it, avr_loss=0.0213]
steps:  43%|████▎     | 263/608 [59:53<1:18:34, 13.66s/it, avr_loss=0.0213]
steps:  43%|████▎     | 263/608 [59:53<1:18:34, 13.66s/it, avr_loss=0.022] 
steps:  43%|████▎     | 264/608 [1:00:06<1:18:19, 13.66s/it, avr_loss=0.022]
steps:  43%|████▎     | 264/608 [1:00:06<1:18:19, 13.66s/it, avr_loss=0.0215]
steps:  44%|████▎     | 265/608 [1:00:19<1:18:04, 13.66s/it, avr_loss=0.0215]
steps:  44%|████▎     | 265/608 [1:00:19<1:18:04, 13.66s/it, avr_loss=0.0209]
steps:  44%|████▍     | 266/608 [1:00:32<1:17:49, 13.65s/it, avr_loss=0.0209]
steps:  44%|████▍     | 266/608 [1:00:32<1:17:49, 13.65s/it, avr_loss=0.0207]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/block_handover_D435_full/train_lora-000007.safetensors

epoch 8/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8

steps:  44%|████▍     | 267/608 [1:00:46<1:17:37, 13.66s/it, avr_loss=0.0207]
steps:  44%|████▍     | 267/608 [1:00:46<1:17:37, 13.66s/it, avr_loss=0.0209]
steps:  44%|████▍     | 268/608 [1:00:59<1:17:22, 13.65s/it, avr_loss=0.0209]
steps:  44%|████▍     | 268/608 [1:00:59<1:17:22, 13.65s/it, avr_loss=0.0211]
steps:  44%|████▍     | 269/608 [1:01:12<1:17:07, 13.65s/it, avr_loss=0.0211]
steps:  44%|████▍     | 269/608 [1:01:12<1:17:07, 13.65s/it, avr_loss=0.0211]
steps:  44%|████▍     | 270/608 [1:01:24<1:16:53, 13.65s/it, avr_loss=0.0211]
steps:  44%|████▍     | 270/608 [1:01:24<1:16:53, 13.65s/it, avr_loss=0.0211]
steps:  45%|████▍     | 271/608 [1:01:37<1:16:38, 13.64s/it, avr_loss=0.0211]
steps:  45%|████▍     | 271/608 [1:01:37<1:16:38, 13.64s/it, avr_loss=0.0215]
steps:  45%|████▍     | 272/608 [1:01:50<1:16:23, 13.64s/it, avr_loss=0.0215]
steps:  45%|████▍     | 272/608 [1:01:50<1:16:23, 13.64s/it, avr_loss=0.0217]
steps:  45%|████▍     | 273/608 [1:02:03<1:16:08, 13.64s/it, avr_loss=0.0217]
steps:  45%|████▍     | 273/608 [1:02:03<1:16:08, 13.64s/it, avr_loss=0.022] 
steps:  45%|████▌     | 274/608 [1:02:15<1:15:53, 13.63s/it, avr_loss=0.022]
steps:  45%|████▌     | 274/608 [1:02:15<1:15:53, 13.63s/it, avr_loss=0.022]
steps:  45%|████▌     | 275/608 [1:02:28<1:15:39, 13.63s/it, avr_loss=0.022]
steps:  45%|████▌     | 275/608 [1:02:28<1:15:39, 13.63s/it, avr_loss=0.022]
steps:  45%|████▌     | 276/608 [1:02:41<1:15:24, 13.63s/it, avr_loss=0.022]
steps:  45%|████▌     | 276/608 [1:02:41<1:15:24, 13.63s/it, avr_loss=0.0219]
steps:  46%|████▌     | 277/608 [1:02:54<1:15:09, 13.63s/it, avr_loss=0.0219]
steps:  46%|████▌     | 277/608 [1:02:54<1:15:09, 13.63s/it, avr_loss=0.0222]
steps:  46%|████▌     | 278/608 [1:03:06<1:14:55, 13.62s/it, avr_loss=0.0222]
steps:  46%|████▌     | 278/608 [1:03:06<1:14:55, 13.62s/it, avr_loss=0.0218]
steps:  46%|████▌     | 279/608 [1:03:19<1:14:40, 13.62s/it, avr_loss=0.0218]
steps:  46%|████▌     | 279/608 [1:03:19<1:14:40, 13.62s/it, avr_loss=0.0243]
steps:  46%|████▌     | 280/608 [1:03:32<1:14:26, 13.62s/it, avr_loss=0.0243]
steps:  46%|████▌     | 280/608 [1:03:32<1:14:26, 13.62s/it, avr_loss=0.0242]
steps:  46%|████▌     | 281/608 [1:03:45<1:14:11, 13.61s/it, avr_loss=0.0242]
steps:  46%|████▌     | 281/608 [1:03:45<1:14:11, 13.61s/it, avr_loss=0.024] 
steps:  46%|████▋     | 282/608 [1:03:58<1:13:56, 13.61s/it, avr_loss=0.024]
steps:  46%|████▋     | 282/608 [1:03:58<1:13:56, 13.61s/it, avr_loss=0.0252]
steps:  47%|████▋     | 283/608 [1:04:10<1:13:42, 13.61s/it, avr_loss=0.0252]
steps:  47%|████▋     | 283/608 [1:04:10<1:13:42, 13.61s/it, avr_loss=0.0254]
steps:  47%|████▋     | 284/608 [1:04:23<1:13:27, 13.60s/it, avr_loss=0.0254]
steps:  47%|████▋     | 284/608 [1:04:23<1:13:27, 13.60s/it, avr_loss=0.0252]
steps:  47%|████▋     | 285/608 [1:04:36<1:13:13, 13.60s/it, avr_loss=0.0252]
steps:  47%|████▋     | 285/608 [1:04:36<1:13:13, 13.60s/it, avr_loss=0.0252]
steps:  47%|████▋     | 286/608 [1:04:49<1:12:58, 13.60s/it, avr_loss=0.0252]
steps:  47%|████▋     | 286/608 [1:04:49<1:12:58, 13.60s/it, avr_loss=0.0253]
steps:  47%|████▋     | 287/608 [1:05:01<1:12:44, 13.60s/it, avr_loss=0.0253]
steps:  47%|████▋     | 287/608 [1:05:01<1:12:44, 13.60s/it, avr_loss=0.0256]
steps:  47%|████▋     | 288/608 [1:05:14<1:12:29, 13.59s/it, avr_loss=0.0256]
steps:  47%|████▋     | 288/608 [1:05:14<1:12:29, 13.59s/it, avr_loss=0.026] 
steps:  48%|████▊     | 289/608 [1:05:27<1:12:15, 13.59s/it, avr_loss=0.026]
steps:  48%|████▊     | 289/608 [1:05:27<1:12:15, 13.59s/it, avr_loss=0.026]
steps:  48%|████▊     | 290/608 [1:05:40<1:12:00, 13.59s/it, avr_loss=0.026]
steps:  48%|████▊     | 290/608 [1:05:40<1:12:00, 13.59s/it, avr_loss=0.0258]
steps:  48%|████▊     | 291/608 [1:05:53<1:11:46, 13.58s/it, avr_loss=0.0258]
steps:  48%|████▊     | 291/608 [1:05:53<1:11:46, 13.58s/it, avr_loss=0.0259]
steps:  48%|████▊     | 292/608 [1:06:05<1:11:31, 13.58s/it, avr_loss=0.0259]
steps:  48%|████▊     | 292/608 [1:06:05<1:11:31, 13.58s/it, avr_loss=0.0259]
steps:  48%|████▊     | 293/608 [1:06:18<1:11:17, 13.58s/it, avr_loss=0.0259]
steps:  48%|████▊     | 293/608 [1:06:18<1:11:17, 13.58s/it, avr_loss=0.0258]
steps:  48%|████▊     | 294/608 [1:06:31<1:11:02, 13.58s/it, avr_loss=0.0258]
steps:  48%|████▊     | 294/608 [1:06:31<1:11:02, 13.58s/it, avr_loss=0.0259]
steps:  49%|████▊     | 295/608 [1:06:44<1:10:48, 13.57s/it, avr_loss=0.0259]
steps:  49%|████▊     | 295/608 [1:06:44<1:10:48, 13.57s/it, avr_loss=0.0259]
steps:  49%|████▊     | 296/608 [1:06:56<1:10:34, 13.57s/it, avr_loss=0.0259]
steps:  49%|████▊     | 296/608 [1:06:56<1:10:34, 13.57s/it, avr_loss=0.0259]
steps:  49%|████▉     | 297/608 [1:07:09<1:10:19, 13.57s/it, avr_loss=0.0259]
steps:  49%|████▉     | 297/608 [1:07:09<1:10:19, 13.57s/it, avr_loss=0.0264]
steps:  49%|████▉     | 298/608 [1:07:22<1:10:05, 13.57s/it, avr_loss=0.0264]
steps:  49%|████▉     | 298/608 [1:07:22<1:10:05, 13.57s/it, avr_loss=0.0265]
steps:  49%|████▉     | 299/608 [1:07:35<1:09:51, 13.56s/it, avr_loss=0.0265]
steps:  49%|████▉     | 299/608 [1:07:35<1:09:51, 13.56s/it, avr_loss=0.0264]
steps:  49%|████▉     | 300/608 [1:07:48<1:09:36, 13.56s/it, avr_loss=0.0264]
steps:  49%|████▉     | 300/608 [1:07:48<1:09:36, 13.56s/it, avr_loss=0.0265]
steps:  50%|████▉     | 301/608 [1:08:01<1:09:22, 13.56s/it, avr_loss=0.0265]
steps:  50%|████▉     | 301/608 [1:08:01<1:09:22, 13.56s/it, avr_loss=0.0251]
steps:  50%|████▉     | 302/608 [1:08:13<1:09:08, 13.56s/it, avr_loss=0.0251]
steps:  50%|████▉     | 302/608 [1:08:13<1:09:08, 13.56s/it, avr_loss=0.0253]
steps:  50%|████▉     | 303/608 [1:08:26<1:08:53, 13.55s/it, avr_loss=0.0253]
steps:  50%|████▉     | 303/608 [1:08:26<1:08:53, 13.55s/it, avr_loss=0.0254]
steps:  50%|█████     | 304/608 [1:08:39<1:08:39, 13.55s/it, avr_loss=0.0254]
steps:  50%|█████     | 304/608 [1:08:39<1:08:39, 13.55s/it, avr_loss=0.0256]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/block_handover_D435_full/train_lora-000008.safetensors

epoch 9/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9

steps:  50%|█████     | 305/608 [1:08:53<1:08:26, 13.55s/it, avr_loss=0.0256]
steps:  50%|█████     | 305/608 [1:08:53<1:08:26, 13.55s/it, avr_loss=0.0254]
steps:  50%|█████     | 306/608 [1:09:06<1:08:12, 13.55s/it, avr_loss=0.0254]
steps:  50%|█████     | 306/608 [1:09:06<1:08:12, 13.55s/it, avr_loss=0.0253]
steps:  50%|█████     | 307/608 [1:09:19<1:07:58, 13.55s/it, avr_loss=0.0253]
steps:  50%|█████     | 307/608 [1:09:19<1:07:58, 13.55s/it, avr_loss=0.0253]
steps:  51%|█████     | 308/608 [1:09:32<1:07:43, 13.55s/it, avr_loss=0.0253]
steps:  51%|█████     | 308/608 [1:09:32<1:07:43, 13.55s/it, avr_loss=0.0253]
steps:  51%|█████     | 309/608 [1:09:45<1:07:29, 13.54s/it, avr_loss=0.0253]
steps:  51%|█████     | 309/608 [1:09:45<1:07:29, 13.54s/it, avr_loss=0.0257]
steps:  51%|█████     | 310/608 [1:09:57<1:07:15, 13.54s/it, avr_loss=0.0257]
steps:  51%|█████     | 310/608 [1:09:57<1:07:15, 13.54s/it, avr_loss=0.0257]
steps:  51%|█████     | 311/608 [1:10:10<1:07:01, 13.54s/it, avr_loss=0.0257]
steps:  51%|█████     | 311/608 [1:10:10<1:07:01, 13.54s/it, avr_loss=0.0253]
steps:  51%|█████▏    | 312/608 [1:10:23<1:06:46, 13.54s/it, avr_loss=0.0253]
steps:  51%|█████▏    | 312/608 [1:10:23<1:06:46, 13.54s/it, avr_loss=0.0253]
steps:  51%|█████▏    | 313/608 [1:10:36<1:06:32, 13.53s/it, avr_loss=0.0253]
steps:  51%|█████▏    | 313/608 [1:10:36<1:06:32, 13.53s/it, avr_loss=0.0254]
steps:  52%|█████▏    | 314/608 [1:10:49<1:06:18, 13.53s/it, avr_loss=0.0254]
steps:  52%|█████▏    | 314/608 [1:10:49<1:06:18, 13.53s/it, avr_loss=0.0254]
steps:  52%|█████▏    | 315/608 [1:11:01<1:06:04, 13.53s/it, avr_loss=0.0254]
steps:  52%|█████▏    | 315/608 [1:11:01<1:06:04, 13.53s/it, avr_loss=0.025] 
steps:  52%|█████▏    | 316/608 [1:11:14<1:05:49, 13.53s/it, avr_loss=0.025]
steps:  52%|█████▏    | 316/608 [1:11:14<1:05:49, 13.53s/it, avr_loss=0.025]
steps:  52%|█████▏    | 317/608 [1:11:27<1:05:35, 13.53s/it, avr_loss=0.025]
steps:  52%|█████▏    | 317/608 [1:11:27<1:05:35, 13.53s/it, avr_loss=0.0227]
steps:  52%|█████▏    | 318/608 [1:11:40<1:05:21, 13.52s/it, avr_loss=0.0227]
steps:  52%|█████▏    | 318/608 [1:11:40<1:05:21, 13.52s/it, avr_loss=0.0239]
steps:  52%|█████▏    | 319/608 [1:11:53<1:05:07, 13.52s/it, avr_loss=0.0239]
steps:  52%|█████▏    | 319/608 [1:11:53<1:05:07, 13.52s/it, avr_loss=0.0243]
steps:  53%|█████▎    | 320/608 [1:12:05<1:04:53, 13.52s/it, avr_loss=0.0243]
steps:  53%|█████▎    | 320/608 [1:12:05<1:04:53, 13.52s/it, avr_loss=0.0225]
steps:  53%|█████▎    | 321/608 [1:12:18<1:04:39, 13.52s/it, avr_loss=0.0225]
steps:  53%|█████▎    | 321/608 [1:12:18<1:04:39, 13.52s/it, avr_loss=0.0224]
steps:  53%|█████▎    | 322/608 [1:12:31<1:04:24, 13.51s/it, avr_loss=0.0224]
steps:  53%|█████▎    | 322/608 [1:12:31<1:04:24, 13.51s/it, avr_loss=0.0226]
steps:  53%|█████▎    | 323/608 [1:12:44<1:04:10, 13.51s/it, avr_loss=0.0226]
steps:  53%|█████▎    | 323/608 [1:12:44<1:04:10, 13.51s/it, avr_loss=0.0228]
steps:  53%|█████▎    | 324/608 [1:12:56<1:03:56, 13.51s/it, avr_loss=0.0228]
steps:  53%|█████▎    | 324/608 [1:12:56<1:03:56, 13.51s/it, avr_loss=0.0228]
steps:  53%|█████▎    | 325/608 [1:13:09<1:03:42, 13.51s/it, avr_loss=0.0228]
steps:  53%|█████▎    | 325/608 [1:13:09<1:03:42, 13.51s/it, avr_loss=0.0228]
steps:  54%|█████▎    | 326/608 [1:13:22<1:03:28, 13.50s/it, avr_loss=0.0228]
steps:  54%|█████▎    | 326/608 [1:13:22<1:03:28, 13.50s/it, avr_loss=0.0243]
steps:  54%|█████▍    | 327/608 [1:13:35<1:03:14, 13.50s/it, avr_loss=0.0243]
steps:  54%|█████▍    | 327/608 [1:13:35<1:03:14, 13.50s/it, avr_loss=0.0242]
steps:  54%|█████▍    | 328/608 [1:13:48<1:03:00, 13.50s/it, avr_loss=0.0242]
steps:  54%|█████▍    | 328/608 [1:13:48<1:03:00, 13.50s/it, avr_loss=0.0262]
steps:  54%|█████▍    | 329/608 [1:14:01<1:02:46, 13.50s/it, avr_loss=0.0262]
steps:  54%|█████▍    | 329/608 [1:14:01<1:02:46, 13.50s/it, avr_loss=0.0263]
steps:  54%|█████▍    | 330/608 [1:14:13<1:02:31, 13.50s/it, avr_loss=0.0263]
steps:  54%|█████▍    | 330/608 [1:14:13<1:02:31, 13.50s/it, avr_loss=0.0262]
steps:  54%|█████▍    | 331/608 [1:14:26<1:02:17, 13.49s/it, avr_loss=0.0262]
steps:  54%|█████▍    | 331/608 [1:14:26<1:02:17, 13.49s/it, avr_loss=0.0261]
steps:  55%|█████▍    | 332/608 [1:14:39<1:02:03, 13.49s/it, avr_loss=0.0261]
steps:  55%|█████▍    | 332/608 [1:14:39<1:02:03, 13.49s/it, avr_loss=0.0262]
steps:  55%|█████▍    | 333/608 [1:14:51<1:01:49, 13.49s/it, avr_loss=0.0262]
steps:  55%|█████▍    | 333/608 [1:14:51<1:01:49, 13.49s/it, avr_loss=0.0263]
steps:  55%|█████▍    | 334/608 [1:15:04<1:01:35, 13.49s/it, avr_loss=0.0263]
steps:  55%|█████▍    | 334/608 [1:15:04<1:01:35, 13.49s/it, avr_loss=0.0265]
steps:  55%|█████▌    | 335/608 [1:15:17<1:01:21, 13.49s/it, avr_loss=0.0265]
steps:  55%|█████▌    | 335/608 [1:15:17<1:01:21, 13.49s/it, avr_loss=0.0261]
steps:  55%|█████▌    | 336/608 [1:15:30<1:01:07, 13.48s/it, avr_loss=0.0261]
steps:  55%|█████▌    | 336/608 [1:15:30<1:01:07, 13.48s/it, avr_loss=0.0262]
steps:  55%|█████▌    | 337/608 [1:15:43<1:00:53, 13.48s/it, avr_loss=0.0262]
steps:  55%|█████▌    | 337/608 [1:15:43<1:00:53, 13.48s/it, avr_loss=0.0261]
steps:  56%|█████▌    | 338/608 [1:15:55<1:00:39, 13.48s/it, avr_loss=0.0261]
steps:  56%|█████▌    | 338/608 [1:15:55<1:00:39, 13.48s/it, avr_loss=0.0262]
steps:  56%|█████▌    | 339/608 [1:16:08<1:00:25, 13.48s/it, avr_loss=0.0262]
steps:  56%|█████▌    | 339/608 [1:16:08<1:00:25, 13.48s/it, avr_loss=0.0264]
steps:  56%|█████▌    | 340/608 [1:16:21<1:00:11, 13.48s/it, avr_loss=0.0264]
steps:  56%|█████▌    | 340/608 [1:16:21<1:00:11, 13.48s/it, avr_loss=0.0262]
steps:  56%|█████▌    | 341/608 [1:16:34<59:57, 13.47s/it, avr_loss=0.0262]  
steps:  56%|█████▌    | 341/608 [1:16:34<59:57, 13.47s/it, avr_loss=0.0264]
steps:  56%|█████▋    | 342/608 [1:16:47<59:43, 13.47s/it, avr_loss=0.0264]
steps:  56%|█████▋    | 342/608 [1:16:47<59:43, 13.47s/it, avr_loss=0.0264]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/block_handover_D435_full/train_lora-000009.safetensors

epoch 10/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10

steps:  56%|█████▋    | 343/608 [1:17:01<59:30, 13.47s/it, avr_loss=0.0264]
steps:  56%|█████▋    | 343/608 [1:17:01<59:30, 13.47s/it, avr_loss=0.0263]
steps:  57%|█████▋    | 344/608 [1:17:14<59:16, 13.47s/it, avr_loss=0.0263]
steps:  57%|█████▋    | 344/608 [1:17:14<59:16, 13.47s/it, avr_loss=0.0264]
steps:  57%|█████▋    | 345/608 [1:17:27<59:02, 13.47s/it, avr_loss=0.0264]
steps:  57%|█████▋    | 345/608 [1:17:27<59:02, 13.47s/it, avr_loss=0.0264]
steps:  57%|█████▋    | 346/608 [1:17:40<58:48, 13.47s/it, avr_loss=0.0264]
steps:  57%|█████▋    | 346/608 [1:17:40<58:48, 13.47s/it, avr_loss=0.0267]
steps:  57%|█████▋    | 347/608 [1:17:52<58:34, 13.47s/it, avr_loss=0.0267]
steps:  57%|█████▋    | 347/608 [1:17:52<58:34, 13.47s/it, avr_loss=0.0277]
steps:  57%|█████▋    | 348/608 [1:18:05<58:20, 13.46s/it, avr_loss=0.0277]
steps:  57%|█████▋    | 348/608 [1:18:05<58:20, 13.46s/it, avr_loss=0.0276]
steps:  57%|█████▋    | 349/608 [1:18:18<58:06, 13.46s/it, avr_loss=0.0276]
steps:  57%|█████▋    | 349/608 [1:18:18<58:06, 13.46s/it, avr_loss=0.0277]
steps:  58%|█████▊    | 350/608 [1:18:31<57:52, 13.46s/it, avr_loss=0.0277]
steps:  58%|█████▊    | 350/608 [1:18:31<57:52, 13.46s/it, avr_loss=0.0275]
steps:  58%|█████▊    | 351/608 [1:18:43<57:38, 13.46s/it, avr_loss=0.0275]
steps:  58%|█████▊    | 351/608 [1:18:43<57:38, 13.46s/it, avr_loss=0.0276]
steps:  58%|█████▊    | 352/608 [1:18:56<57:24, 13.46s/it, avr_loss=0.0276]
steps:  58%|█████▊    | 352/608 [1:18:56<57:24, 13.46s/it, avr_loss=0.0276]
steps:  58%|█████▊    | 353/608 [1:19:09<57:10, 13.45s/it, avr_loss=0.0276]
steps:  58%|█████▊    | 353/608 [1:19:09<57:10, 13.45s/it, avr_loss=0.028] 
steps:  58%|█████▊    | 354/608 [1:19:22<56:56, 13.45s/it, avr_loss=0.028]
steps:  58%|█████▊    | 354/608 [1:19:22<56:56, 13.45s/it, avr_loss=0.028]
steps:  58%|█████▊    | 355/608 [1:19:35<56:43, 13.45s/it, avr_loss=0.028]
steps:  58%|█████▊    | 355/608 [1:19:35<56:43, 13.45s/it, avr_loss=0.0279]
steps:  59%|█████▊    | 356/608 [1:19:47<56:29, 13.45s/it, avr_loss=0.0279]
steps:  59%|█████▊    | 356/608 [1:19:47<56:29, 13.45s/it, avr_loss=0.0266]
steps:  59%|█████▊    | 357/608 [1:20:00<56:15, 13.45s/it, avr_loss=0.0266]
steps:  59%|█████▊    | 357/608 [1:20:00<56:15, 13.45s/it, avr_loss=0.0267]
steps:  59%|█████▉    | 358/608 [1:20:13<56:01, 13.45s/it, avr_loss=0.0267]
steps:  59%|█████▉    | 358/608 [1:20:13<56:01, 13.45s/it, avr_loss=0.0266]
steps:  59%|█████▉    | 359/608 [1:20:26<55:47, 13.44s/it, avr_loss=0.0266]
steps:  59%|█████▉    | 359/608 [1:20:26<55:47, 13.44s/it, avr_loss=0.0268]
steps:  59%|█████▉    | 360/608 [1:20:38<55:33, 13.44s/it, avr_loss=0.0268]
steps:  59%|█████▉    | 360/608 [1:20:38<55:33, 13.44s/it, avr_loss=0.0267]
steps:  59%|█████▉    | 361/608 [1:20:51<55:19, 13.44s/it, avr_loss=0.0267]
steps:  59%|█████▉    | 361/608 [1:20:51<55:19, 13.44s/it, avr_loss=0.03]  
steps:  60%|█████▉    | 362/608 [1:21:04<55:05, 13.44s/it, avr_loss=0.03]
steps:  60%|█████▉    | 362/608 [1:21:04<55:05, 13.44s/it, avr_loss=0.0297]
steps:  60%|█████▉    | 363/608 [1:21:17<54:51, 13.44s/it, avr_loss=0.0297]
steps:  60%|█████▉    | 363/608 [1:21:17<54:51, 13.44s/it, avr_loss=0.0295]
steps:  60%|█████▉    | 364/608 [1:21:29<54:37, 13.43s/it, avr_loss=0.0295]
steps:  60%|█████▉    | 364/608 [1:21:29<54:37, 13.43s/it, avr_loss=0.0276]
steps:  60%|██████    | 365/608 [1:21:42<54:23, 13.43s/it, avr_loss=0.0276]
steps:  60%|██████    | 365/608 [1:21:42<54:23, 13.43s/it, avr_loss=0.0279]
steps:  60%|██████    | 366/608 [1:21:55<54:10, 13.43s/it, avr_loss=0.0279]
steps:  60%|██████    | 366/608 [1:21:55<54:10, 13.43s/it, avr_loss=0.0261]
steps:  60%|██████    | 367/608 [1:22:08<53:56, 13.43s/it, avr_loss=0.0261]
steps:  60%|██████    | 367/608 [1:22:08<53:56, 13.43s/it, avr_loss=0.0258]
steps:  61%|██████    | 368/608 [1:22:20<53:42, 13.43s/it, avr_loss=0.0258]
steps:  61%|██████    | 368/608 [1:22:20<53:42, 13.43s/it, avr_loss=0.026] 
steps:  61%|██████    | 369/608 [1:22:33<53:28, 13.42s/it, avr_loss=0.026]
steps:  61%|██████    | 369/608 [1:22:33<53:28, 13.42s/it, avr_loss=0.0259]
steps:  61%|██████    | 370/608 [1:22:46<53:14, 13.42s/it, avr_loss=0.0259]
steps:  61%|██████    | 370/608 [1:22:46<53:14, 13.42s/it, avr_loss=0.0261]
steps:  61%|██████    | 371/608 [1:22:59<53:00, 13.42s/it, avr_loss=0.0261]
steps:  61%|██████    | 371/608 [1:22:59<53:00, 13.42s/it, avr_loss=0.0259]
steps:  61%|██████    | 372/608 [1:23:11<52:46, 13.42s/it, avr_loss=0.0259]
steps:  61%|██████    | 372/608 [1:23:11<52:46, 13.42s/it, avr_loss=0.0261]
steps:  61%|██████▏   | 373/608 [1:23:24<52:33, 13.42s/it, avr_loss=0.0261]
steps:  61%|██████▏   | 373/608 [1:23:24<52:33, 13.42s/it, avr_loss=0.0259]
steps:  62%|██████▏   | 374/608 [1:23:37<52:19, 13.42s/it, avr_loss=0.0259]
steps:  62%|██████▏   | 374/608 [1:23:37<52:19, 13.42s/it, avr_loss=0.0259]
steps:  62%|██████▏   | 375/608 [1:23:50<52:05, 13.41s/it, avr_loss=0.0259]
steps:  62%|██████▏   | 375/608 [1:23:50<52:05, 13.41s/it, avr_loss=0.0258]
steps:  62%|██████▏   | 376/608 [1:24:02<51:51, 13.41s/it, avr_loss=0.0258]
steps:  62%|██████▏   | 376/608 [1:24:02<51:51, 13.41s/it, avr_loss=0.0256]
steps:  62%|██████▏   | 377/608 [1:24:15<51:37, 13.41s/it, avr_loss=0.0256]
steps:  62%|██████▏   | 377/608 [1:24:15<51:37, 13.41s/it, avr_loss=0.0257]
steps:  62%|██████▏   | 378/608 [1:24:28<51:23, 13.41s/it, avr_loss=0.0257]
steps:  62%|██████▏   | 378/608 [1:24:28<51:23, 13.41s/it, avr_loss=0.0258]
steps:  62%|██████▏   | 379/608 [1:24:41<51:10, 13.41s/it, avr_loss=0.0258]
steps:  62%|██████▏   | 379/608 [1:24:41<51:10, 13.41s/it, avr_loss=0.0257]
steps:  62%|██████▎   | 380/608 [1:24:53<50:56, 13.41s/it, avr_loss=0.0257]
steps:  62%|██████▎   | 380/608 [1:24:53<50:56, 13.41s/it, avr_loss=0.0256]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/block_handover_D435_full/train_lora-000010.safetensors

epoch 11/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11

steps:  63%|██████▎   | 381/608 [1:25:08<50:43, 13.41s/it, avr_loss=0.0256]
steps:  63%|██████▎   | 381/608 [1:25:08<50:43, 13.41s/it, avr_loss=0.0256]
steps:  63%|██████▎   | 382/608 [1:25:21<50:29, 13.41s/it, avr_loss=0.0256]
steps:  63%|██████▎   | 382/608 [1:25:21<50:29, 13.41s/it, avr_loss=0.0257]
steps:  63%|██████▎   | 383/608 [1:25:34<50:16, 13.40s/it, avr_loss=0.0257]
steps:  63%|██████▎   | 383/608 [1:25:34<50:16, 13.40s/it, avr_loss=0.0256]
steps:  63%|██████▎   | 384/608 [1:25:46<50:02, 13.40s/it, avr_loss=0.0256]
steps:  63%|██████▎   | 384/608 [1:25:46<50:02, 13.40s/it, avr_loss=0.0262]
steps:  63%|██████▎   | 385/608 [1:25:59<49:48, 13.40s/it, avr_loss=0.0262]
steps:  63%|██████▎   | 385/608 [1:25:59<49:48, 13.40s/it, avr_loss=0.0246]
steps:  63%|██████▎   | 386/608 [1:26:12<49:34, 13.40s/it, avr_loss=0.0246]
steps:  63%|██████▎   | 386/608 [1:26:12<49:34, 13.40s/it, avr_loss=0.0244]
steps:  64%|██████▎   | 387/608 [1:26:24<49:20, 13.40s/it, avr_loss=0.0244]
steps:  64%|██████▎   | 387/608 [1:26:24<49:20, 13.40s/it, avr_loss=0.0243]
steps:  64%|██████▍   | 388/608 [1:26:37<49:07, 13.40s/it, avr_loss=0.0243]
steps:  64%|██████▍   | 388/608 [1:26:37<49:07, 13.40s/it, avr_loss=0.0241]
steps:  64%|██████▍   | 389/608 [1:26:50<48:53, 13.39s/it, avr_loss=0.0241]
steps:  64%|██████▍   | 389/608 [1:26:50<48:53, 13.39s/it, avr_loss=0.0239]
steps:  64%|██████▍   | 390/608 [1:27:03<48:39, 13.39s/it, avr_loss=0.0239]
steps:  64%|██████▍   | 390/608 [1:27:03<48:39, 13.39s/it, avr_loss=0.0238]
steps:  64%|██████▍   | 391/608 [1:27:16<48:25, 13.39s/it, avr_loss=0.0238]
steps:  64%|██████▍   | 391/608 [1:27:16<48:25, 13.39s/it, avr_loss=0.0235]
steps:  64%|██████▍   | 392/608 [1:27:28<48:12, 13.39s/it, avr_loss=0.0235]
steps:  64%|██████▍   | 392/608 [1:27:28<48:12, 13.39s/it, avr_loss=0.0236]
steps:  65%|██████▍   | 393/608 [1:27:41<47:58, 13.39s/it, avr_loss=0.0236]
steps:  65%|██████▍   | 393/608 [1:27:41<47:58, 13.39s/it, avr_loss=0.0235]
steps:  65%|██████▍   | 394/608 [1:27:54<47:44, 13.39s/it, avr_loss=0.0235]
steps:  65%|██████▍   | 394/608 [1:27:54<47:44, 13.39s/it, avr_loss=0.0236]
steps:  65%|██████▍   | 395/608 [1:28:07<47:30, 13.38s/it, avr_loss=0.0236]
steps:  65%|██████▍   | 395/608 [1:28:07<47:30, 13.38s/it, avr_loss=0.0255]
steps:  65%|██████▌   | 396/608 [1:28:19<47:17, 13.38s/it, avr_loss=0.0255]
steps:  65%|██████▌   | 396/608 [1:28:19<47:17, 13.38s/it, avr_loss=0.0258]
steps:  65%|██████▌   | 397/608 [1:28:32<47:03, 13.38s/it, avr_loss=0.0258]
steps:  65%|██████▌   | 397/608 [1:28:32<47:03, 13.38s/it, avr_loss=0.0259]
steps:  65%|██████▌   | 398/608 [1:28:45<46:49, 13.38s/it, avr_loss=0.0259]
steps:  65%|██████▌   | 398/608 [1:28:45<46:49, 13.38s/it, avr_loss=0.0259]
steps:  66%|██████▌   | 399/608 [1:28:58<46:36, 13.38s/it, avr_loss=0.0259]
steps:  66%|██████▌   | 399/608 [1:28:58<46:36, 13.38s/it, avr_loss=0.0227]
steps:  66%|██████▌   | 400/608 [1:29:11<46:22, 13.38s/it, avr_loss=0.0227]
steps:  66%|██████▌   | 400/608 [1:29:11<46:22, 13.38s/it, avr_loss=0.0239]
steps:  66%|██████▌   | 401/608 [1:29:23<46:08, 13.38s/it, avr_loss=0.0239]
steps:  66%|██████▌   | 401/608 [1:29:23<46:08, 13.38s/it, avr_loss=0.0238]
steps:  66%|██████▌   | 402/608 [1:29:36<45:55, 13.37s/it, avr_loss=0.0238]
steps:  66%|██████▌   | 402/608 [1:29:36<45:55, 13.37s/it, avr_loss=0.0237]
steps:  66%|██████▋   | 403/608 [1:29:49<45:41, 13.37s/it, avr_loss=0.0237]
steps:  66%|██████▋   | 403/608 [1:29:49<45:41, 13.37s/it, avr_loss=0.0235]
steps:  66%|██████▋   | 404/608 [1:30:02<45:27, 13.37s/it, avr_loss=0.0235]
steps:  66%|██████▋   | 404/608 [1:30:02<45:27, 13.37s/it, avr_loss=0.0234]
steps:  67%|██████▋   | 405/608 [1:30:14<45:14, 13.37s/it, avr_loss=0.0234]
steps:  67%|██████▋   | 405/608 [1:30:14<45:14, 13.37s/it, avr_loss=0.0235]
steps:  67%|██████▋   | 406/608 [1:30:27<45:00, 13.37s/it, avr_loss=0.0235]
steps:  67%|██████▋   | 406/608 [1:30:27<45:00, 13.37s/it, avr_loss=0.0235]
steps:  67%|██████▋   | 407/608 [1:30:40<44:46, 13.37s/it, avr_loss=0.0235]
steps:  67%|██████▋   | 407/608 [1:30:40<44:46, 13.37s/it, avr_loss=0.0236]
steps:  67%|██████▋   | 408/608 [1:30:53<44:33, 13.37s/it, avr_loss=0.0236]
steps:  67%|██████▋   | 408/608 [1:30:53<44:33, 13.37s/it, avr_loss=0.0231]
steps:  67%|██████▋   | 409/608 [1:31:05<44:19, 13.36s/it, avr_loss=0.0231]
steps:  67%|██████▋   | 409/608 [1:31:05<44:19, 13.36s/it, avr_loss=0.0231]
steps:  67%|██████▋   | 410/608 [1:31:18<44:05, 13.36s/it, avr_loss=0.0231]
steps:  67%|██████▋   | 410/608 [1:31:18<44:05, 13.36s/it, avr_loss=0.0229]
steps:  68%|██████▊   | 411/608 [1:31:31<43:52, 13.36s/it, avr_loss=0.0229]
steps:  68%|██████▊   | 411/608 [1:31:31<43:52, 13.36s/it, avr_loss=0.0234]
steps:  68%|██████▊   | 412/608 [1:31:44<43:38, 13.36s/it, avr_loss=0.0234]
steps:  68%|██████▊   | 412/608 [1:31:44<43:38, 13.36s/it, avr_loss=0.0236]
steps:  68%|██████▊   | 413/608 [1:31:56<43:24, 13.36s/it, avr_loss=0.0236]
steps:  68%|██████▊   | 413/608 [1:31:56<43:24, 13.36s/it, avr_loss=0.0247]
steps:  68%|██████▊   | 414/608 [1:32:09<43:11, 13.36s/it, avr_loss=0.0247]
steps:  68%|██████▊   | 414/608 [1:32:09<43:11, 13.36s/it, avr_loss=0.0248]
steps:  68%|██████▊   | 415/608 [1:32:22<42:57, 13.36s/it, avr_loss=0.0248]
steps:  68%|██████▊   | 415/608 [1:32:22<42:57, 13.36s/it, avr_loss=0.0246]
steps:  68%|██████▊   | 416/608 [1:32:35<42:43, 13.35s/it, avr_loss=0.0246]
steps:  68%|██████▊   | 416/608 [1:32:35<42:43, 13.35s/it, avr_loss=0.0244]
steps:  69%|██████▊   | 417/608 [1:32:47<42:30, 13.35s/it, avr_loss=0.0244]
steps:  69%|██████▊   | 417/608 [1:32:47<42:30, 13.35s/it, avr_loss=0.0243]
steps:  69%|██████▉   | 418/608 [1:33:00<42:16, 13.35s/it, avr_loss=0.0243]
steps:  69%|██████▉   | 418/608 [1:33:00<42:16, 13.35s/it, avr_loss=0.0249]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/block_handover_D435_full/train_lora-000011.safetensors

epoch 12/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12

steps:  69%|██████▉   | 419/608 [1:33:15<42:03, 13.35s/it, avr_loss=0.0249]
steps:  69%|██████▉   | 419/608 [1:33:15<42:03, 13.35s/it, avr_loss=0.025] 
steps:  69%|██████▉   | 420/608 [1:33:28<41:50, 13.35s/it, avr_loss=0.025]
steps:  69%|██████▉   | 420/608 [1:33:28<41:50, 13.35s/it, avr_loss=0.0248]
steps:  69%|██████▉   | 421/608 [1:33:41<41:36, 13.35s/it, avr_loss=0.0248]
steps:  69%|██████▉   | 421/608 [1:33:41<41:36, 13.35s/it, avr_loss=0.025] 
steps:  69%|██████▉   | 422/608 [1:33:53<41:23, 13.35s/it, avr_loss=0.025]
steps:  69%|██████▉   | 422/608 [1:33:53<41:23, 13.35s/it, avr_loss=0.0242]
steps:  70%|██████▉   | 423/608 [1:34:06<41:09, 13.35s/it, avr_loss=0.0242]
steps:  70%|██████▉   | 423/608 [1:34:06<41:09, 13.35s/it, avr_loss=0.024] 
steps:  70%|██████▉   | 424/608 [1:34:19<40:55, 13.35s/it, avr_loss=0.024]
steps:  70%|██████▉   | 424/608 [1:34:19<40:55, 13.35s/it, avr_loss=0.0243]
steps:  70%|██████▉   | 425/608 [1:34:31<40:42, 13.35s/it, avr_loss=0.0243]
steps:  70%|██████▉   | 425/608 [1:34:31<40:42, 13.35s/it, avr_loss=0.0244]
steps:  70%|███████   | 426/608 [1:34:44<40:28, 13.34s/it, avr_loss=0.0244]
steps:  70%|███████   | 426/608 [1:34:44<40:28, 13.34s/it, avr_loss=0.0246]
steps:  70%|███████   | 427/608 [1:34:57<40:15, 13.34s/it, avr_loss=0.0246]
steps:  70%|███████   | 427/608 [1:34:57<40:15, 13.34s/it, avr_loss=0.0246]
steps:  70%|███████   | 428/608 [1:35:10<40:01, 13.34s/it, avr_loss=0.0246]
steps:  70%|███████   | 428/608 [1:35:10<40:01, 13.34s/it, avr_loss=0.0248]
steps:  71%|███████   | 429/608 [1:35:23<39:47, 13.34s/it, avr_loss=0.0248]
steps:  71%|███████   | 429/608 [1:35:23<39:47, 13.34s/it, avr_loss=0.0248]
steps:  71%|███████   | 430/608 [1:35:35<39:34, 13.34s/it, avr_loss=0.0248]
steps:  71%|███████   | 430/608 [1:35:35<39:34, 13.34s/it, avr_loss=0.0246]
steps:  71%|███████   | 431/608 [1:35:48<39:20, 13.34s/it, avr_loss=0.0246]
steps:  71%|███████   | 431/608 [1:35:48<39:20, 13.34s/it, avr_loss=0.0247]
steps:  71%|███████   | 432/608 [1:36:01<39:07, 13.34s/it, avr_loss=0.0247]
steps:  71%|███████   | 432/608 [1:36:01<39:07, 13.34s/it, avr_loss=0.0246]
steps:  71%|███████   | 433/608 [1:36:14<38:53, 13.34s/it, avr_loss=0.0246]
steps:  71%|███████   | 433/608 [1:36:14<38:53, 13.34s/it, avr_loss=0.0221]
steps:  71%|███████▏  | 434/608 [1:36:26<38:40, 13.33s/it, avr_loss=0.0221]
steps:  71%|███████▏  | 434/608 [1:36:26<38:40, 13.33s/it, avr_loss=0.022] 
steps:  72%|███████▏  | 435/608 [1:36:39<38:26, 13.33s/it, avr_loss=0.022]
steps:  72%|███████▏  | 435/608 [1:36:39<38:26, 13.33s/it, avr_loss=0.0217]
steps:  72%|███████▏  | 436/608 [1:36:52<38:12, 13.33s/it, avr_loss=0.0217]
steps:  72%|███████▏  | 436/608 [1:36:52<38:12, 13.33s/it, avr_loss=0.0216]
steps:  72%|███████▏  | 437/608 [1:37:05<37:59, 13.33s/it, avr_loss=0.0216]
steps:  72%|███████▏  | 437/608 [1:37:05<37:59, 13.33s/it, avr_loss=0.0216]
steps:  72%|███████▏  | 438/608 [1:37:17<37:45, 13.33s/it, avr_loss=0.0216]
steps:  72%|███████▏  | 438/608 [1:37:17<37:45, 13.33s/it, avr_loss=0.0205]
steps:  72%|███████▏  | 439/608 [1:37:30<37:32, 13.33s/it, avr_loss=0.0205]
steps:  72%|███████▏  | 439/608 [1:37:30<37:32, 13.33s/it, avr_loss=0.0206]
steps:  72%|███████▏  | 440/608 [1:37:43<37:18, 13.33s/it, avr_loss=0.0206]
steps:  72%|███████▏  | 440/608 [1:37:43<37:18, 13.33s/it, avr_loss=0.0214]
steps:  73%|███████▎  | 441/608 [1:37:56<37:05, 13.33s/it, avr_loss=0.0214]
steps:  73%|███████▎  | 441/608 [1:37:56<37:05, 13.33s/it, avr_loss=0.0214]
steps:  73%|███████▎  | 442/608 [1:38:09<36:51, 13.32s/it, avr_loss=0.0214]
steps:  73%|███████▎  | 442/608 [1:38:09<36:51, 13.32s/it, avr_loss=0.0212]
steps:  73%|███████▎  | 443/608 [1:38:21<36:38, 13.32s/it, avr_loss=0.0212]
steps:  73%|███████▎  | 443/608 [1:38:21<36:38, 13.32s/it, avr_loss=0.0214]
steps:  73%|███████▎  | 444/608 [1:38:34<36:24, 13.32s/it, avr_loss=0.0214]
steps:  73%|███████▎  | 444/608 [1:38:34<36:24, 13.32s/it, avr_loss=0.0213]
steps:  73%|███████▎  | 445/608 [1:38:47<36:11, 13.32s/it, avr_loss=0.0213]
steps:  73%|███████▎  | 445/608 [1:38:47<36:11, 13.32s/it, avr_loss=0.0211]
steps:  73%|███████▎  | 446/608 [1:39:00<35:57, 13.32s/it, avr_loss=0.0211]
steps:  73%|███████▎  | 446/608 [1:39:00<35:57, 13.32s/it, avr_loss=0.0215]
steps:  74%|███████▎  | 447/608 [1:39:12<35:44, 13.32s/it, avr_loss=0.0215]
steps:  74%|███████▎  | 447/608 [1:39:12<35:44, 13.32s/it, avr_loss=0.0215]
steps:  74%|███████▎  | 448/608 [1:39:25<35:30, 13.32s/it, avr_loss=0.0215]
steps:  74%|███████▎  | 448/608 [1:39:25<35:30, 13.32s/it, avr_loss=0.0217]
steps:  74%|███████▍  | 449/608 [1:39:38<35:17, 13.31s/it, avr_loss=0.0217]
steps:  74%|███████▍  | 449/608 [1:39:38<35:17, 13.31s/it, avr_loss=0.0211]
steps:  74%|███████▍  | 450/608 [1:39:51<35:03, 13.31s/it, avr_loss=0.0211]
steps:  74%|███████▍  | 450/608 [1:39:51<35:03, 13.31s/it, avr_loss=0.0209]
steps:  74%|███████▍  | 451/608 [1:40:03<34:50, 13.31s/it, avr_loss=0.0209]
steps:  74%|███████▍  | 451/608 [1:40:03<34:50, 13.31s/it, avr_loss=0.0212]
steps:  74%|███████▍  | 452/608 [1:40:16<34:36, 13.31s/it, avr_loss=0.0212]
steps:  74%|███████▍  | 452/608 [1:40:16<34:36, 13.31s/it, avr_loss=0.022] 
steps:  75%|███████▍  | 453/608 [1:40:29<34:23, 13.31s/it, avr_loss=0.022]
steps:  75%|███████▍  | 453/608 [1:40:29<34:23, 13.31s/it, avr_loss=0.0219]
steps:  75%|███████▍  | 454/608 [1:40:42<34:09, 13.31s/it, avr_loss=0.0219]
steps:  75%|███████▍  | 454/608 [1:40:42<34:09, 13.31s/it, avr_loss=0.0227]
steps:  75%|███████▍  | 455/608 [1:40:55<33:56, 13.31s/it, avr_loss=0.0227]
steps:  75%|███████▍  | 455/608 [1:40:55<33:56, 13.31s/it, avr_loss=0.0233]
steps:  75%|███████▌  | 456/608 [1:41:07<33:42, 13.31s/it, avr_loss=0.0233]
steps:  75%|███████▌  | 456/608 [1:41:07<33:42, 13.31s/it, avr_loss=0.0227]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/block_handover_D435_full/train_lora-000012.safetensors

epoch 13/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13

steps:  75%|███████▌  | 457/608 [1:41:22<33:29, 13.31s/it, avr_loss=0.0227]
steps:  75%|███████▌  | 457/608 [1:41:22<33:29, 13.31s/it, avr_loss=0.0227]
steps:  75%|███████▌  | 458/608 [1:41:35<33:16, 13.31s/it, avr_loss=0.0227]
steps:  75%|███████▌  | 458/608 [1:41:35<33:16, 13.31s/it, avr_loss=0.023] 
steps:  75%|███████▌  | 459/608 [1:41:47<33:02, 13.31s/it, avr_loss=0.023]
steps:  75%|███████▌  | 459/608 [1:41:47<33:02, 13.31s/it, avr_loss=0.0229]
steps:  76%|███████▌  | 460/608 [1:42:00<32:49, 13.31s/it, avr_loss=0.0229]
steps:  76%|███████▌  | 460/608 [1:42:00<32:49, 13.31s/it, avr_loss=0.023] 
steps:  76%|███████▌  | 461/608 [1:42:13<32:35, 13.30s/it, avr_loss=0.023]
steps:  76%|███████▌  | 461/608 [1:42:13<32:35, 13.30s/it, avr_loss=0.0231]
steps:  76%|███████▌  | 462/608 [1:42:26<32:22, 13.30s/it, avr_loss=0.0231]
steps:  76%|███████▌  | 462/608 [1:42:26<32:22, 13.30s/it, avr_loss=0.023] 
steps:  76%|███████▌  | 463/608 [1:42:38<32:08, 13.30s/it, avr_loss=0.023]
steps:  76%|███████▌  | 463/608 [1:42:38<32:08, 13.30s/it, avr_loss=0.023]
steps:  76%|███████▋  | 464/608 [1:42:51<31:55, 13.30s/it, avr_loss=0.023]
steps:  76%|███████▋  | 464/608 [1:42:51<31:55, 13.30s/it, avr_loss=0.0229]
steps:  76%|███████▋  | 465/608 [1:43:04<31:41, 13.30s/it, avr_loss=0.0229]
steps:  76%|███████▋  | 465/608 [1:43:04<31:41, 13.30s/it, avr_loss=0.0232]
steps:  77%|███████▋  | 466/608 [1:43:17<31:28, 13.30s/it, avr_loss=0.0232]
steps:  77%|███████▋  | 466/608 [1:43:17<31:28, 13.30s/it, avr_loss=0.0229]
steps:  77%|███████▋  | 467/608 [1:43:29<31:14, 13.30s/it, avr_loss=0.0229]
steps:  77%|███████▋  | 467/608 [1:43:29<31:14, 13.30s/it, avr_loss=0.0229]
steps:  77%|███████▋  | 468/608 [1:43:42<31:01, 13.30s/it, avr_loss=0.0229]
steps:  77%|███████▋  | 468/608 [1:43:42<31:01, 13.30s/it, avr_loss=0.0229]
steps:  77%|███████▋  | 469/608 [1:43:55<30:48, 13.30s/it, avr_loss=0.0229]
steps:  77%|███████▋  | 469/608 [1:43:55<30:48, 13.30s/it, avr_loss=0.0226]
steps:  77%|███████▋  | 470/608 [1:44:08<30:34, 13.29s/it, avr_loss=0.0226]
steps:  77%|███████▋  | 470/608 [1:44:08<30:34, 13.29s/it, avr_loss=0.0227]
steps:  77%|███████▋  | 471/608 [1:44:20<30:21, 13.29s/it, avr_loss=0.0227]
steps:  77%|███████▋  | 471/608 [1:44:20<30:21, 13.29s/it, avr_loss=0.0228]
steps:  78%|███████▊  | 472/608 [1:44:33<30:07, 13.29s/it, avr_loss=0.0228]
steps:  78%|███████▊  | 472/608 [1:44:33<30:07, 13.29s/it, avr_loss=0.0225]
steps:  78%|███████▊  | 473/608 [1:44:46<29:54, 13.29s/it, avr_loss=0.0225]
steps:  78%|███████▊  | 473/608 [1:44:46<29:54, 13.29s/it, avr_loss=0.023] 
steps:  78%|███████▊  | 474/608 [1:44:59<29:40, 13.29s/it, avr_loss=0.023]
steps:  78%|███████▊  | 474/608 [1:44:59<29:40, 13.29s/it, avr_loss=0.0228]
steps:  78%|███████▊  | 475/608 [1:45:12<29:27, 13.29s/it, avr_loss=0.0228]
steps:  78%|███████▊  | 475/608 [1:45:12<29:27, 13.29s/it, avr_loss=0.0226]
steps:  78%|███████▊  | 476/608 [1:45:24<29:13, 13.29s/it, avr_loss=0.0226]
steps:  78%|███████▊  | 476/608 [1:45:24<29:13, 13.29s/it, avr_loss=0.0236]
steps:  78%|███████▊  | 477/608 [1:45:37<29:00, 13.29s/it, avr_loss=0.0236]
steps:  78%|███████▊  | 477/608 [1:45:37<29:00, 13.29s/it, avr_loss=0.0237]
steps:  79%|███████▊  | 478/608 [1:45:50<28:47, 13.29s/it, avr_loss=0.0237]
steps:  79%|███████▊  | 478/608 [1:45:50<28:47, 13.29s/it, avr_loss=0.0228]
steps:  79%|███████▉  | 479/608 [1:46:03<28:33, 13.28s/it, avr_loss=0.0228]
steps:  79%|███████▉  | 479/608 [1:46:03<28:33, 13.28s/it, avr_loss=0.023] 
steps:  79%|███████▉  | 480/608 [1:46:15<28:20, 13.28s/it, avr_loss=0.023]
steps:  79%|███████▉  | 480/608 [1:46:15<28:20, 13.28s/it, avr_loss=0.0232]
steps:  79%|███████▉  | 481/608 [1:46:28<28:06, 13.28s/it, avr_loss=0.0232]
steps:  79%|███████▉  | 481/608 [1:46:28<28:06, 13.28s/it, avr_loss=0.0231]
steps:  79%|███████▉  | 482/608 [1:46:41<27:53, 13.28s/it, avr_loss=0.0231]
steps:  79%|███████▉  | 482/608 [1:46:41<27:53, 13.28s/it, avr_loss=0.0233]
steps:  79%|███████▉  | 483/608 [1:46:54<27:40, 13.28s/it, avr_loss=0.0233]
steps:  79%|███████▉  | 483/608 [1:46:54<27:40, 13.28s/it, avr_loss=0.0236]
steps:  80%|███████▉  | 484/608 [1:47:07<27:26, 13.28s/it, avr_loss=0.0236]
steps:  80%|███████▉  | 484/608 [1:47:07<27:26, 13.28s/it, avr_loss=0.0233]
steps:  80%|███████▉  | 485/608 [1:47:19<27:13, 13.28s/it, avr_loss=0.0233]
steps:  80%|███████▉  | 485/608 [1:47:19<27:13, 13.28s/it, avr_loss=0.0234]
steps:  80%|███████▉  | 486/608 [1:47:32<26:59, 13.28s/it, avr_loss=0.0234]
steps:  80%|███████▉  | 486/608 [1:47:32<26:59, 13.28s/it, avr_loss=0.0233]
steps:  80%|████████  | 487/608 [1:47:45<26:46, 13.28s/it, avr_loss=0.0233]
steps:  80%|████████  | 487/608 [1:47:45<26:46, 13.28s/it, avr_loss=0.0233]
steps:  80%|████████  | 488/608 [1:47:58<26:33, 13.28s/it, avr_loss=0.0233]
steps:  80%|████████  | 488/608 [1:47:58<26:33, 13.28s/it, avr_loss=0.0234]
steps:  80%|████████  | 489/608 [1:48:11<26:19, 13.27s/it, avr_loss=0.0234]
steps:  80%|████████  | 489/608 [1:48:11<26:19, 13.27s/it, avr_loss=0.0227]
steps:  81%|████████  | 490/608 [1:48:23<26:06, 13.27s/it, avr_loss=0.0227]
steps:  81%|████████  | 490/608 [1:48:23<26:06, 13.27s/it, avr_loss=0.0218]
steps:  81%|████████  | 491/608 [1:48:36<25:52, 13.27s/it, avr_loss=0.0218]
steps:  81%|████████  | 491/608 [1:48:36<25:52, 13.27s/it, avr_loss=0.0218]
steps:  81%|████████  | 492/608 [1:48:49<25:39, 13.27s/it, avr_loss=0.0218]
steps:  81%|████████  | 492/608 [1:48:49<25:39, 13.27s/it, avr_loss=0.0209]
steps:  81%|████████  | 493/608 [1:49:02<25:26, 13.27s/it, avr_loss=0.0209]
steps:  81%|████████  | 493/608 [1:49:02<25:26, 13.27s/it, avr_loss=0.0209]
steps:  81%|████████▏ | 494/608 [1:49:15<25:12, 13.27s/it, avr_loss=0.0209]
steps:  81%|████████▏ | 494/608 [1:49:15<25:12, 13.27s/it, avr_loss=0.021] 
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/block_handover_D435_full/train_lora-000013.safetensors

epoch 14/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14

steps:  81%|████████▏ | 495/608 [1:49:29<24:59, 13.27s/it, avr_loss=0.021]
steps:  81%|████████▏ | 495/608 [1:49:29<24:59, 13.27s/it, avr_loss=0.0209]
steps:  82%|████████▏ | 496/608 [1:49:42<24:46, 13.27s/it, avr_loss=0.0209]
steps:  82%|████████▏ | 496/608 [1:49:42<24:46, 13.27s/it, avr_loss=0.0204]
steps:  82%|████████▏ | 497/608 [1:49:55<24:32, 13.27s/it, avr_loss=0.0204]
steps:  82%|████████▏ | 497/608 [1:49:55<24:32, 13.27s/it, avr_loss=0.0211]
steps:  82%|████████▏ | 498/608 [1:50:08<24:19, 13.27s/it, avr_loss=0.0211]
steps:  82%|████████▏ | 498/608 [1:50:08<24:19, 13.27s/it, avr_loss=0.0211]
steps:  82%|████████▏ | 499/608 [1:50:20<24:06, 13.27s/it, avr_loss=0.0211]
steps:  82%|████████▏ | 499/608 [1:50:20<24:06, 13.27s/it, avr_loss=0.021] 
steps:  82%|████████▏ | 500/608 [1:50:33<23:52, 13.27s/it, avr_loss=0.021]
steps:  82%|████████▏ | 500/608 [1:50:33<23:52, 13.27s/it, avr_loss=0.0209]
steps:  82%|████████▏ | 501/608 [1:50:46<23:39, 13.27s/it, avr_loss=0.0209]
steps:  82%|████████▏ | 501/608 [1:50:46<23:39, 13.27s/it, avr_loss=0.0213]
steps:  83%|████████▎ | 502/608 [1:50:58<23:26, 13.26s/it, avr_loss=0.0213]
steps:  83%|████████▎ | 502/608 [1:50:58<23:26, 13.26s/it, avr_loss=0.0213]
steps:  83%|████████▎ | 503/608 [1:51:11<23:12, 13.26s/it, avr_loss=0.0213]
steps:  83%|████████▎ | 503/608 [1:51:11<23:12, 13.26s/it, avr_loss=0.0211]
steps:  83%|████████▎ | 504/608 [1:51:24<22:59, 13.26s/it, avr_loss=0.0211]
steps:  83%|████████▎ | 504/608 [1:51:24<22:59, 13.26s/it, avr_loss=0.0212]
steps:  83%|████████▎ | 505/608 [1:51:37<22:46, 13.26s/it, avr_loss=0.0212]
steps:  83%|████████▎ | 505/608 [1:51:37<22:46, 13.26s/it, avr_loss=0.0211]
steps:  83%|████████▎ | 506/608 [1:51:50<22:32, 13.26s/it, avr_loss=0.0211]
steps:  83%|████████▎ | 506/608 [1:51:50<22:32, 13.26s/it, avr_loss=0.0212]
steps:  83%|████████▎ | 507/608 [1:52:03<22:19, 13.26s/it, avr_loss=0.0212]
steps:  83%|████████▎ | 507/608 [1:52:03<22:19, 13.26s/it, avr_loss=0.0225]
steps:  84%|████████▎ | 508/608 [1:52:15<22:05, 13.26s/it, avr_loss=0.0225]
steps:  84%|████████▎ | 508/608 [1:52:15<22:05, 13.26s/it, avr_loss=0.0227]
steps:  84%|████████▎ | 509/608 [1:52:28<21:52, 13.26s/it, avr_loss=0.0227]
steps:  84%|████████▎ | 509/608 [1:52:28<21:52, 13.26s/it, avr_loss=0.0226]
steps:  84%|████████▍ | 510/608 [1:52:41<21:39, 13.26s/it, avr_loss=0.0226]
steps:  84%|████████▍ | 510/608 [1:52:41<21:39, 13.26s/it, avr_loss=0.0227]
steps:  84%|████████▍ | 511/608 [1:52:53<21:25, 13.26s/it, avr_loss=0.0227]
steps:  84%|████████▍ | 511/608 [1:52:53<21:25, 13.26s/it, avr_loss=0.023] 
steps:  84%|████████▍ | 512/608 [1:53:06<21:12, 13.26s/it, avr_loss=0.023]
steps:  84%|████████▍ | 512/608 [1:53:06<21:12, 13.26s/it, avr_loss=0.023]
steps:  84%|████████▍ | 513/608 [1:53:19<20:59, 13.25s/it, avr_loss=0.023]
steps:  84%|████████▍ | 513/608 [1:53:19<20:59, 13.25s/it, avr_loss=0.023]
steps:  85%|████████▍ | 514/608 [1:53:32<20:45, 13.25s/it, avr_loss=0.023]
steps:  85%|████████▍ | 514/608 [1:53:32<20:45, 13.25s/it, avr_loss=0.0224]
steps:  85%|████████▍ | 515/608 [1:53:45<20:32, 13.25s/it, avr_loss=0.0224]
steps:  85%|████████▍ | 515/608 [1:53:45<20:32, 13.25s/it, avr_loss=0.0223]
steps:  85%|████████▍ | 516/608 [1:53:57<20:19, 13.25s/it, avr_loss=0.0223]
steps:  85%|████████▍ | 516/608 [1:53:57<20:19, 13.25s/it, avr_loss=0.0223]
steps:  85%|████████▌ | 517/608 [1:54:10<20:05, 13.25s/it, avr_loss=0.0223]
steps:  85%|████████▌ | 517/608 [1:54:10<20:05, 13.25s/it, avr_loss=0.0221]
steps:  85%|████████▌ | 518/608 [1:54:23<19:52, 13.25s/it, avr_loss=0.0221]
steps:  85%|████████▌ | 518/608 [1:54:23<19:52, 13.25s/it, avr_loss=0.0219]
steps:  85%|████████▌ | 519/608 [1:54:36<19:39, 13.25s/it, avr_loss=0.0219]
steps:  85%|████████▌ | 519/608 [1:54:36<19:39, 13.25s/it, avr_loss=0.0219]
steps:  86%|████████▌ | 520/608 [1:54:49<19:25, 13.25s/it, avr_loss=0.0219]
steps:  86%|████████▌ | 520/608 [1:54:49<19:25, 13.25s/it, avr_loss=0.0217]
steps:  86%|████████▌ | 521/608 [1:55:01<19:12, 13.25s/it, avr_loss=0.0217]
steps:  86%|████████▌ | 521/608 [1:55:01<19:12, 13.25s/it, avr_loss=0.0214]
steps:  86%|████████▌ | 522/608 [1:55:14<18:59, 13.25s/it, avr_loss=0.0214]
steps:  86%|████████▌ | 522/608 [1:55:14<18:59, 13.25s/it, avr_loss=0.0215]
steps:  86%|████████▌ | 523/608 [1:55:27<18:45, 13.25s/it, avr_loss=0.0215]
steps:  86%|████████▌ | 523/608 [1:55:27<18:45, 13.25s/it, avr_loss=0.0215]
steps:  86%|████████▌ | 524/608 [1:55:40<18:32, 13.24s/it, avr_loss=0.0215]
steps:  86%|████████▌ | 524/608 [1:55:40<18:32, 13.24s/it, avr_loss=0.0214]
steps:  86%|████████▋ | 525/608 [1:55:53<18:19, 13.24s/it, avr_loss=0.0214]
steps:  86%|████████▋ | 525/608 [1:55:53<18:19, 13.24s/it, avr_loss=0.0215]
steps:  87%|████████▋ | 526/608 [1:56:05<18:05, 13.24s/it, avr_loss=0.0215]
steps:  87%|████████▋ | 526/608 [1:56:05<18:05, 13.24s/it, avr_loss=0.0213]
steps:  87%|████████▋ | 527/608 [1:56:18<17:52, 13.24s/it, avr_loss=0.0213]
steps:  87%|████████▋ | 527/608 [1:56:18<17:52, 13.24s/it, avr_loss=0.0205]
steps:  87%|████████▋ | 528/608 [1:56:31<17:39, 13.24s/it, avr_loss=0.0205]
steps:  87%|████████▋ | 528/608 [1:56:31<17:39, 13.24s/it, avr_loss=0.0209]
steps:  87%|████████▋ | 529/608 [1:56:43<17:25, 13.24s/it, avr_loss=0.0209]
steps:  87%|████████▋ | 529/608 [1:56:43<17:25, 13.24s/it, avr_loss=0.0209]
steps:  87%|████████▋ | 530/608 [1:56:56<17:12, 13.24s/it, avr_loss=0.0209]
steps:  87%|████████▋ | 530/608 [1:56:56<17:12, 13.24s/it, avr_loss=0.021] 
steps:  87%|████████▋ | 531/608 [1:57:09<16:59, 13.24s/it, avr_loss=0.021]
steps:  87%|████████▋ | 531/608 [1:57:09<16:59, 13.24s/it, avr_loss=0.0212]
steps:  88%|████████▊ | 532/608 [1:57:22<16:46, 13.24s/it, avr_loss=0.0212]
steps:  88%|████████▊ | 532/608 [1:57:22<16:46, 13.24s/it, avr_loss=0.0212]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/block_handover_D435_full/train_lora-000014.safetensors

epoch 15/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15

steps:  88%|████████▊ | 533/608 [1:57:36<16:32, 13.24s/it, avr_loss=0.0212]
steps:  88%|████████▊ | 533/608 [1:57:36<16:32, 13.24s/it, avr_loss=0.0214]
steps:  88%|████████▊ | 534/608 [1:57:49<16:19, 13.24s/it, avr_loss=0.0214]
steps:  88%|████████▊ | 534/608 [1:57:49<16:19, 13.24s/it, avr_loss=0.0214]
steps:  88%|████████▊ | 535/608 [1:58:02<16:06, 13.24s/it, avr_loss=0.0214]
steps:  88%|████████▊ | 535/608 [1:58:02<16:06, 13.24s/it, avr_loss=0.0206]
steps:  88%|████████▊ | 536/608 [1:58:14<15:53, 13.24s/it, avr_loss=0.0206]
steps:  88%|████████▊ | 536/608 [1:58:14<15:53, 13.24s/it, avr_loss=0.0206]
steps:  88%|████████▊ | 537/608 [1:58:27<15:39, 13.24s/it, avr_loss=0.0206]
steps:  88%|████████▊ | 537/608 [1:58:27<15:39, 13.24s/it, avr_loss=0.0211]
steps:  88%|████████▊ | 538/608 [1:58:40<15:26, 13.23s/it, avr_loss=0.0211]
steps:  88%|████████▊ | 538/608 [1:58:40<15:26, 13.23s/it, avr_loss=0.021] 
steps:  89%|████████▊ | 539/608 [1:58:53<15:13, 13.23s/it, avr_loss=0.021]
steps:  89%|████████▊ | 539/608 [1:58:53<15:13, 13.23s/it, avr_loss=0.0206]
steps:  89%|████████▉ | 540/608 [1:59:05<14:59, 13.23s/it, avr_loss=0.0206]
steps:  89%|████████▉ | 540/608 [1:59:05<14:59, 13.23s/it, avr_loss=0.0206]
steps:  89%|████████▉ | 541/608 [1:59:18<14:46, 13.23s/it, avr_loss=0.0206]
steps:  89%|████████▉ | 541/608 [1:59:18<14:46, 13.23s/it, avr_loss=0.0212]
steps:  89%|████████▉ | 542/608 [1:59:31<14:33, 13.23s/it, avr_loss=0.0212]
steps:  89%|████████▉ | 542/608 [1:59:31<14:33, 13.23s/it, avr_loss=0.0223]
steps:  89%|████████▉ | 543/608 [1:59:44<14:19, 13.23s/it, avr_loss=0.0223]
steps:  89%|████████▉ | 543/608 [1:59:44<14:19, 13.23s/it, avr_loss=0.0224]
steps:  89%|████████▉ | 544/608 [1:59:57<14:06, 13.23s/it, avr_loss=0.0224]
steps:  89%|████████▉ | 544/608 [1:59:57<14:06, 13.23s/it, avr_loss=0.0222]
steps:  90%|████████▉ | 545/608 [2:00:09<13:53, 13.23s/it, avr_loss=0.0222]
steps:  90%|████████▉ | 545/608 [2:00:09<13:53, 13.23s/it, avr_loss=0.0211]
steps:  90%|████████▉ | 546/608 [2:00:22<13:40, 13.23s/it, avr_loss=0.0211]
steps:  90%|████████▉ | 546/608 [2:00:22<13:40, 13.23s/it, avr_loss=0.0209]
steps:  90%|████████▉ | 547/608 [2:00:35<13:26, 13.23s/it, avr_loss=0.0209]
steps:  90%|████████▉ | 547/608 [2:00:35<13:26, 13.23s/it, avr_loss=0.021] 
steps:  90%|█████████ | 548/608 [2:00:47<13:13, 13.23s/it, avr_loss=0.021]
steps:  90%|█████████ | 548/608 [2:00:47<13:13, 13.23s/it, avr_loss=0.0212]
steps:  90%|█████████ | 549/608 [2:01:00<13:00, 13.23s/it, avr_loss=0.0212]
steps:  90%|█████████ | 549/608 [2:01:00<13:00, 13.23s/it, avr_loss=0.0204]
steps:  90%|█████████ | 550/608 [2:01:13<12:47, 13.22s/it, avr_loss=0.0204]
steps:  90%|█████████ | 550/608 [2:01:13<12:47, 13.22s/it, avr_loss=0.0206]
steps:  91%|█████████ | 551/608 [2:01:26<12:33, 13.22s/it, avr_loss=0.0206]
steps:  91%|█████████ | 551/608 [2:01:26<12:33, 13.22s/it, avr_loss=0.0217]
steps:  91%|█████████ | 552/608 [2:01:39<12:20, 13.22s/it, avr_loss=0.0217]
steps:  91%|█████████ | 552/608 [2:01:39<12:20, 13.22s/it, avr_loss=0.0214]
steps:  91%|█████████ | 553/608 [2:01:51<12:07, 13.22s/it, avr_loss=0.0214]
steps:  91%|█████████ | 553/608 [2:01:51<12:07, 13.22s/it, avr_loss=0.0213]
steps:  91%|█████████ | 554/608 [2:02:04<11:53, 13.22s/it, avr_loss=0.0213]
steps:  91%|█████████ | 554/608 [2:02:04<11:53, 13.22s/it, avr_loss=0.0213]
steps:  91%|█████████▏| 555/608 [2:02:17<11:40, 13.22s/it, avr_loss=0.0213]
steps:  91%|█████████▏| 555/608 [2:02:17<11:40, 13.22s/it, avr_loss=0.0213]
steps:  91%|█████████▏| 556/608 [2:02:30<11:27, 13.22s/it, avr_loss=0.0213]
steps:  91%|█████████▏| 556/608 [2:02:30<11:27, 13.22s/it, avr_loss=0.0212]
steps:  92%|█████████▏| 557/608 [2:02:42<11:14, 13.22s/it, avr_loss=0.0212]
steps:  92%|█████████▏| 557/608 [2:02:42<11:14, 13.22s/it, avr_loss=0.0215]
steps:  92%|█████████▏| 558/608 [2:02:55<11:00, 13.22s/it, avr_loss=0.0215]
steps:  92%|█████████▏| 558/608 [2:02:55<11:00, 13.22s/it, avr_loss=0.0214]
steps:  92%|█████████▏| 559/608 [2:03:08<10:47, 13.22s/it, avr_loss=0.0214]
steps:  92%|█████████▏| 559/608 [2:03:08<10:47, 13.22s/it, avr_loss=0.0214]
steps:  92%|█████████▏| 560/608 [2:03:21<10:34, 13.22s/it, avr_loss=0.0214]
steps:  92%|█████████▏| 560/608 [2:03:21<10:34, 13.22s/it, avr_loss=0.0213]
steps:  92%|█████████▏| 561/608 [2:03:33<10:21, 13.22s/it, avr_loss=0.0213]
steps:  92%|█████████▏| 561/608 [2:03:33<10:21, 13.22s/it, avr_loss=0.0213]
steps:  92%|█████████▏| 562/608 [2:03:46<10:07, 13.21s/it, avr_loss=0.0213]
steps:  92%|█████████▏| 562/608 [2:03:46<10:07, 13.21s/it, avr_loss=0.0214]
steps:  93%|█████████▎| 563/608 [2:03:59<09:54, 13.21s/it, avr_loss=0.0214]
steps:  93%|█████████▎| 563/608 [2:03:59<09:54, 13.21s/it, avr_loss=0.0214]
steps:  93%|█████████▎| 564/608 [2:04:12<09:41, 13.21s/it, avr_loss=0.0214]
steps:  93%|█████████▎| 564/608 [2:04:12<09:41, 13.21s/it, avr_loss=0.0214]
steps:  93%|█████████▎| 565/608 [2:04:25<09:28, 13.21s/it, avr_loss=0.0214]
steps:  93%|█████████▎| 565/608 [2:04:25<09:28, 13.21s/it, avr_loss=0.0214]
steps:  93%|█████████▎| 566/608 [2:04:37<09:14, 13.21s/it, avr_loss=0.0214]
steps:  93%|█████████▎| 566/608 [2:04:37<09:14, 13.21s/it, avr_loss=0.0209]
steps:  93%|█████████▎| 567/608 [2:04:50<09:01, 13.21s/it, avr_loss=0.0209]
steps:  93%|█████████▎| 567/608 [2:04:50<09:01, 13.21s/it, avr_loss=0.0209]
steps:  93%|█████████▎| 568/608 [2:05:03<08:48, 13.21s/it, avr_loss=0.0209]
steps:  93%|█████████▎| 568/608 [2:05:03<08:48, 13.21s/it, avr_loss=0.0208]
steps:  94%|█████████▎| 569/608 [2:05:16<08:35, 13.21s/it, avr_loss=0.0208]
steps:  94%|█████████▎| 569/608 [2:05:16<08:35, 13.21s/it, avr_loss=0.0203]
steps:  94%|█████████▍| 570/608 [2:05:29<08:21, 13.21s/it, avr_loss=0.0203]
steps:  94%|█████████▍| 570/608 [2:05:29<08:21, 13.21s/it, avr_loss=0.02]  
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/block_handover_D435_full/train_lora-000015.safetensors

epoch 16/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16

steps:  94%|█████████▍| 571/608 [2:05:43<08:08, 13.21s/it, avr_loss=0.02]
steps:  94%|█████████▍| 571/608 [2:05:43<08:08, 13.21s/it, avr_loss=0.0203]
steps:  94%|█████████▍| 572/608 [2:05:56<07:55, 13.21s/it, avr_loss=0.0203]
steps:  94%|█████████▍| 572/608 [2:05:56<07:55, 13.21s/it, avr_loss=0.0203]
steps:  94%|█████████▍| 573/608 [2:06:09<07:42, 13.21s/it, avr_loss=0.0203]
steps:  94%|█████████▍| 573/608 [2:06:09<07:42, 13.21s/it, avr_loss=0.0202]
steps:  94%|█████████▍| 574/608 [2:06:22<07:29, 13.21s/it, avr_loss=0.0202]
steps:  94%|█████████▍| 574/608 [2:06:22<07:29, 13.21s/it, avr_loss=0.0202]
steps:  95%|█████████▍| 575/608 [2:06:34<07:15, 13.21s/it, avr_loss=0.0202]
steps:  95%|█████████▍| 575/608 [2:06:34<07:15, 13.21s/it, avr_loss=0.0202]
steps:  95%|█████████▍| 576/608 [2:06:47<07:02, 13.21s/it, avr_loss=0.0202]
steps:  95%|█████████▍| 576/608 [2:06:47<07:02, 13.21s/it, avr_loss=0.0204]
steps:  95%|█████████▍| 577/608 [2:07:00<06:49, 13.21s/it, avr_loss=0.0204]
steps:  95%|█████████▍| 577/608 [2:07:00<06:49, 13.21s/it, avr_loss=0.0204]
steps:  95%|█████████▌| 578/608 [2:07:13<06:36, 13.21s/it, avr_loss=0.0204]
steps:  95%|█████████▌| 578/608 [2:07:13<06:36, 13.21s/it, avr_loss=0.0203]
steps:  95%|█████████▌| 579/608 [2:07:25<06:22, 13.21s/it, avr_loss=0.0203]
steps:  95%|█████████▌| 579/608 [2:07:25<06:22, 13.21s/it, avr_loss=0.0199]
steps:  95%|█████████▌| 580/608 [2:07:38<06:09, 13.20s/it, avr_loss=0.0199]
steps:  95%|█████████▌| 580/608 [2:07:38<06:09, 13.20s/it, avr_loss=0.0188]
steps:  96%|█████████▌| 581/608 [2:07:51<05:56, 13.20s/it, avr_loss=0.0188]
steps:  96%|█████████▌| 581/608 [2:07:51<05:56, 13.20s/it, avr_loss=0.0187]
steps:  96%|█████████▌| 582/608 [2:08:04<05:43, 13.20s/it, avr_loss=0.0187]
steps:  96%|█████████▌| 582/608 [2:08:04<05:43, 13.20s/it, avr_loss=0.0186]
steps:  96%|█████████▌| 583/608 [2:08:17<05:30, 13.20s/it, avr_loss=0.0186]
steps:  96%|█████████▌| 583/608 [2:08:17<05:30, 13.20s/it, avr_loss=0.0185]
steps:  96%|█████████▌| 584/608 [2:08:29<05:16, 13.20s/it, avr_loss=0.0185]
steps:  96%|█████████▌| 584/608 [2:08:29<05:16, 13.20s/it, avr_loss=0.0186]
steps:  96%|█████████▌| 585/608 [2:08:42<05:03, 13.20s/it, avr_loss=0.0186]
steps:  96%|█████████▌| 585/608 [2:08:42<05:03, 13.20s/it, avr_loss=0.0186]
steps:  96%|█████████▋| 586/608 [2:08:55<04:50, 13.20s/it, avr_loss=0.0186]
steps:  96%|█████████▋| 586/608 [2:08:55<04:50, 13.20s/it, avr_loss=0.0205]
steps:  97%|█████████▋| 587/608 [2:09:08<04:37, 13.20s/it, avr_loss=0.0205]
steps:  97%|█████████▋| 587/608 [2:09:08<04:37, 13.20s/it, avr_loss=0.0205]
steps:  97%|█████████▋| 588/608 [2:09:21<04:23, 13.20s/it, avr_loss=0.0205]
steps:  97%|█████████▋| 588/608 [2:09:21<04:23, 13.20s/it, avr_loss=0.0202]
steps:  97%|█████████▋| 589/608 [2:09:33<04:10, 13.20s/it, avr_loss=0.0202]
steps:  97%|█████████▋| 589/608 [2:09:34<04:10, 13.20s/it, avr_loss=0.019] 
steps:  97%|█████████▋| 590/608 [2:09:46<03:57, 13.20s/it, avr_loss=0.019]
steps:  97%|█████████▋| 590/608 [2:09:46<03:57, 13.20s/it, avr_loss=0.019]
steps:  97%|█████████▋| 591/608 [2:09:59<03:44, 13.20s/it, avr_loss=0.019]
steps:  97%|█████████▋| 591/608 [2:09:59<03:44, 13.20s/it, avr_loss=0.0193]
steps:  97%|█████████▋| 592/608 [2:10:12<03:31, 13.20s/it, avr_loss=0.0193]
steps:  97%|█████████▋| 592/608 [2:10:12<03:31, 13.20s/it, avr_loss=0.0195]
steps:  98%|█████████▊| 593/608 [2:10:25<03:17, 13.20s/it, avr_loss=0.0195]
steps:  98%|█████████▊| 593/608 [2:10:25<03:17, 13.20s/it, avr_loss=0.0194]
steps:  98%|█████████▊| 594/608 [2:10:37<03:04, 13.20s/it, avr_loss=0.0194]
steps:  98%|█████████▊| 594/608 [2:10:37<03:04, 13.20s/it, avr_loss=0.0212]
steps:  98%|█████████▊| 595/608 [2:10:50<02:51, 13.19s/it, avr_loss=0.0212]
steps:  98%|█████████▊| 595/608 [2:10:50<02:51, 13.19s/it, avr_loss=0.0209]
steps:  98%|█████████▊| 596/608 [2:11:03<02:38, 13.19s/it, avr_loss=0.0209]
steps:  98%|█████████▊| 596/608 [2:11:03<02:38, 13.19s/it, avr_loss=0.0214]
steps:  98%|█████████▊| 597/608 [2:11:16<02:25, 13.19s/it, avr_loss=0.0214]
steps:  98%|█████████▊| 597/608 [2:11:16<02:25, 13.19s/it, avr_loss=0.0214]
steps:  98%|█████████▊| 598/608 [2:11:29<02:11, 13.19s/it, avr_loss=0.0214]
steps:  98%|█████████▊| 598/608 [2:11:29<02:11, 13.19s/it, avr_loss=0.0213]
steps:  99%|█████████▊| 599/608 [2:11:41<01:58, 13.19s/it, avr_loss=0.0213]
steps:  99%|█████████▊| 599/608 [2:11:41<01:58, 13.19s/it, avr_loss=0.0214]
steps:  99%|█████████▊| 600/608 [2:11:54<01:45, 13.19s/it, avr_loss=0.0214]
steps:  99%|█████████▊| 600/608 [2:11:54<01:45, 13.19s/it, avr_loss=0.0213]
steps:  99%|█████████▉| 601/608 [2:12:07<01:32, 13.19s/it, avr_loss=0.0213]
steps:  99%|█████████▉| 601/608 [2:12:07<01:32, 13.19s/it, avr_loss=0.0212]
steps:  99%|█████████▉| 602/608 [2:12:20<01:19, 13.19s/it, avr_loss=0.0212]
steps:  99%|█████████▉| 602/608 [2:12:20<01:19, 13.19s/it, avr_loss=0.0212]
steps:  99%|█████████▉| 603/608 [2:12:32<01:05, 13.19s/it, avr_loss=0.0212]
steps:  99%|█████████▉| 603/608 [2:12:32<01:05, 13.19s/it, avr_loss=0.022] 
steps:  99%|█████████▉| 604/608 [2:12:45<00:52, 13.19s/it, avr_loss=0.022]
steps:  99%|█████████▉| 604/608 [2:12:45<00:52, 13.19s/it, avr_loss=0.0222]
steps: 100%|█████████▉| 605/608 [2:12:58<00:39, 13.19s/it, avr_loss=0.0222]
steps: 100%|█████████▉| 605/608 [2:12:58<00:39, 13.19s/it, avr_loss=0.0222]
steps: 100%|█████████▉| 606/608 [2:13:11<00:26, 13.19s/it, avr_loss=0.0222]
steps: 100%|█████████▉| 606/608 [2:13:11<00:26, 13.19s/it, avr_loss=0.0222]
steps: 100%|█████████▉| 607/608 [2:13:23<00:13, 13.19s/it, avr_loss=0.0222]
steps: 100%|█████████▉| 607/608 [2:13:23<00:13, 13.19s/it, avr_loss=0.0222]
steps: 100%|██████████| 608/608 [2:13:36<00:00, 13.19s/it, avr_loss=0.0222]
steps: 100%|██████████| 608/608 [2:13:36<00:00, 13.19s/it, avr_loss=0.0223]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/block_handover_D435_full/train_lora.safetensors
INFO:hv_train_network:model saved.

steps: 100%|██████████| 608/608 [2:13:38<00:00, 13.19s/it, avr_loss=0.0223]
