nohup: ignoring input
The following values were not passed to `accelerate launch` and had defaults used instead:
	`--num_machines` was set to a value of `1`
	`--mixed_precision` was set to a value of `'no'`
	`--dynamo_backend` was set to a value of `'no'`
To avoid this warning pass in values for each of the problematic parameters or run `accelerate config`.
Xformers is not installed!
Flash Attn is not installed!
Xformers is not installed!
Sage Attn is not installed!
Flash Attn is not installed!
Sage Attn is not installed!
Trying to import sageattention
Failed to import sageattention
Trying to import sageattention
Failed to import sageattention
Xformers is not installed!
Flash Attn is not installed!
Sage Attn is not installed!
Xformers is not installed!
Flash Attn is not installed!
Sage Attn is not installed!
Xformers is not installed!
Flash Attn is not installed!
Sage Attn is not installed!
Xformers is not installed!
Flash Attn is not installed!
Sage Attn is not installed!
Trying to import sageattention
Failed to import sageattention
Trying to import sageattention
Failed to import sageattention
Trying to import sageattention
Failed to import sageattention
Trying to import sageattention
Failed to import sageattention
INFO:hv_train_network:Load dataset config from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_config_10.toml
INFO:hv_train_network:Load dataset config from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_config_10.toml
INFO:hv_train_network:Load dataset config from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_config_10.toml
INFO:hv_train_network:Load dataset config from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_config_10.toml
INFO:hv_train_network:Load dataset config from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_config_10.toml
INFO:dataset.image_video_dataset:load video jsonl from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata_10.jsonl
INFO:dataset.image_video_dataset:load video jsonl from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata_10.jsonl
INFO:dataset.image_video_dataset:load video jsonl from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata_10.jsonl
INFO:dataset.image_video_dataset:load video jsonl from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata_10.jsonl
INFO:dataset.image_video_dataset:load video jsonl from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata_10.jsonl
INFO:dataset.image_video_dataset:loaded 100 videos
INFO:dataset.image_video_dataset:loaded 100 videos
INFO:dataset.image_video_dataset:loaded 100 videos
INFO:dataset.image_video_dataset:loaded 100 videos
INFO:dataset.config_utils:[Dataset 0]
  is_image_dataset: False
  resolution: (768, 512)
  batch_size: 1
  num_repeats: 1
  caption_extension: "None"
  enable_bucket: True
  bucket_no_upscale: False
  cache_directory: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/cache_10"
  debug_dataset: False
    video_directory: "None"
    video_jsonl_file: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata_10.jsonl"
    control_directory: "None"
    target_frames: (1,)
    frame_extraction: full
    frame_stride: 1
    frame_sample: 1
    max_frames: 129
    source_fps: 30.0


INFO:dataset.config_utils:[Dataset 0]
  is_image_dataset: False
  resolution: (768, 512)
  batch_size: 1
  num_repeats: 1
  caption_extension: "None"
  enable_bucket: True
  bucket_no_upscale: False
  cache_directory: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/cache_10"
  debug_dataset: False
    video_directory: "None"
    video_jsonl_file: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata_10.jsonl"
    control_directory: "None"
    target_frames: (1,)
    frame_extraction: full
    frame_stride: 1
    frame_sample: 1
    max_frames: 129
    source_fps: 30.0


INFO:dataset.config_utils:[Dataset 0]
  is_image_dataset: False
  resolution: (768, 512)
  batch_size: 1
  num_repeats: 1
  caption_extension: "None"
  enable_bucket: True
  bucket_no_upscale: False
  cache_directory: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/cache_10"
  debug_dataset: False
    video_directory: "None"
    video_jsonl_file: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata_10.jsonl"
    control_directory: "None"
    target_frames: (1,)
    frame_extraction: full
    frame_stride: 1
    frame_sample: 1
    max_frames: 129
    source_fps: 30.0


INFO:dataset.config_utils:[Dataset 0]
  is_image_dataset: False
  resolution: (768, 512)
  batch_size: 1
  num_repeats: 1
  caption_extension: "None"
  enable_bucket: True
  bucket_no_upscale: False
  cache_directory: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/cache_10"
  debug_dataset: False
    video_directory: "None"
    video_jsonl_file: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata_10.jsonl"
    control_directory: "None"
    target_frames: (1,)
    frame_extraction: full
    frame_stride: 1
    frame_sample: 1
    max_frames: 129
    source_fps: 30.0


INFO:hv_train_network:Load dataset config from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_config_10.toml
INFO:dataset.image_video_dataset:loaded 100 videos
INFO:dataset.config_utils:[Dataset 0]
  is_image_dataset: False
  resolution: (768, 512)
  batch_size: 1
  num_repeats: 1
  caption_extension: "None"
  enable_bucket: True
  bucket_no_upscale: False
  cache_directory: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/cache_10"
  debug_dataset: False
    video_directory: "None"
    video_jsonl_file: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata_10.jsonl"
    control_directory: "None"
    target_frames: (1,)
    frame_extraction: full
    frame_stride: 1
    frame_sample: 1
    max_frames: 129
    source_fps: 30.0


INFO:dataset.image_video_dataset:load video jsonl from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata_10.jsonl
INFO:dataset.image_video_dataset:bucket: (768, 512, 41), count: 100
INFO:dataset.image_video_dataset:bucket: (768, 512, 41), count: 100
INFO:dataset.image_video_dataset:total batches: 100
INFO:dataset.image_video_dataset:bucket: (768, 512, 41), count: 100
INFO:dataset.image_video_dataset:bucket: (768, 512, 41), count: 100
INFO:dataset.image_video_dataset:total batches: 100
INFO:dataset.image_video_dataset:total batches: 100
INFO:dataset.image_video_dataset:total batches: 100
INFO:dataset.image_video_dataset:bucket: (768, 512, 41), count: 100
INFO:dataset.image_video_dataset:loaded 100 videos
INFO:dataset.image_video_dataset:total batches: 100
INFO:dataset.config_utils:[Dataset 0]
  is_image_dataset: False
  resolution: (768, 512)
  batch_size: 1
  num_repeats: 1
  caption_extension: "None"
  enable_bucket: True
  bucket_no_upscale: False
  cache_directory: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/cache_10"
  debug_dataset: False
    video_directory: "None"
    video_jsonl_file: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata_10.jsonl"
    control_directory: "None"
    target_frames: (1,)
    frame_extraction: full
    frame_stride: 1
    frame_sample: 1
    max_frames: 129
    source_fps: 30.0


INFO:dataset.image_video_dataset:bucket: (768, 512, 41), count: 100
INFO:dataset.image_video_dataset:total batches: 100
INFO:hv_train_network:preparing accelerator
INFO:hv_train_network:preparing accelerator
INFO:hv_train_network:preparing accelerator
INFO:hv_train_network:preparing accelerator
INFO:hv_train_network:preparing accelerator
INFO:hv_train_network:preparing accelerator
accelerator device: cuda:0
INFO:hv_train_network:DiT precision: torch.bfloat16, weight precision: torch.bfloat16
INFO:hv_train_network:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:__main__:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:frame_pack.hunyuan_video_packed:Creating HunyuanVideoTransformer3DModelPacked
INFO:frame_pack.hunyuan_video_packed:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, device=cuda:0
accelerator device: cuda:1
INFO:hv_train_network:DiT precision: torch.bfloat16, weight precision: torch.bfloat16
INFO:hv_train_network:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:__main__:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:frame_pack.hunyuan_video_packed:Creating HunyuanVideoTransformer3DModelPacked
accelerator device: cuda:4
INFO:hv_train_network:DiT precision: torch.bfloat16, weight precision: torch.bfloat16
INFO:hv_train_network:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:__main__:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:frame_pack.hunyuan_video_packed:Creating HunyuanVideoTransformer3DModelPacked
accelerator device: cuda:5
INFO:hv_train_network:DiT precision: torch.bfloat16, weight precision: torch.bfloat16
INFO:hv_train_network:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:__main__:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:frame_pack.hunyuan_video_packed:Creating HunyuanVideoTransformer3DModelPacked
accelerator device: cuda:3
INFO:hv_train_network:DiT precision: torch.bfloat16, weight precision: torch.bfloat16
INFO:hv_train_network:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:__main__:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:frame_pack.hunyuan_video_packed:Creating HunyuanVideoTransformer3DModelPacked
accelerator device: cuda:2
INFO:hv_train_network:DiT precision: torch.bfloat16, weight precision: torch.bfloat16
INFO:hv_train_network:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:__main__:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:frame_pack.hunyuan_video_packed:Creating HunyuanVideoTransformer3DModelPacked
INFO:frame_pack.hunyuan_video_packed:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, device=cuda:1
INFO:frame_pack.hunyuan_video_packed:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, device=cuda:4
INFO:frame_pack.hunyuan_video_packed:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, device=cuda:5
INFO:frame_pack.hunyuan_video_packed:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, device=cuda:3
INFO:frame_pack.hunyuan_video_packed:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, device=cuda:2
INFO:frame_pack.hunyuan_video_packed:Loaded DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, info=<All keys matched successfully>
INFO:networks.lora:create LoRA network. base dim (rank): 32, alpha: 1
INFO:networks.lora:neuron dropout: p=None, rank dropout: p=None, module dropout: p=None
INFO:frame_pack.hunyuan_video_packed:Loaded DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, info=<All keys matched successfully>
import network module: networks.lora_framepack
INFO:networks.lora:create LoRA network. base dim (rank): 32, alpha: 1
INFO:networks.lora:neuron dropout: p=None, rank dropout: p=None, module dropout: p=None
INFO:frame_pack.hunyuan_video_packed:Loaded DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, info=<All keys matched successfully>
INFO:networks.lora:create LoRA network. base dim (rank): 32, alpha: 1
INFO:networks.lora:neuron dropout: p=None, rank dropout: p=None, module dropout: p=None
INFO:frame_pack.hunyuan_video_packed:Loaded DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, info=<All keys matched successfully>
INFO:networks.lora:create LoRA network. base dim (rank): 32, alpha: 1
INFO:networks.lora:neuron dropout: p=None, rank dropout: p=None, module dropout: p=None
INFO:frame_pack.hunyuan_video_packed:Loaded DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, info=<All keys matched successfully>
INFO:networks.lora:create LoRA network. base dim (rank): 32, alpha: 1
INFO:networks.lora:neuron dropout: p=None, rank dropout: p=None, module dropout: p=None
INFO:frame_pack.hunyuan_video_packed:Loaded DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, info=<All keys matched successfully>
INFO:networks.lora:create LoRA network. base dim (rank): 32, alpha: 1
INFO:networks.lora:neuron dropout: p=None, rank dropout: p=None, module dropout: p=None
INFO:networks.lora:create LoRA for U-Net/DiT: 440 modules.
INFO:networks.lora:enable LoRA for U-Net: 440 modules
Gradient checkpointing enabled for HunyuanVideoTransformer3DModelPacked.
INFO:hv_train_network:use 8-bit AdamW optimizer | {}
INFO:networks.lora:create LoRA for U-Net/DiT: 440 modules.
INFO:networks.lora:enable LoRA for U-Net: 440 modules
Gradient checkpointing enabled for HunyuanVideoTransformer3DModelPacked.
prepare optimizer, data loader etc.
INFO:hv_train_network:use 8-bit AdamW optimizer | {}
override steps. steps for 16 epochs is / 指定エポックまでのステップ数: 272
INFO:networks.lora:create LoRA for U-Net/DiT: 440 modules.
INFO:networks.lora:enable LoRA for U-Net: 440 modules
Gradient checkpointing enabled for HunyuanVideoTransformer3DModelPacked.
INFO:hv_train_network:use 8-bit AdamW optimizer | {}
INFO:networks.lora:create LoRA for U-Net/DiT: 440 modules.
INFO:networks.lora:enable LoRA for U-Net: 440 modules
Gradient checkpointing enabled for HunyuanVideoTransformer3DModelPacked.
INFO:hv_train_network:use 8-bit AdamW optimizer | {}
INFO:networks.lora:create LoRA for U-Net/DiT: 440 modules.
INFO:networks.lora:enable LoRA for U-Net: 440 modules
Gradient checkpointing enabled for HunyuanVideoTransformer3DModelPacked.
INFO:hv_train_network:use 8-bit AdamW optimizer | {}
INFO:networks.lora:create LoRA for U-Net/DiT: 440 modules.
INFO:networks.lora:enable LoRA for U-Net: 440 modules
Gradient checkpointing enabled for HunyuanVideoTransformer3DModelPacked.
INFO:hv_train_network:use 8-bit AdamW optimizer | {}
INFO:hv_train_network:set DiT model name for metadata: /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:hv_train_network:set VAE model name for metadata: /media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/vae/diffusion_pytorch_model.safetensors
INFO:hv_train_network:set DiT model name for metadata: /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:hv_train_network:set DiT model name for metadata: /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:hv_train_network:DiT dtype: torch.bfloat16, device: cuda:5
running training / 学習開始
INFO:hv_train_network:set VAE model name for metadata: /media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/vae/diffusion_pytorch_model.safetensors
  num train items / 学習画像、動画数: 100
INFO:hv_train_network:set VAE model name for metadata: /media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/vae/diffusion_pytorch_model.safetensors
  num batches per epoch / 1epochのバッチ数: 17
  num epochs / epoch数: 16
  batch size per device / バッチサイズ: 1
  gradient accumulation steps / 勾配を合計するステップ数 = 1
  total optimization steps / 学習ステップ数: 272
INFO:hv_train_network:set DiT model name for metadata: /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:hv_train_network:set VAE model name for metadata: /media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/vae/diffusion_pytorch_model.safetensors
INFO:hv_train_network:DiT dtype: torch.bfloat16, device: cuda:1
INFO:hv_train_network:set DiT model name for metadata: /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:hv_train_network:DiT dtype: torch.bfloat16, device: cuda:2

steps:   0%|          | 0/272 [00:00<?, ?it/s]INFO:hv_train_network:set DiT model name for metadata: /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:hv_train_network:set VAE model name for metadata: /media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/vae/diffusion_pytorch_model.safetensors
INFO:hv_train_network:set VAE model name for metadata: /media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/vae/diffusion_pytorch_model.safetensors
INFO:hv_train_network:DiT dtype: torch.bfloat16, device: cuda:0
INFO:hv_train_network:DiT dtype: torch.bfloat16, device: cuda:3
INFO:hv_train_network:DiT dtype: torch.bfloat16, device: cuda:4

epoch 1/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1

steps:   0%|          | 1/272 [00:15<1:07:55, 15.04s/it]
steps:   0%|          | 1/272 [00:15<1:07:55, 15.04s/it, avr_loss=0.0569]
steps:   1%|          | 2/272 [00:27<1:02:02, 13.79s/it, avr_loss=0.0569]
steps:   1%|          | 2/272 [00:27<1:02:02, 13.79s/it, avr_loss=0.0546]
steps:   1%|          | 3/272 [00:40<59:54, 13.36s/it, avr_loss=0.0546]  
steps:   1%|          | 3/272 [00:40<59:54, 13.36s/it, avr_loss=0.0793]
steps:   1%|▏         | 4/272 [00:52<58:45, 13.16s/it, avr_loss=0.0793]
steps:   1%|▏         | 4/272 [00:52<58:45, 13.16s/it, avr_loss=0.0778]
steps:   2%|▏         | 5/272 [01:05<57:59, 13.03s/it, avr_loss=0.0778]
steps:   2%|▏         | 5/272 [01:05<57:59, 13.03s/it, avr_loss=0.0795]
steps:   2%|▏         | 6/272 [01:17<57:23, 12.95s/it, avr_loss=0.0795]
steps:   2%|▏         | 6/272 [01:17<57:23, 12.95s/it, avr_loss=0.0776]
steps:   3%|▎         | 7/272 [01:30<56:55, 12.89s/it, avr_loss=0.0776]
steps:   3%|▎         | 7/272 [01:30<56:55, 12.89s/it, avr_loss=0.0745]
steps:   3%|▎         | 8/272 [01:42<56:32, 12.85s/it, avr_loss=0.0745]
steps:   3%|▎         | 8/272 [01:42<56:32, 12.85s/it, avr_loss=0.0734]
steps:   3%|▎         | 9/272 [01:55<56:10, 12.82s/it, avr_loss=0.0734]
steps:   3%|▎         | 9/272 [01:55<56:10, 12.82s/it, avr_loss=0.0721]
steps:   4%|▎         | 10/272 [02:07<55:50, 12.79s/it, avr_loss=0.0721]
steps:   4%|▎         | 10/272 [02:07<55:50, 12.79s/it, avr_loss=0.0753]
steps:   4%|▍         | 11/272 [02:20<55:31, 12.77s/it, avr_loss=0.0753]
steps:   4%|▍         | 11/272 [02:20<55:31, 12.77s/it, avr_loss=0.0749]
steps:   4%|▍         | 12/272 [02:32<55:14, 12.75s/it, avr_loss=0.0749]
steps:   4%|▍         | 12/272 [02:32<55:14, 12.75s/it, avr_loss=0.0732]
steps:   5%|▍         | 13/272 [02:45<54:57, 12.73s/it, avr_loss=0.0732]
steps:   5%|▍         | 13/272 [02:45<54:57, 12.73s/it, avr_loss=0.0723]
steps:   5%|▌         | 14/272 [02:58<54:41, 12.72s/it, avr_loss=0.0723]
steps:   5%|▌         | 14/272 [02:58<54:41, 12.72s/it, avr_loss=0.0715]
steps:   6%|▌         | 15/272 [03:10<54:25, 12.71s/it, avr_loss=0.0715]
steps:   6%|▌         | 15/272 [03:10<54:25, 12.71s/it, avr_loss=0.0702]
steps:   6%|▌         | 16/272 [03:23<54:10, 12.70s/it, avr_loss=0.0702]
steps:   6%|▌         | 16/272 [03:23<54:10, 12.70s/it, avr_loss=0.0701]
steps:   6%|▋         | 17/272 [03:35<53:55, 12.69s/it, avr_loss=0.0701]
steps:   6%|▋         | 17/272 [03:35<53:55, 12.69s/it, avr_loss=0.0692]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/block_handover_D435_10/train_lora-000001.safetensors

epoch 2/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2

steps:   7%|▋         | 18/272 [03:50<54:13, 12.81s/it, avr_loss=0.0692]
steps:   7%|▋         | 18/272 [03:50<54:13, 12.81s/it, avr_loss=0.069] 
steps:   7%|▋         | 19/272 [04:03<53:56, 12.79s/it, avr_loss=0.069]
steps:   7%|▋         | 19/272 [04:03<53:56, 12.79s/it, avr_loss=0.0695]
steps:   7%|▋         | 20/272 [04:15<53:41, 12.78s/it, avr_loss=0.0695]
steps:   7%|▋         | 20/272 [04:15<53:41, 12.78s/it, avr_loss=0.065] 
steps:   8%|▊         | 21/272 [04:28<53:25, 12.77s/it, avr_loss=0.065]
steps:   8%|▊         | 21/272 [04:28<53:25, 12.77s/it, avr_loss=0.0672]
steps:   8%|▊         | 22/272 [04:40<53:10, 12.76s/it, avr_loss=0.0672]
steps:   8%|▊         | 22/272 [04:40<53:10, 12.76s/it, avr_loss=0.0651]
steps:   8%|▊         | 23/272 [04:53<52:55, 12.75s/it, avr_loss=0.0651]
steps:   8%|▊         | 23/272 [04:53<52:55, 12.75s/it, avr_loss=0.0649]
steps:   9%|▉         | 24/272 [05:05<52:40, 12.74s/it, avr_loss=0.0649]
steps:   9%|▉         | 24/272 [05:05<52:40, 12.74s/it, avr_loss=0.0662]
steps:   9%|▉         | 25/272 [05:18<52:27, 12.74s/it, avr_loss=0.0662]
steps:   9%|▉         | 25/272 [05:18<52:27, 12.74s/it, avr_loss=0.0662]
steps:  10%|▉         | 26/272 [05:31<52:12, 12.73s/it, avr_loss=0.0662]
steps:  10%|▉         | 26/272 [05:31<52:12, 12.73s/it, avr_loss=0.0653]
steps:  10%|▉         | 27/272 [05:43<51:58, 12.73s/it, avr_loss=0.0653]
steps:  10%|▉         | 27/272 [05:43<51:58, 12.73s/it, avr_loss=0.0743]
steps:  10%|█         | 28/272 [05:56<51:44, 12.72s/it, avr_loss=0.0743]
steps:  10%|█         | 28/272 [05:56<51:44, 12.72s/it, avr_loss=0.073] 
steps:  11%|█         | 29/272 [06:08<51:29, 12.72s/it, avr_loss=0.073]
steps:  11%|█         | 29/272 [06:08<51:29, 12.72s/it, avr_loss=0.0727]
steps:  11%|█         | 30/272 [06:21<51:15, 12.71s/it, avr_loss=0.0727]
steps:  11%|█         | 30/272 [06:21<51:15, 12.71s/it, avr_loss=0.0725]
steps:  11%|█▏        | 31/272 [06:33<51:01, 12.70s/it, avr_loss=0.0725]
steps:  11%|█▏        | 31/272 [06:33<51:01, 12.70s/it, avr_loss=0.0717]
steps:  12%|█▏        | 32/272 [06:46<50:47, 12.70s/it, avr_loss=0.0717]
steps:  12%|█▏        | 32/272 [06:46<50:47, 12.70s/it, avr_loss=0.0719]
steps:  12%|█▏        | 33/272 [06:58<50:33, 12.69s/it, avr_loss=0.0719]
steps:  12%|█▏        | 33/272 [06:58<50:33, 12.69s/it, avr_loss=0.0707]
steps:  12%|█▎        | 34/272 [07:11<50:20, 12.69s/it, avr_loss=0.0707]
steps:  12%|█▎        | 34/272 [07:11<50:20, 12.69s/it, avr_loss=0.0704]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/block_handover_D435_10/train_lora-000002.safetensors

epoch 3/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3

steps:  13%|█▎        | 35/272 [07:25<50:18, 12.74s/it, avr_loss=0.0704]
steps:  13%|█▎        | 35/272 [07:25<50:18, 12.74s/it, avr_loss=0.0711]
steps:  13%|█▎        | 36/272 [07:38<50:04, 12.73s/it, avr_loss=0.0711]
steps:  13%|█▎        | 36/272 [07:38<50:04, 12.73s/it, avr_loss=0.0712]
steps:  14%|█▎        | 37/272 [07:50<49:50, 12.73s/it, avr_loss=0.0712]
steps:  14%|█▎        | 37/272 [07:50<49:50, 12.73s/it, avr_loss=0.0735]
steps:  14%|█▍        | 38/272 [08:03<49:37, 12.72s/it, avr_loss=0.0735]
steps:  14%|█▍        | 38/272 [08:03<49:37, 12.72s/it, avr_loss=0.07]  
steps:  14%|█▍        | 39/272 [08:15<49:23, 12.72s/it, avr_loss=0.07]
steps:  14%|█▍        | 39/272 [08:15<49:23, 12.72s/it, avr_loss=0.0702]
steps:  15%|█▍        | 40/272 [08:28<49:10, 12.72s/it, avr_loss=0.0702]
steps:  15%|█▍        | 40/272 [08:28<49:10, 12.72s/it, avr_loss=0.069] 
steps:  15%|█▌        | 41/272 [08:41<48:56, 12.71s/it, avr_loss=0.069]
steps:  15%|█▌        | 41/272 [08:41<48:56, 12.71s/it, avr_loss=0.068]
steps:  15%|█▌        | 42/272 [08:53<48:43, 12.71s/it, avr_loss=0.068]
steps:  15%|█▌        | 42/272 [08:53<48:43, 12.71s/it, avr_loss=0.0701]
steps:  16%|█▌        | 43/272 [09:06<48:29, 12.71s/it, avr_loss=0.0701]
steps:  16%|█▌        | 43/272 [09:06<48:29, 12.71s/it, avr_loss=0.073] 
steps:  16%|█▌        | 44/272 [09:18<48:16, 12.70s/it, avr_loss=0.073]
steps:  16%|█▌        | 44/272 [09:18<48:16, 12.70s/it, avr_loss=0.0606]
steps:  17%|█▋        | 45/272 [09:31<48:02, 12.70s/it, avr_loss=0.0606]
steps:  17%|█▋        | 45/272 [09:31<48:02, 12.70s/it, avr_loss=0.0606]
steps:  17%|█▋        | 46/272 [09:43<47:49, 12.70s/it, avr_loss=0.0606]
steps:  17%|█▋        | 46/272 [09:43<47:49, 12.70s/it, avr_loss=0.064] 
steps:  17%|█▋        | 47/272 [09:56<47:35, 12.69s/it, avr_loss=0.064]
steps:  17%|█▋        | 47/272 [09:56<47:35, 12.69s/it, avr_loss=0.0646]
steps:  18%|█▊        | 48/272 [10:09<47:22, 12.69s/it, avr_loss=0.0646]
steps:  18%|█▊        | 48/272 [10:09<47:22, 12.69s/it, avr_loss=0.0643]
steps:  18%|█▊        | 49/272 [10:21<47:08, 12.69s/it, avr_loss=0.0643]
steps:  18%|█▊        | 49/272 [10:21<47:08, 12.69s/it, avr_loss=0.0672]
steps:  18%|█▊        | 50/272 [10:34<46:55, 12.68s/it, avr_loss=0.0672]
steps:  18%|█▊        | 50/272 [10:34<46:55, 12.68s/it, avr_loss=0.0677]
steps:  19%|█▉        | 51/272 [10:46<46:42, 12.68s/it, avr_loss=0.0677]
steps:  19%|█▉        | 51/272 [10:46<46:42, 12.68s/it, avr_loss=0.0703]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/block_handover_D435_10/train_lora-000003.safetensors

epoch 4/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4

steps:  19%|█▉        | 52/272 [11:01<46:36, 12.71s/it, avr_loss=0.0703]
steps:  19%|█▉        | 52/272 [11:01<46:36, 12.71s/it, avr_loss=0.0696]
steps:  19%|█▉        | 53/272 [11:13<46:23, 12.71s/it, avr_loss=0.0696]
steps:  19%|█▉        | 53/272 [11:13<46:23, 12.71s/it, avr_loss=0.0684]
steps:  20%|█▉        | 54/272 [11:26<46:09, 12.71s/it, avr_loss=0.0684]
steps:  20%|█▉        | 54/272 [11:26<46:09, 12.71s/it, avr_loss=0.0659]
steps:  20%|██        | 55/272 [11:38<45:57, 12.71s/it, avr_loss=0.0659]
steps:  20%|██        | 55/272 [11:38<45:57, 12.71s/it, avr_loss=0.0675]
steps:  21%|██        | 56/272 [11:51<45:43, 12.70s/it, avr_loss=0.0675]
steps:  21%|██        | 56/272 [11:51<45:43, 12.70s/it, avr_loss=0.067] 
steps:  21%|██        | 57/272 [12:03<45:30, 12.70s/it, avr_loss=0.067]
steps:  21%|██        | 57/272 [12:03<45:30, 12.70s/it, avr_loss=0.067]
steps:  21%|██▏       | 58/272 [12:16<45:17, 12.70s/it, avr_loss=0.067]
steps:  21%|██▏       | 58/272 [12:16<45:17, 12.70s/it, avr_loss=0.0663]
steps:  22%|██▏       | 59/272 [12:28<45:03, 12.69s/it, avr_loss=0.0663]
steps:  22%|██▏       | 59/272 [12:28<45:03, 12.69s/it, avr_loss=0.0631]
steps:  22%|██▏       | 60/272 [12:41<44:50, 12.69s/it, avr_loss=0.0631]
steps:  22%|██▏       | 60/272 [12:41<44:50, 12.69s/it, avr_loss=0.0603]
steps:  22%|██▏       | 61/272 [12:54<44:37, 12.69s/it, avr_loss=0.0603]
steps:  22%|██▏       | 61/272 [12:54<44:37, 12.69s/it, avr_loss=0.0607]
steps:  23%|██▎       | 62/272 [13:06<44:24, 12.69s/it, avr_loss=0.0607]
steps:  23%|██▎       | 62/272 [13:06<44:24, 12.69s/it, avr_loss=0.0607]
steps:  23%|██▎       | 63/272 [13:19<44:11, 12.69s/it, avr_loss=0.0607]
steps:  23%|██▎       | 63/272 [13:19<44:11, 12.69s/it, avr_loss=0.0573]
steps:  24%|██▎       | 64/272 [13:31<43:58, 12.68s/it, avr_loss=0.0573]
steps:  24%|██▎       | 64/272 [13:31<43:58, 12.68s/it, avr_loss=0.0563]
steps:  24%|██▍       | 65/272 [13:44<43:45, 12.68s/it, avr_loss=0.0563]
steps:  24%|██▍       | 65/272 [13:44<43:45, 12.68s/it, avr_loss=0.057] 
steps:  24%|██▍       | 66/272 [13:56<43:31, 12.68s/it, avr_loss=0.057]
steps:  24%|██▍       | 66/272 [13:56<43:31, 12.68s/it, avr_loss=0.0547]
steps:  25%|██▍       | 67/272 [14:09<43:18, 12.68s/it, avr_loss=0.0547]
steps:  25%|██▍       | 67/272 [14:09<43:18, 12.68s/it, avr_loss=0.0541]
steps:  25%|██▌       | 68/272 [14:21<43:05, 12.68s/it, avr_loss=0.0541]
steps:  25%|██▌       | 68/272 [14:21<43:05, 12.68s/it, avr_loss=0.0512]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/block_handover_D435_10/train_lora-000004.safetensors

epoch 5/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5

steps:  25%|██▌       | 69/272 [14:36<42:58, 12.70s/it, avr_loss=0.0512]
steps:  25%|██▌       | 69/272 [14:36<42:58, 12.70s/it, avr_loss=0.0512]
steps:  26%|██▌       | 70/272 [14:48<42:45, 12.70s/it, avr_loss=0.0512]
steps:  26%|██▌       | 70/272 [14:48<42:45, 12.70s/it, avr_loss=0.0533]
steps:  26%|██▌       | 71/272 [15:01<42:32, 12.70s/it, avr_loss=0.0533]
steps:  26%|██▌       | 71/272 [15:01<42:32, 12.70s/it, avr_loss=0.055] 
steps:  26%|██▋       | 72/272 [15:14<42:18, 12.69s/it, avr_loss=0.055]
steps:  26%|██▋       | 72/272 [15:14<42:18, 12.69s/it, avr_loss=0.0529]
steps:  27%|██▋       | 73/272 [15:26<42:05, 12.69s/it, avr_loss=0.0529]
steps:  27%|██▋       | 73/272 [15:26<42:05, 12.69s/it, avr_loss=0.0538]
steps:  27%|██▋       | 74/272 [15:39<41:52, 12.69s/it, avr_loss=0.0538]
steps:  27%|██▋       | 74/272 [15:39<41:52, 12.69s/it, avr_loss=0.0538]
steps:  28%|██▊       | 75/272 [15:51<41:39, 12.69s/it, avr_loss=0.0538]
steps:  28%|██▊       | 75/272 [15:51<41:39, 12.69s/it, avr_loss=0.0536]
steps:  28%|██▊       | 76/272 [16:04<41:26, 12.69s/it, avr_loss=0.0536]
steps:  28%|██▊       | 76/272 [16:04<41:26, 12.69s/it, avr_loss=0.0534]
steps:  28%|██▊       | 77/272 [16:16<41:13, 12.69s/it, avr_loss=0.0534]
steps:  28%|██▊       | 77/272 [16:16<41:13, 12.69s/it, avr_loss=0.053] 
steps:  29%|██▊       | 78/272 [16:29<41:00, 12.68s/it, avr_loss=0.053]
steps:  29%|██▊       | 78/272 [16:29<41:00, 12.68s/it, avr_loss=0.0544]
steps:  29%|██▉       | 79/272 [16:41<40:47, 12.68s/it, avr_loss=0.0544]
steps:  29%|██▉       | 79/272 [16:41<40:47, 12.68s/it, avr_loss=0.0545]
steps:  29%|██▉       | 80/272 [16:54<40:34, 12.68s/it, avr_loss=0.0545]
steps:  29%|██▉       | 80/272 [16:54<40:34, 12.68s/it, avr_loss=0.0602]
steps:  30%|██▉       | 81/272 [17:06<40:21, 12.68s/it, avr_loss=0.0602]
steps:  30%|██▉       | 81/272 [17:06<40:21, 12.68s/it, avr_loss=0.0597]
steps:  30%|███       | 82/272 [17:19<40:08, 12.68s/it, avr_loss=0.0597]
steps:  30%|███       | 82/272 [17:19<40:08, 12.68s/it, avr_loss=0.0597]
steps:  31%|███       | 83/272 [17:32<39:55, 12.68s/it, avr_loss=0.0597]
steps:  31%|███       | 83/272 [17:32<39:55, 12.68s/it, avr_loss=0.0583]
steps:  31%|███       | 84/272 [17:44<39:42, 12.67s/it, avr_loss=0.0583]
steps:  31%|███       | 84/272 [17:44<39:42, 12.67s/it, avr_loss=0.0651]
steps:  31%|███▏      | 85/272 [17:57<39:29, 12.67s/it, avr_loss=0.0651]
steps:  31%|███▏      | 85/272 [17:57<39:29, 12.67s/it, avr_loss=0.0651]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/block_handover_D435_10/train_lora-000005.safetensors

epoch 6/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6

steps:  32%|███▏      | 86/272 [18:11<39:21, 12.70s/it, avr_loss=0.0651]
steps:  32%|███▏      | 86/272 [18:11<39:21, 12.70s/it, avr_loss=0.0648]
steps:  32%|███▏      | 87/272 [18:24<39:08, 12.69s/it, avr_loss=0.0648]
steps:  32%|███▏      | 87/272 [18:24<39:08, 12.69s/it, avr_loss=0.0673]
steps:  32%|███▏      | 88/272 [18:36<38:55, 12.69s/it, avr_loss=0.0673]
steps:  32%|███▏      | 88/272 [18:36<38:55, 12.69s/it, avr_loss=0.0653]
steps:  33%|███▎      | 89/272 [18:49<38:42, 12.69s/it, avr_loss=0.0653]
steps:  33%|███▎      | 89/272 [18:49<38:42, 12.69s/it, avr_loss=0.0653]
steps:  33%|███▎      | 90/272 [19:02<38:29, 12.69s/it, avr_loss=0.0653]
steps:  33%|███▎      | 90/272 [19:02<38:29, 12.69s/it, avr_loss=0.0748]
steps:  33%|███▎      | 91/272 [19:14<38:16, 12.69s/it, avr_loss=0.0748]
steps:  33%|███▎      | 91/272 [19:14<38:16, 12.69s/it, avr_loss=0.0746]
steps:  34%|███▍      | 92/272 [19:27<38:03, 12.69s/it, avr_loss=0.0746]
steps:  34%|███▍      | 92/272 [19:27<38:03, 12.69s/it, avr_loss=0.0744]
steps:  34%|███▍      | 93/272 [19:39<37:50, 12.68s/it, avr_loss=0.0744]
steps:  34%|███▍      | 93/272 [19:39<37:50, 12.68s/it, avr_loss=0.0786]
steps:  35%|███▍      | 94/272 [19:52<37:37, 12.68s/it, avr_loss=0.0786]
steps:  35%|███▍      | 94/272 [19:52<37:37, 12.68s/it, avr_loss=0.0835]
steps:  35%|███▍      | 95/272 [20:04<37:24, 12.68s/it, avr_loss=0.0835]
steps:  35%|███▍      | 95/272 [20:04<37:24, 12.68s/it, avr_loss=0.0821]
steps:  35%|███▌      | 96/272 [20:17<37:11, 12.68s/it, avr_loss=0.0821]
steps:  35%|███▌      | 96/272 [20:17<37:11, 12.68s/it, avr_loss=0.0816]
steps:  36%|███▌      | 97/272 [20:29<36:59, 12.68s/it, avr_loss=0.0816]
steps:  36%|███▌      | 97/272 [20:29<36:59, 12.68s/it, avr_loss=0.0755]
steps:  36%|███▌      | 98/272 [20:42<36:46, 12.68s/it, avr_loss=0.0755]
steps:  36%|███▌      | 98/272 [20:42<36:46, 12.68s/it, avr_loss=0.0759]
steps:  36%|███▋      | 99/272 [20:55<36:33, 12.68s/it, avr_loss=0.0759]
steps:  36%|███▋      | 99/272 [20:55<36:33, 12.68s/it, avr_loss=0.0758]
steps:  37%|███▋      | 100/272 [21:07<36:20, 12.68s/it, avr_loss=0.0758]
steps:  37%|███▋      | 100/272 [21:07<36:20, 12.68s/it, avr_loss=0.076] 
steps:  37%|███▋      | 101/272 [21:20<36:07, 12.67s/it, avr_loss=0.076]
steps:  37%|███▋      | 101/272 [21:20<36:07, 12.67s/it, avr_loss=0.0691]
steps:  38%|███▊      | 102/272 [21:32<35:54, 12.67s/it, avr_loss=0.0691]
steps:  38%|███▊      | 102/272 [21:32<35:54, 12.67s/it, avr_loss=0.0691]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/block_handover_D435_10/train_lora-000006.safetensors

epoch 7/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7

steps:  38%|███▊      | 103/272 [21:46<35:44, 12.69s/it, avr_loss=0.0691]
steps:  38%|███▊      | 103/272 [21:46<35:44, 12.69s/it, avr_loss=0.0697]
steps:  38%|███▊      | 104/272 [21:59<35:31, 12.69s/it, avr_loss=0.0697]
steps:  38%|███▊      | 104/272 [21:59<35:31, 12.69s/it, avr_loss=0.0653]
steps:  39%|███▊      | 105/272 [22:12<35:18, 12.69s/it, avr_loss=0.0653]
steps:  39%|███▊      | 105/272 [22:12<35:18, 12.69s/it, avr_loss=0.0688]
steps:  39%|███▉      | 106/272 [22:24<35:05, 12.69s/it, avr_loss=0.0688]
steps:  39%|███▉      | 106/272 [22:24<35:05, 12.69s/it, avr_loss=0.0692]
steps:  39%|███▉      | 107/272 [22:37<34:52, 12.68s/it, avr_loss=0.0692]
steps:  39%|███▉      | 107/272 [22:37<34:52, 12.68s/it, avr_loss=0.0595]
steps:  40%|███▉      | 108/272 [22:49<34:40, 12.68s/it, avr_loss=0.0595]
steps:  40%|███▉      | 108/272 [22:49<34:40, 12.68s/it, avr_loss=0.0613]
steps:  40%|████      | 109/272 [23:02<34:27, 12.68s/it, avr_loss=0.0613]
steps:  40%|████      | 109/272 [23:02<34:27, 12.68s/it, avr_loss=0.0614]
steps:  40%|████      | 110/272 [23:14<34:14, 12.68s/it, avr_loss=0.0614]
steps:  40%|████      | 110/272 [23:14<34:14, 12.68s/it, avr_loss=0.0571]
steps:  41%|████      | 111/272 [23:27<34:01, 12.68s/it, avr_loss=0.0571]
steps:  41%|████      | 111/272 [23:27<34:01, 12.68s/it, avr_loss=0.0521]
steps:  41%|████      | 112/272 [23:40<33:48, 12.68s/it, avr_loss=0.0521]
steps:  41%|████      | 112/272 [23:40<33:48, 12.68s/it, avr_loss=0.0555]
steps:  42%|████▏     | 113/272 [23:52<33:35, 12.68s/it, avr_loss=0.0555]
steps:  42%|████▏     | 113/272 [23:52<33:35, 12.68s/it, avr_loss=0.0565]
steps:  42%|████▏     | 114/272 [24:05<33:23, 12.68s/it, avr_loss=0.0565]
steps:  42%|████▏     | 114/272 [24:05<33:23, 12.68s/it, avr_loss=0.0565]
steps:  42%|████▏     | 115/272 [24:17<33:10, 12.68s/it, avr_loss=0.0565]
steps:  42%|████▏     | 115/272 [24:17<33:10, 12.68s/it, avr_loss=0.0577]
steps:  43%|████▎     | 116/272 [24:30<32:57, 12.68s/it, avr_loss=0.0577]
steps:  43%|████▎     | 116/272 [24:30<32:57, 12.68s/it, avr_loss=0.0581]
steps:  43%|████▎     | 117/272 [24:42<32:44, 12.67s/it, avr_loss=0.0581]
steps:  43%|████▎     | 117/272 [24:42<32:44, 12.67s/it, avr_loss=0.0585]
steps:  43%|████▎     | 118/272 [24:55<32:31, 12.67s/it, avr_loss=0.0585]
steps:  43%|████▎     | 118/272 [24:55<32:31, 12.67s/it, avr_loss=0.0597]
steps:  44%|████▍     | 119/272 [25:08<32:18, 12.67s/it, avr_loss=0.0597]
steps:  44%|████▍     | 119/272 [25:08<32:18, 12.67s/it, avr_loss=0.0599]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/block_handover_D435_10/train_lora-000007.safetensors

epoch 8/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8

steps:  44%|████▍     | 120/272 [25:22<32:08, 12.69s/it, avr_loss=0.0599]
steps:  44%|████▍     | 120/272 [25:22<32:08, 12.69s/it, avr_loss=0.0588]
steps:  44%|████▍     | 121/272 [25:35<31:55, 12.69s/it, avr_loss=0.0588]
steps:  44%|████▍     | 121/272 [25:35<31:55, 12.69s/it, avr_loss=0.0615]
steps:  45%|████▍     | 122/272 [25:47<31:42, 12.69s/it, avr_loss=0.0615]
steps:  45%|████▍     | 122/272 [25:47<31:42, 12.69s/it, avr_loss=0.06]  
steps:  45%|████▌     | 123/272 [26:00<31:30, 12.68s/it, avr_loss=0.06]
steps:  45%|████▌     | 123/272 [26:00<31:30, 12.68s/it, avr_loss=0.06]
steps:  46%|████▌     | 124/272 [26:12<31:17, 12.68s/it, avr_loss=0.06]
steps:  46%|████▌     | 124/272 [26:12<31:17, 12.68s/it, avr_loss=0.0591]
steps:  46%|████▌     | 125/272 [26:25<31:04, 12.68s/it, avr_loss=0.0591]
steps:  46%|████▌     | 125/272 [26:25<31:04, 12.68s/it, avr_loss=0.058] 
steps:  46%|████▋     | 126/272 [26:38<30:51, 12.68s/it, avr_loss=0.058]
steps:  46%|████▋     | 126/272 [26:38<30:51, 12.68s/it, avr_loss=0.058]
steps:  47%|████▋     | 127/272 [26:50<30:38, 12.68s/it, avr_loss=0.058]
steps:  47%|████▋     | 127/272 [26:50<30:38, 12.68s/it, avr_loss=0.0597]
steps:  47%|████▋     | 128/272 [27:03<30:26, 12.68s/it, avr_loss=0.0597]
steps:  47%|████▋     | 128/272 [27:03<30:26, 12.68s/it, avr_loss=0.0599]
steps:  47%|████▋     | 129/272 [27:15<30:13, 12.68s/it, avr_loss=0.0599]
steps:  47%|████▋     | 129/272 [27:15<30:13, 12.68s/it, avr_loss=0.0559]
steps:  48%|████▊     | 130/272 [27:28<30:00, 12.68s/it, avr_loss=0.0559]
steps:  48%|████▊     | 130/272 [27:28<30:00, 12.68s/it, avr_loss=0.0548]
steps:  48%|████▊     | 131/272 [27:40<29:47, 12.68s/it, avr_loss=0.0548]
steps:  48%|████▊     | 131/272 [27:40<29:47, 12.68s/it, avr_loss=0.0548]
steps:  49%|████▊     | 132/272 [27:53<29:34, 12.68s/it, avr_loss=0.0548]
steps:  49%|████▊     | 132/272 [27:53<29:34, 12.68s/it, avr_loss=0.0557]
steps:  49%|████▉     | 133/272 [28:05<29:21, 12.68s/it, avr_loss=0.0557]
steps:  49%|████▉     | 133/272 [28:05<29:21, 12.68s/it, avr_loss=0.0544]
steps:  49%|████▉     | 134/272 [28:18<29:09, 12.67s/it, avr_loss=0.0544]
steps:  49%|████▉     | 134/272 [28:18<29:09, 12.67s/it, avr_loss=0.0547]
steps:  50%|████▉     | 135/272 [28:30<28:56, 12.67s/it, avr_loss=0.0547]
steps:  50%|████▉     | 135/272 [28:30<28:56, 12.67s/it, avr_loss=0.0533]
steps:  50%|█████     | 136/272 [28:43<28:43, 12.67s/it, avr_loss=0.0533]
steps:  50%|█████     | 136/272 [28:43<28:43, 12.67s/it, avr_loss=0.0527]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/block_handover_D435_10/train_lora-000008.safetensors

epoch 9/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9

steps:  50%|█████     | 137/272 [28:57<28:32, 12.69s/it, avr_loss=0.0527]
steps:  50%|█████     | 137/272 [28:57<28:32, 12.69s/it, avr_loss=0.0529]
steps:  51%|█████     | 138/272 [29:10<28:19, 12.68s/it, avr_loss=0.0529]
steps:  51%|█████     | 138/272 [29:10<28:19, 12.68s/it, avr_loss=0.0499]
steps:  51%|█████     | 139/272 [29:22<28:06, 12.68s/it, avr_loss=0.0499]
steps:  51%|█████     | 139/272 [29:22<28:06, 12.68s/it, avr_loss=0.048] 
steps:  51%|█████▏    | 140/272 [29:35<27:54, 12.68s/it, avr_loss=0.048]
steps:  51%|█████▏    | 140/272 [29:35<27:54, 12.68s/it, avr_loss=0.0473]
steps:  52%|█████▏    | 141/272 [29:48<27:41, 12.68s/it, avr_loss=0.0473]
steps:  52%|█████▏    | 141/272 [29:48<27:41, 12.68s/it, avr_loss=0.0473]
steps:  52%|█████▏    | 142/272 [30:00<27:28, 12.68s/it, avr_loss=0.0473]
steps:  52%|█████▏    | 142/272 [30:00<27:28, 12.68s/it, avr_loss=0.0477]
steps:  53%|█████▎    | 143/272 [30:13<27:15, 12.68s/it, avr_loss=0.0477]
steps:  53%|█████▎    | 143/272 [30:13<27:15, 12.68s/it, avr_loss=0.0477]
steps:  53%|█████▎    | 144/272 [30:25<27:02, 12.68s/it, avr_loss=0.0477]
steps:  53%|█████▎    | 144/272 [30:25<27:02, 12.68s/it, avr_loss=0.0462]
steps:  53%|█████▎    | 145/272 [30:38<26:50, 12.68s/it, avr_loss=0.0462]
steps:  53%|█████▎    | 145/272 [30:38<26:50, 12.68s/it, avr_loss=0.0458]
steps:  54%|█████▎    | 146/272 [30:50<26:37, 12.68s/it, avr_loss=0.0458]
steps:  54%|█████▎    | 146/272 [30:50<26:37, 12.68s/it, avr_loss=0.0567]
steps:  54%|█████▍    | 147/272 [31:03<26:24, 12.68s/it, avr_loss=0.0567]
steps:  54%|█████▍    | 147/272 [31:03<26:24, 12.68s/it, avr_loss=0.0579]
steps:  54%|█████▍    | 148/272 [31:16<26:11, 12.68s/it, avr_loss=0.0579]
steps:  54%|█████▍    | 148/272 [31:16<26:11, 12.68s/it, avr_loss=0.0578]
steps:  55%|█████▍    | 149/272 [31:28<25:59, 12.68s/it, avr_loss=0.0578]
steps:  55%|█████▍    | 149/272 [31:28<25:59, 12.68s/it, avr_loss=0.0551]
steps:  55%|█████▌    | 150/272 [31:41<25:46, 12.67s/it, avr_loss=0.0551]
steps:  55%|█████▌    | 150/272 [31:41<25:46, 12.67s/it, avr_loss=0.0562]
steps:  56%|█████▌    | 151/272 [31:53<25:33, 12.67s/it, avr_loss=0.0562]
steps:  56%|█████▌    | 151/272 [31:53<25:33, 12.67s/it, avr_loss=0.0552]
steps:  56%|█████▌    | 152/272 [32:06<25:20, 12.67s/it, avr_loss=0.0552]
steps:  56%|█████▌    | 152/272 [32:06<25:20, 12.67s/it, avr_loss=0.0552]
steps:  56%|█████▋    | 153/272 [32:18<25:08, 12.67s/it, avr_loss=0.0552]
steps:  56%|█████▋    | 153/272 [32:18<25:08, 12.67s/it, avr_loss=0.0572]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/block_handover_D435_10/train_lora-000009.safetensors

epoch 10/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10

steps:  57%|█████▋    | 154/272 [32:33<24:56, 12.68s/it, avr_loss=0.0572]
steps:  57%|█████▋    | 154/272 [32:33<24:56, 12.68s/it, avr_loss=0.0569]
steps:  57%|█████▋    | 155/272 [32:45<24:43, 12.68s/it, avr_loss=0.0569]
steps:  57%|█████▋    | 155/272 [32:45<24:43, 12.68s/it, avr_loss=0.0604]
steps:  57%|█████▋    | 156/272 [32:58<24:31, 12.68s/it, avr_loss=0.0604]
steps:  57%|█████▋    | 156/272 [32:58<24:31, 12.68s/it, avr_loss=0.0614]
steps:  58%|█████▊    | 157/272 [33:11<24:18, 12.68s/it, avr_loss=0.0614]
steps:  58%|█████▊    | 157/272 [33:11<24:18, 12.68s/it, avr_loss=0.0615]
steps:  58%|█████▊    | 158/272 [33:23<24:05, 12.68s/it, avr_loss=0.0615]
steps:  58%|█████▊    | 158/272 [33:23<24:05, 12.68s/it, avr_loss=0.0625]
steps:  58%|█████▊    | 159/272 [33:36<23:52, 12.68s/it, avr_loss=0.0625]
steps:  58%|█████▊    | 159/272 [33:36<23:52, 12.68s/it, avr_loss=0.0615]
steps:  59%|█████▉    | 160/272 [33:48<23:40, 12.68s/it, avr_loss=0.0615]
steps:  59%|█████▉    | 160/272 [33:48<23:40, 12.68s/it, avr_loss=0.0623]
steps:  59%|█████▉    | 161/272 [34:01<23:27, 12.68s/it, avr_loss=0.0623]
steps:  59%|█████▉    | 161/272 [34:01<23:27, 12.68s/it, avr_loss=0.0622]
steps:  60%|█████▉    | 162/272 [34:13<23:14, 12.68s/it, avr_loss=0.0622]
steps:  60%|█████▉    | 162/272 [34:13<23:14, 12.68s/it, avr_loss=0.0717]
steps:  60%|█████▉    | 163/272 [34:26<23:01, 12.68s/it, avr_loss=0.0717]
steps:  60%|█████▉    | 163/272 [34:26<23:01, 12.68s/it, avr_loss=0.0605]
steps:  60%|██████    | 164/272 [34:38<22:49, 12.68s/it, avr_loss=0.0605]
steps:  60%|██████    | 164/272 [34:38<22:49, 12.68s/it, avr_loss=0.0593]
steps:  61%|██████    | 165/272 [34:51<22:36, 12.68s/it, avr_loss=0.0593]
steps:  61%|██████    | 165/272 [34:51<22:36, 12.68s/it, avr_loss=0.0596]
steps:  61%|██████    | 166/272 [35:04<22:23, 12.67s/it, avr_loss=0.0596]
steps:  61%|██████    | 166/272 [35:04<22:23, 12.67s/it, avr_loss=0.0596]
steps:  61%|██████▏   | 167/272 [35:16<22:10, 12.67s/it, avr_loss=0.0596]
steps:  61%|██████▏   | 167/272 [35:16<22:10, 12.67s/it, avr_loss=0.0585]
steps:  62%|██████▏   | 168/272 [35:29<21:58, 12.67s/it, avr_loss=0.0585]
steps:  62%|██████▏   | 168/272 [35:29<21:58, 12.67s/it, avr_loss=0.0584]
steps:  62%|██████▏   | 169/272 [35:41<21:45, 12.67s/it, avr_loss=0.0584]
steps:  62%|██████▏   | 169/272 [35:41<21:45, 12.67s/it, avr_loss=0.0613]
steps:  62%|██████▎   | 170/272 [35:54<21:32, 12.67s/it, avr_loss=0.0613]
steps:  62%|██████▎   | 170/272 [35:54<21:32, 12.67s/it, avr_loss=0.0601]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/block_handover_D435_10/train_lora-000010.safetensors

epoch 11/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11

steps:  63%|██████▎   | 171/272 [36:08<21:20, 12.68s/it, avr_loss=0.0601]
steps:  63%|██████▎   | 171/272 [36:08<21:20, 12.68s/it, avr_loss=0.0606]
steps:  63%|██████▎   | 172/272 [36:21<21:08, 12.68s/it, avr_loss=0.0606]
steps:  63%|██████▎   | 172/272 [36:21<21:08, 12.68s/it, avr_loss=0.0568]
steps:  64%|██████▎   | 173/272 [36:33<20:55, 12.68s/it, avr_loss=0.0568]
steps:  64%|██████▎   | 173/272 [36:33<20:55, 12.68s/it, avr_loss=0.0561]
steps:  64%|██████▍   | 174/272 [36:46<20:42, 12.68s/it, avr_loss=0.0561]
steps:  64%|██████▍   | 174/272 [36:46<20:42, 12.68s/it, avr_loss=0.0579]
steps:  64%|██████▍   | 175/272 [36:58<20:29, 12.68s/it, avr_loss=0.0579]
steps:  64%|██████▍   | 175/272 [36:58<20:29, 12.68s/it, avr_loss=0.0567]
steps:  65%|██████▍   | 176/272 [37:11<20:17, 12.68s/it, avr_loss=0.0567]
steps:  65%|██████▍   | 176/272 [37:11<20:17, 12.68s/it, avr_loss=0.0572]
steps:  65%|██████▌   | 177/272 [37:24<20:04, 12.68s/it, avr_loss=0.0572]
steps:  65%|██████▌   | 177/272 [37:24<20:04, 12.68s/it, avr_loss=0.0565]
steps:  65%|██████▌   | 178/272 [37:36<19:51, 12.68s/it, avr_loss=0.0565]
steps:  65%|██████▌   | 178/272 [37:36<19:51, 12.68s/it, avr_loss=0.0562]
steps:  66%|██████▌   | 179/272 [37:49<19:38, 12.68s/it, avr_loss=0.0562]
steps:  66%|██████▌   | 179/272 [37:49<19:38, 12.68s/it, avr_loss=0.0467]
steps:  66%|██████▌   | 180/272 [38:01<19:26, 12.68s/it, avr_loss=0.0467]
steps:  66%|██████▌   | 180/272 [38:01<19:26, 12.68s/it, avr_loss=0.0507]
steps:  67%|██████▋   | 181/272 [38:14<19:13, 12.68s/it, avr_loss=0.0507]
steps:  67%|██████▋   | 181/272 [38:14<19:13, 12.68s/it, avr_loss=0.0505]
steps:  67%|██████▋   | 182/272 [38:26<19:00, 12.68s/it, avr_loss=0.0505]
steps:  67%|██████▋   | 182/272 [38:26<19:00, 12.68s/it, avr_loss=0.0549]
steps:  67%|██████▋   | 183/272 [38:39<18:48, 12.67s/it, avr_loss=0.0549]
steps:  67%|██████▋   | 183/272 [38:39<18:48, 12.67s/it, avr_loss=0.0553]
steps:  68%|██████▊   | 184/272 [38:52<18:35, 12.67s/it, avr_loss=0.0553]
steps:  68%|██████▊   | 184/272 [38:52<18:35, 12.67s/it, avr_loss=0.057] 
steps:  68%|██████▊   | 185/272 [39:04<18:22, 12.67s/it, avr_loss=0.057]
steps:  68%|██████▊   | 185/272 [39:04<18:22, 12.67s/it, avr_loss=0.0683]
steps:  68%|██████▊   | 186/272 [39:17<18:09, 12.67s/it, avr_loss=0.0683]
steps:  68%|██████▊   | 186/272 [39:17<18:09, 12.67s/it, avr_loss=0.0652]
steps:  69%|██████▉   | 187/272 [39:29<17:57, 12.67s/it, avr_loss=0.0652]
steps:  69%|██████▉   | 187/272 [39:29<17:57, 12.67s/it, avr_loss=0.0644]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/block_handover_D435_10/train_lora-000011.safetensors

epoch 12/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12

steps:  69%|██████▉   | 188/272 [39:44<17:45, 12.68s/it, avr_loss=0.0644]
steps:  69%|██████▉   | 188/272 [39:44<17:45, 12.68s/it, avr_loss=0.0709]
steps:  69%|██████▉   | 189/272 [39:56<17:32, 12.68s/it, avr_loss=0.0709]
steps:  69%|██████▉   | 189/272 [39:56<17:32, 12.68s/it, avr_loss=0.0712]
steps:  70%|██████▉   | 190/272 [40:09<17:19, 12.68s/it, avr_loss=0.0712]
steps:  70%|██████▉   | 190/272 [40:09<17:19, 12.68s/it, avr_loss=0.0705]
steps:  70%|███████   | 191/272 [40:21<17:07, 12.68s/it, avr_loss=0.0705]
steps:  70%|███████   | 191/272 [40:21<17:07, 12.68s/it, avr_loss=0.0685]
steps:  71%|███████   | 192/272 [40:34<16:54, 12.68s/it, avr_loss=0.0685]
steps:  71%|███████   | 192/272 [40:34<16:54, 12.68s/it, avr_loss=0.0685]
steps:  71%|███████   | 193/272 [40:46<16:41, 12.68s/it, avr_loss=0.0685]
steps:  71%|███████   | 193/272 [40:46<16:41, 12.68s/it, avr_loss=0.0676]
steps:  71%|███████▏  | 194/272 [40:59<16:28, 12.68s/it, avr_loss=0.0676]
steps:  71%|███████▏  | 194/272 [40:59<16:28, 12.68s/it, avr_loss=0.0676]
steps:  72%|███████▏  | 195/272 [41:11<16:16, 12.68s/it, avr_loss=0.0676]
steps:  72%|███████▏  | 195/272 [41:11<16:16, 12.68s/it, avr_loss=0.0675]
steps:  72%|███████▏  | 196/272 [41:24<16:03, 12.68s/it, avr_loss=0.0675]
steps:  72%|███████▏  | 196/272 [41:24<16:03, 12.68s/it, avr_loss=0.0686]
steps:  72%|███████▏  | 197/272 [41:37<15:50, 12.68s/it, avr_loss=0.0686]
steps:  72%|███████▏  | 197/272 [41:37<15:50, 12.68s/it, avr_loss=0.0648]
steps:  73%|███████▎  | 198/272 [41:49<15:37, 12.68s/it, avr_loss=0.0648]
steps:  73%|███████▎  | 198/272 [41:49<15:37, 12.68s/it, avr_loss=0.0647]
steps:  73%|███████▎  | 199/272 [42:02<15:25, 12.67s/it, avr_loss=0.0647]
steps:  73%|███████▎  | 199/272 [42:02<15:25, 12.67s/it, avr_loss=0.0606]
steps:  74%|███████▎  | 200/272 [42:14<15:12, 12.67s/it, avr_loss=0.0606]
steps:  74%|███████▎  | 200/272 [42:14<15:12, 12.67s/it, avr_loss=0.0603]
steps:  74%|███████▍  | 201/272 [42:27<14:59, 12.67s/it, avr_loss=0.0603]
steps:  74%|███████▍  | 201/272 [42:27<14:59, 12.67s/it, avr_loss=0.0583]
steps:  74%|███████▍  | 202/272 [42:39<14:47, 12.67s/it, avr_loss=0.0583]
steps:  74%|███████▍  | 202/272 [42:39<14:47, 12.67s/it, avr_loss=0.0478]
steps:  75%|███████▍  | 203/272 [42:52<14:34, 12.67s/it, avr_loss=0.0478]
steps:  75%|███████▍  | 203/272 [42:52<14:34, 12.67s/it, avr_loss=0.0538]
steps:  75%|███████▌  | 204/272 [43:05<14:21, 12.67s/it, avr_loss=0.0538]
steps:  75%|███████▌  | 204/272 [43:05<14:21, 12.67s/it, avr_loss=0.0538]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/block_handover_D435_10/train_lora-000012.safetensors

epoch 13/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13

steps:  75%|███████▌  | 205/272 [43:19<14:09, 12.68s/it, avr_loss=0.0538]
steps:  75%|███████▌  | 205/272 [43:19<14:09, 12.68s/it, avr_loss=0.0467]
steps:  76%|███████▌  | 206/272 [43:31<13:56, 12.68s/it, avr_loss=0.0467]
steps:  76%|███████▌  | 206/272 [43:31<13:56, 12.68s/it, avr_loss=0.0464]
steps:  76%|███████▌  | 207/272 [43:44<13:44, 12.68s/it, avr_loss=0.0464]
steps:  76%|███████▌  | 207/272 [43:44<13:44, 12.68s/it, avr_loss=0.0465]
steps:  76%|███████▋  | 208/272 [43:57<13:31, 12.68s/it, avr_loss=0.0465]
steps:  76%|███████▋  | 208/272 [43:57<13:31, 12.68s/it, avr_loss=0.0466]
steps:  77%|███████▋  | 209/272 [44:09<13:18, 12.68s/it, avr_loss=0.0466]
steps:  77%|███████▋  | 209/272 [44:09<13:18, 12.68s/it, avr_loss=0.0469]
steps:  77%|███████▋  | 210/272 [44:22<13:05, 12.68s/it, avr_loss=0.0469]
steps:  77%|███████▋  | 210/272 [44:22<13:05, 12.68s/it, avr_loss=0.0484]
steps:  78%|███████▊  | 211/272 [44:34<12:53, 12.68s/it, avr_loss=0.0484]
steps:  78%|███████▊  | 211/272 [44:34<12:53, 12.68s/it, avr_loss=0.0481]
steps:  78%|███████▊  | 212/272 [44:47<12:40, 12.68s/it, avr_loss=0.0481]
steps:  78%|███████▊  | 212/272 [44:47<12:40, 12.68s/it, avr_loss=0.057] 
steps:  78%|███████▊  | 213/272 [44:59<12:27, 12.68s/it, avr_loss=0.057]
steps:  78%|███████▊  | 213/272 [44:59<12:27, 12.68s/it, avr_loss=0.0562]
steps:  79%|███████▊  | 214/272 [45:12<12:15, 12.67s/it, avr_loss=0.0562]
steps:  79%|███████▊  | 214/272 [45:12<12:15, 12.67s/it, avr_loss=0.056] 
steps:  79%|███████▉  | 215/272 [45:24<12:02, 12.67s/it, avr_loss=0.056]
steps:  79%|███████▉  | 215/272 [45:24<12:02, 12.67s/it, avr_loss=0.0562]
steps:  79%|███████▉  | 216/272 [45:37<11:49, 12.67s/it, avr_loss=0.0562]
steps:  79%|███████▉  | 216/272 [45:37<11:49, 12.67s/it, avr_loss=0.0559]
steps:  80%|███████▉  | 217/272 [45:50<11:37, 12.67s/it, avr_loss=0.0559]
steps:  80%|███████▉  | 217/272 [45:50<11:37, 12.67s/it, avr_loss=0.0562]
steps:  80%|████████  | 218/272 [46:02<11:24, 12.67s/it, avr_loss=0.0562]
steps:  80%|████████  | 218/272 [46:02<11:24, 12.67s/it, avr_loss=0.0567]
steps:  81%|████████  | 219/272 [46:15<11:11, 12.67s/it, avr_loss=0.0567]
steps:  81%|████████  | 219/272 [46:15<11:11, 12.67s/it, avr_loss=0.0564]
steps:  81%|████████  | 220/272 [46:27<10:58, 12.67s/it, avr_loss=0.0564]
steps:  81%|████████  | 220/272 [46:27<10:58, 12.67s/it, avr_loss=0.0506]
steps:  81%|████████▏ | 221/272 [46:40<10:46, 12.67s/it, avr_loss=0.0506]
steps:  81%|████████▏ | 221/272 [46:40<10:46, 12.67s/it, avr_loss=0.0507]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/block_handover_D435_10/train_lora-000013.safetensors

epoch 14/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14

steps:  82%|████████▏ | 222/272 [46:54<10:33, 12.68s/it, avr_loss=0.0507]
steps:  82%|████████▏ | 222/272 [46:54<10:33, 12.68s/it, avr_loss=0.0516]
steps:  82%|████████▏ | 223/272 [47:07<10:21, 12.68s/it, avr_loss=0.0516]
steps:  82%|████████▏ | 223/272 [47:07<10:21, 12.68s/it, avr_loss=0.0513]
steps:  82%|████████▏ | 224/272 [47:19<10:08, 12.68s/it, avr_loss=0.0513]
steps:  82%|████████▏ | 224/272 [47:19<10:08, 12.68s/it, avr_loss=0.0649]
steps:  83%|████████▎ | 225/272 [47:32<09:55, 12.68s/it, avr_loss=0.0649]
steps:  83%|████████▎ | 225/272 [47:32<09:55, 12.68s/it, avr_loss=0.0664]
steps:  83%|████████▎ | 226/272 [47:44<09:43, 12.68s/it, avr_loss=0.0664]
steps:  83%|████████▎ | 226/272 [47:44<09:43, 12.68s/it, avr_loss=0.0676]
steps:  83%|████████▎ | 227/272 [47:57<09:30, 12.68s/it, avr_loss=0.0676]
steps:  83%|████████▎ | 227/272 [47:57<09:30, 12.68s/it, avr_loss=0.067] 
steps:  84%|████████▍ | 228/272 [48:10<09:17, 12.68s/it, avr_loss=0.067]
steps:  84%|████████▍ | 228/272 [48:10<09:17, 12.68s/it, avr_loss=0.0671]
steps:  84%|████████▍ | 229/272 [48:22<09:05, 12.68s/it, avr_loss=0.0671]
steps:  84%|████████▍ | 229/272 [48:22<09:05, 12.68s/it, avr_loss=0.0579]
steps:  85%|████████▍ | 230/272 [48:35<08:52, 12.68s/it, avr_loss=0.0579]
steps:  85%|████████▍ | 230/272 [48:35<08:52, 12.68s/it, avr_loss=0.058] 
steps:  85%|████████▍ | 231/272 [48:47<08:39, 12.67s/it, avr_loss=0.058]
steps:  85%|████████▍ | 231/272 [48:47<08:39, 12.67s/it, avr_loss=0.0583]
steps:  85%|████████▌ | 232/272 [49:00<08:26, 12.67s/it, avr_loss=0.0583]
steps:  85%|████████▌ | 232/272 [49:00<08:26, 12.67s/it, avr_loss=0.0595]
steps:  86%|████████▌ | 233/272 [49:12<08:14, 12.67s/it, avr_loss=0.0595]
steps:  86%|████████▌ | 233/272 [49:12<08:14, 12.67s/it, avr_loss=0.0592]
steps:  86%|████████▌ | 234/272 [49:25<08:01, 12.67s/it, avr_loss=0.0592]
steps:  86%|████████▌ | 234/272 [49:25<08:01, 12.67s/it, avr_loss=0.0597]
steps:  86%|████████▋ | 235/272 [49:38<07:48, 12.67s/it, avr_loss=0.0597]
steps:  86%|████████▋ | 235/272 [49:38<07:48, 12.67s/it, avr_loss=0.061] 
steps:  87%|████████▋ | 236/272 [49:50<07:36, 12.67s/it, avr_loss=0.061]
steps:  87%|████████▋ | 236/272 [49:50<07:36, 12.67s/it, avr_loss=0.0621]
steps:  87%|████████▋ | 237/272 [50:03<07:23, 12.67s/it, avr_loss=0.0621]
steps:  87%|████████▋ | 237/272 [50:03<07:23, 12.67s/it, avr_loss=0.0624]
steps:  88%|████████▊ | 238/272 [50:15<07:10, 12.67s/it, avr_loss=0.0624]
steps:  88%|████████▊ | 238/272 [50:15<07:10, 12.67s/it, avr_loss=0.0623]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/block_handover_D435_10/train_lora-000014.safetensors

epoch 15/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15

steps:  88%|████████▊ | 239/272 [50:30<06:58, 12.68s/it, avr_loss=0.0623]
steps:  88%|████████▊ | 239/272 [50:30<06:58, 12.68s/it, avr_loss=0.0614]
steps:  88%|████████▊ | 240/272 [50:42<06:45, 12.68s/it, avr_loss=0.0614]
steps:  88%|████████▊ | 240/272 [50:42<06:45, 12.68s/it, avr_loss=0.0648]
steps:  89%|████████▊ | 241/272 [50:55<06:32, 12.68s/it, avr_loss=0.0648]
steps:  89%|████████▊ | 241/272 [50:55<06:32, 12.68s/it, avr_loss=0.0513]
steps:  89%|████████▉ | 242/272 [51:07<06:20, 12.68s/it, avr_loss=0.0513]
steps:  89%|████████▉ | 242/272 [51:07<06:20, 12.68s/it, avr_loss=0.0504]
steps:  89%|████████▉ | 243/272 [51:20<06:07, 12.68s/it, avr_loss=0.0504]
steps:  89%|████████▉ | 243/272 [51:20<06:07, 12.68s/it, avr_loss=0.0488]
steps:  90%|████████▉ | 244/272 [51:32<05:54, 12.68s/it, avr_loss=0.0488]
steps:  90%|████████▉ | 244/272 [51:32<05:54, 12.68s/it, avr_loss=0.0495]
steps:  90%|█████████ | 245/272 [51:45<05:42, 12.68s/it, avr_loss=0.0495]
steps:  90%|█████████ | 245/272 [51:45<05:42, 12.68s/it, avr_loss=0.0494]
steps:  90%|█████████ | 246/272 [51:58<05:29, 12.67s/it, avr_loss=0.0494]
steps:  90%|█████████ | 246/272 [51:58<05:29, 12.67s/it, avr_loss=0.0496]
steps:  91%|█████████ | 247/272 [52:10<05:16, 12.67s/it, avr_loss=0.0496]
steps:  91%|█████████ | 247/272 [52:10<05:16, 12.67s/it, avr_loss=0.0506]
steps:  91%|█████████ | 248/272 [52:23<05:04, 12.67s/it, avr_loss=0.0506]
steps:  91%|█████████ | 248/272 [52:23<05:04, 12.67s/it, avr_loss=0.0502]
steps:  92%|█████████▏| 249/272 [52:35<04:51, 12.67s/it, avr_loss=0.0502]
steps:  92%|█████████▏| 249/272 [52:35<04:51, 12.67s/it, avr_loss=0.0489]
steps:  92%|█████████▏| 250/272 [52:48<04:38, 12.67s/it, avr_loss=0.0489]
steps:  92%|█████████▏| 250/272 [52:48<04:38, 12.67s/it, avr_loss=0.0491]
steps:  92%|█████████▏| 251/272 [53:00<04:26, 12.67s/it, avr_loss=0.0491]
steps:  92%|█████████▏| 251/272 [53:00<04:26, 12.67s/it, avr_loss=0.048] 
steps:  93%|█████████▎| 252/272 [53:13<04:13, 12.67s/it, avr_loss=0.048]
steps:  93%|█████████▎| 252/272 [53:13<04:13, 12.67s/it, avr_loss=0.0466]
steps:  93%|█████████▎| 253/272 [53:25<04:00, 12.67s/it, avr_loss=0.0466]
steps:  93%|█████████▎| 253/272 [53:25<04:00, 12.67s/it, avr_loss=0.0451]
steps:  93%|█████████▎| 254/272 [53:38<03:48, 12.67s/it, avr_loss=0.0451]
steps:  93%|█████████▎| 254/272 [53:38<03:48, 12.67s/it, avr_loss=0.0445]
steps:  94%|█████████▍| 255/272 [53:50<03:35, 12.67s/it, avr_loss=0.0445]
steps:  94%|█████████▍| 255/272 [53:50<03:35, 12.67s/it, avr_loss=0.0448]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/block_handover_D435_10/train_lora-000015.safetensors

epoch 16/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16

steps:  94%|█████████▍| 256/272 [54:05<03:22, 12.68s/it, avr_loss=0.0448]
steps:  94%|█████████▍| 256/272 [54:05<03:22, 12.68s/it, avr_loss=0.0454]
steps:  94%|█████████▍| 257/272 [54:17<03:10, 12.68s/it, avr_loss=0.0454]
steps:  94%|█████████▍| 257/272 [54:17<03:10, 12.68s/it, avr_loss=0.0425]
steps:  95%|█████████▍| 258/272 [54:30<02:57, 12.68s/it, avr_loss=0.0425]
steps:  95%|█████████▍| 258/272 [54:30<02:57, 12.68s/it, avr_loss=0.0423]
steps:  95%|█████████▌| 259/272 [54:42<02:44, 12.68s/it, avr_loss=0.0423]
steps:  95%|█████████▌| 259/272 [54:42<02:44, 12.68s/it, avr_loss=0.0424]
steps:  96%|█████████▌| 260/272 [54:55<02:32, 12.68s/it, avr_loss=0.0424]
steps:  96%|█████████▌| 260/272 [54:55<02:32, 12.68s/it, avr_loss=0.0429]
steps:  96%|█████████▌| 261/272 [55:08<02:19, 12.67s/it, avr_loss=0.0429]
steps:  96%|█████████▌| 261/272 [55:08<02:19, 12.67s/it, avr_loss=0.0415]
steps:  96%|█████████▋| 262/272 [55:20<02:06, 12.67s/it, avr_loss=0.0415]
steps:  96%|█████████▋| 262/272 [55:20<02:06, 12.67s/it, avr_loss=0.0415]
steps:  97%|█████████▋| 263/272 [55:33<01:54, 12.67s/it, avr_loss=0.0415]
steps:  97%|█████████▋| 263/272 [55:33<01:54, 12.67s/it, avr_loss=0.0444]
steps:  97%|█████████▋| 264/272 [55:45<01:41, 12.67s/it, avr_loss=0.0444]
steps:  97%|█████████▋| 264/272 [55:45<01:41, 12.67s/it, avr_loss=0.0427]
steps:  97%|█████████▋| 265/272 [55:58<01:28, 12.67s/it, avr_loss=0.0427]
steps:  97%|█████████▋| 265/272 [55:58<01:28, 12.67s/it, avr_loss=0.0426]
steps:  98%|█████████▊| 266/272 [56:10<01:16, 12.67s/it, avr_loss=0.0426]
steps:  98%|█████████▊| 266/272 [56:10<01:16, 12.67s/it, avr_loss=0.0452]
steps:  98%|█████████▊| 267/272 [56:23<01:03, 12.67s/it, avr_loss=0.0452]
steps:  98%|█████████▊| 267/272 [56:23<01:03, 12.67s/it, avr_loss=0.0459]
steps:  99%|█████████▊| 268/272 [56:35<00:50, 12.67s/it, avr_loss=0.0459]
steps:  99%|█████████▊| 268/272 [56:35<00:50, 12.67s/it, avr_loss=0.0461]
steps:  99%|█████████▉| 269/272 [56:48<00:38, 12.67s/it, avr_loss=0.0461]
steps:  99%|█████████▉| 269/272 [56:48<00:38, 12.67s/it, avr_loss=0.0456]
steps:  99%|█████████▉| 270/272 [57:01<00:25, 12.67s/it, avr_loss=0.0456]
steps:  99%|█████████▉| 270/272 [57:01<00:25, 12.67s/it, avr_loss=0.0456]
steps: 100%|█████████▉| 271/272 [57:13<00:12, 12.67s/it, avr_loss=0.0456]
steps: 100%|█████████▉| 271/272 [57:13<00:12, 12.67s/it, avr_loss=0.0465]
steps: 100%|██████████| 272/272 [57:26<00:00, 12.67s/it, avr_loss=0.0465]
steps: 100%|██████████| 272/272 [57:26<00:00, 12.67s/it, avr_loss=0.0466]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/block_handover_D435_10/train_lora.safetensors
INFO:hv_train_network:model saved.

steps: 100%|██████████| 272/272 [57:28<00:00, 12.68s/it, avr_loss=0.0466]
