                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        
steps:   0%|          | 1/10016 [00:13<38:29:26, 13.84s/it]
steps:   0%|          | 1/10016 [00:13<38:29:31, 13.84s/it, avr_loss=0.0273]
steps:   0%|          | 2/10016 [00:26<37:00:51, 13.31s/it, avr_loss=0.0273]
steps:   0%|          | 2/10016 [00:26<37:00:53, 13.31s/it, avr_loss=0.0295]
steps:   0%|          | 3/10016 [00:39<36:35:39, 13.16s/it, avr_loss=0.0295]
steps:   0%|          | 3/10016 [00:39<36:35:40, 13.16s/it, avr_loss=0.0301]
steps:   0%|          | 4/10016 [00:52<36:26:32, 13.10s/it, avr_loss=0.0301]
steps:   0%|          | 4/10016 [00:52<36:26:33, 13.10s/it, avr_loss=0.0278]
steps:   0%|          | 5/10016 [01:05<36:20:48, 13.07s/it, avr_loss=0.0278]
steps:   0%|          | 5/10016 [01:05<36:20:48, 13.07s/it, avr_loss=0.0284]
steps:   0%|          | 6/10016 [01:18<36:16:29, 13.05s/it, avr_loss=0.0284]
steps:   0%|          | 6/10016 [01:18<36:16:29, 13.05s/it, avr_loss=0.0279]
steps:   0%|          | 7/10016 [01:31<36:12:56, 13.03s/it, avr_loss=0.0279]
steps:   0%|          | 7/10016 [01:31<36:12:57, 13.03s/it, avr_loss=0.0272]
steps:   0%|          | 8/10016 [01:44<36:10:23, 13.01s/it, avr_loss=0.0272]
steps:   0%|          | 8/10016 [01:44<36:10:24, 13.01s/it, avr_loss=0.027] 
steps:   0%|          | 9/10016 [01:56<36:08:04, 13.00s/it, avr_loss=0.027]
steps:   0%|          | 9/10016 [01:56<36:08:04, 13.00s/it, avr_loss=0.0276]
steps:   0%|          | 10/10016 [02:09<36:06:18, 12.99s/it, avr_loss=0.0276]
steps:   0%|          | 10/10016 [02:09<36:06:18, 12.99s/it, avr_loss=0.0304]
steps:   0%|          | 11/10016 [02:22<36:04:40, 12.98s/it, avr_loss=0.0304]
steps:   0%|          | 11/10016 [02:22<36:04:41, 12.98s/it, avr_loss=0.0297]
steps:   0%|          | 12/10016 [02:35<36:03:09, 12.97s/it, avr_loss=0.0297]
steps:   0%|          | 12/10016 [02:35<36:03:09, 12.97s/it, avr_loss=0.0306]
steps:   0%|          | 13/10016 [02:48<36:01:52, 12.97s/it, avr_loss=0.0306]
steps:   0%|          | 13/10016 [02:48<36:01:52, 12.97s/it, avr_loss=0.0297]
steps:   0%|          | 14/10016 [03:01<36:00:45, 12.96s/it, avr_loss=0.0297]
steps:   0%|          | 14/10016 [03:01<36:00:45, 12.96s/it, avr_loss=0.0299]
steps:   0%|          | 15/10016 [03:14<35:59:43, 12.96s/it, avr_loss=0.0299]
steps:   0%|          | 15/10016 [03:14<35:59:43, 12.96s/it, avr_loss=0.0299]
steps:   0%|          | 16/10016 [03:27<35:58:54, 12.95s/it, avr_loss=0.0299]
steps:   0%|          | 16/10016 [03:27<35:58:54, 12.95s/it, avr_loss=0.0306]
steps:   0%|          | 17/10016 [03:40<35:58:03, 12.95s/it, avr_loss=0.0306]
steps:   0%|          | 17/10016 [03:40<35:58:03, 12.95s/it, avr_loss=0.0304]
steps:   0%|          | 18/10016 [03:53<35:57:16, 12.95s/it, avr_loss=0.0304]
steps:   0%|          | 18/10016 [03:53<35:57:16, 12.95s/it, avr_loss=0.0301]
steps:   0%|          | 19/10016 [04:05<35:56:32, 12.94s/it, avr_loss=0.0301]
steps:   0%|          | 19/10016 [04:05<35:56:32, 12.94s/it, avr_loss=0.0305]
steps:   0%|          | 20/10016 [04:18<35:56:00, 12.94s/it, avr_loss=0.0305]
steps:   0%|          | 20/10016 [04:18<35:56:00, 12.94s/it, avr_loss=0.03]  
steps:   0%|          | 21/10016 [04:31<35:55:25, 12.94s/it, avr_loss=0.03]
steps:   0%|          | 21/10016 [04:31<35:55:25, 12.94s/it, avr_loss=0.0329]
steps:   0%|          | 22/10016 [04:44<35:54:54, 12.94s/it, avr_loss=0.0329]
steps:   0%|          | 22/10016 [04:44<35:54:54, 12.94s/it, avr_loss=0.0326]
steps:   0%|          | 23/10016 [04:57<35:54:22, 12.94s/it, avr_loss=0.0326]
steps:   0%|          | 23/10016 [04:57<35:54:22, 12.94s/it, avr_loss=0.0333]
steps:   0%|          | 24/10016 [05:10<35:53:54, 12.93s/it, avr_loss=0.0333]
steps:   0%|          | 24/10016 [05:10<35:53:54, 12.93s/it, avr_loss=0.0333]
steps:   0%|          | 25/10016 [05:23<35:53:28, 12.93s/it, avr_loss=0.0333]
steps:   0%|          | 25/10016 [05:23<35:53:28, 12.93s/it, avr_loss=0.0329]
steps:   0%|          | 26/10016 [05:36<35:53:04, 12.93s/it, avr_loss=0.0329]
steps:   0%|          | 26/10016 [05:36<35:53:04, 12.93s/it, avr_loss=0.0326]
steps:   0%|          | 27/10016 [05:49<35:52:42, 12.93s/it, avr_loss=0.0326]
steps:   0%|          | 27/10016 [05:49<35:52:42, 12.93s/it, avr_loss=0.0338]
steps:   0%|          | 28/10016 [06:02<35:52:20, 12.93s/it, avr_loss=0.0338]
steps:   0%|          | 28/10016 [06:02<35:52:20, 12.93s/it, avr_loss=0.0335]
steps:   0%|          | 29/10016 [06:14<35:51:57, 12.93s/it, avr_loss=0.0335]
steps:   0%|          | 29/10016 [06:14<35:51:58, 12.93s/it, avr_loss=0.0335]
steps:   0%|          | 30/10016 [06:27<35:51:33, 12.93s/it, avr_loss=0.0335]
steps:   0%|          | 30/10016 [06:27<35:51:33, 12.93s/it, avr_loss=0.0335]
steps:   0%|          | 31/10016 [06:40<35:51:06, 12.93s/it, avr_loss=0.0335]
steps:   0%|          | 31/10016 [06:40<35:51:06, 12.93s/it, avr_loss=0.0333]
steps:   0%|          | 32/10016 [06:53<35:50:44, 12.93s/it, avr_loss=0.0333]
steps:   0%|          | 32/10016 [06:53<35:50:44, 12.93s/it, avr_loss=0.0332]
steps:   0%|          | 33/10016 [07:06<35:50:23, 12.92s/it, avr_loss=0.0332]
steps:   0%|          | 33/10016 [07:06<35:50:23, 12.92s/it, avr_loss=0.0331]
steps:   0%|          | 34/10016 [07:19<35:50:03, 12.92s/it, avr_loss=0.0331]
steps:   0%|          | 34/10016 [07:19<35:50:03, 12.92s/it, avr_loss=0.033] 
steps:   0%|          | 35/10016 [07:32<35:49:42, 12.92s/it, avr_loss=0.033]
steps:   0%|          | 35/10016 [07:32<35:49:42, 12.92s/it, avr_loss=0.0328]
steps:   0%|          | 36/10016 [07:45<35:49:22, 12.92s/it, avr_loss=0.0328]
steps:   0%|          | 36/10016 [07:45<35:49:22, 12.92s/it, avr_loss=0.0324]
steps:   0%|          | 37/10016 [07:58<35:49:03, 12.92s/it, avr_loss=0.0324]
steps:   0%|          | 37/10016 [07:58<35:49:03, 12.92s/it, avr_loss=0.0323]
steps:   0%|          | 38/10016 [08:10<35:48:42, 12.92s/it, avr_loss=0.0323]
steps:   0%|          | 38/10016 [08:10<35:48:42, 12.92s/it, avr_loss=0.032] 
steps:   0%|          | 39/10016 [08:23<35:48:21, 12.92s/it, avr_loss=0.032]
steps:   0%|          | 39/10016 [08:23<35:48:21, 12.92s/it, avr_loss=0.0318]
steps:   0%|          | 40/10016 [08:36<35:48:02, 12.92s/it, avr_loss=0.0318]
steps:   0%|          | 40/10016 [08:36<35:48:02, 12.92s/it, avr_loss=0.0316]
steps:   0%|          | 41/10016 [08:49<35:47:44, 12.92s/it, avr_loss=0.0316]
steps:   0%|          | 41/10016 [08:49<35:47:44, 12.92s/it, avr_loss=0.0314]
steps:   0%|          | 42/10016 [09:02<35:47:26, 12.92s/it, avr_loss=0.0314]
steps:   0%|          | 42/10016 [09:02<35:47:26, 12.92s/it, avr_loss=0.0312]
steps:   0%|          | 43/10016 [09:15<35:47:08, 12.92s/it, avr_loss=0.0312]
steps:   0%|          | 43/10016 [09:15<35:47:08, 12.92s/it, avr_loss=0.0311]
steps:   0%|          | 44/10016 [09:28<35:46:50, 12.92s/it, avr_loss=0.0311]
steps:   0%|          | 44/10016 [09:28<35:46:50, 12.92s/it, avr_loss=0.0308]
steps:   0%|          | 45/10016 [09:41<35:46:33, 12.92s/it, avr_loss=0.0308]
steps:   0%|          | 45/10016 [09:41<35:46:33, 12.92s/it, avr_loss=0.0305]
steps:   0%|          | 46/10016 [09:54<35:46:13, 12.92s/it, avr_loss=0.0305]
steps:   0%|          | 46/10016 [09:54<35:46:14, 12.92s/it, avr_loss=0.0319]
steps:   0%|          | 47/10016 [10:07<35:45:56, 12.92s/it, avr_loss=0.0319]
steps:   0%|          | 47/10016 [10:07<35:45:56, 12.92s/it, avr_loss=0.0322]
steps:   0%|          | 48/10016 [10:19<35:45:37, 12.92s/it, avr_loss=0.0322]
steps:   0%|          | 48/10016 [10:19<35:45:37, 12.92s/it, avr_loss=0.032] 
steps:   0%|          | 49/10016 [10:32<35:45:20, 12.91s/it, avr_loss=0.032]
steps:   0%|          | 49/10016 [10:32<35:45:20, 12.91s/it, avr_loss=0.0319]
steps:   0%|          | 50/10016 [10:45<35:45:03, 12.91s/it, avr_loss=0.0319]
steps:   0%|          | 50/10016 [10:45<35:45:03, 12.91s/it, avr_loss=0.0317]
steps:   1%|          | 51/10016 [10:58<35:44:48, 12.91s/it, avr_loss=0.0317]
steps:   1%|          | 51/10016 [10:58<35:44:48, 12.91s/it, avr_loss=0.0315]
steps:   1%|          | 52/10016 [11:11<35:44:31, 12.91s/it, avr_loss=0.0315]
steps:   1%|          | 52/10016 [11:11<35:44:31, 12.91s/it, avr_loss=0.0314]
steps:   1%|          | 53/10016 [11:24<35:44:15, 12.91s/it, avr_loss=0.0314]
steps:   1%|          | 53/10016 [11:24<35:44:15, 12.91s/it, avr_loss=0.0313]
steps:   1%|          | 54/10016 [11:37<35:44:00, 12.91s/it, avr_loss=0.0313]
steps:   1%|          | 54/10016 [11:37<35:44:00, 12.91s/it, avr_loss=0.031] 
steps:   1%|          | 55/10016 [11:50<35:43:45, 12.91s/it, avr_loss=0.031]
steps:   1%|          | 55/10016 [11:50<35:43:45, 12.91s/it, avr_loss=0.031]
steps:   1%|          | 56/10016 [12:03<35:43:30, 12.91s/it, avr_loss=0.031]
steps:   1%|          | 56/10016 [12:03<35:43:30, 12.91s/it, avr_loss=0.0309]
steps:   1%|          | 57/10016 [12:16<35:43:13, 12.91s/it, avr_loss=0.0309]
steps:   1%|          | 57/10016 [12:16<35:43:13, 12.91s/it, avr_loss=0.0308]
steps:   1%|          | 58/10016 [12:28<35:42:56, 12.91s/it, avr_loss=0.0308]
steps:   1%|          | 58/10016 [12:28<35:42:56, 12.91s/it, avr_loss=0.0307]
steps:   1%|          | 59/10016 [12:41<35:42:40, 12.91s/it, avr_loss=0.0307]
steps:   1%|          | 59/10016 [12:41<35:42:40, 12.91s/it, avr_loss=0.0307]
steps:   1%|          | 60/10016 [12:54<35:42:22, 12.91s/it, avr_loss=0.0307]
steps:   1%|          | 60/10016 [12:54<35:42:22, 12.91s/it, avr_loss=0.0307]
steps:   1%|          | 61/10016 [13:07<35:42:07, 12.91s/it, avr_loss=0.0307]
steps:   1%|          | 61/10016 [13:07<35:42:07, 12.91s/it, avr_loss=0.0305]
steps:   1%|          | 62/10016 [13:20<35:41:53, 12.91s/it, avr_loss=0.0305]
steps:   1%|          | 62/10016 [13:20<35:41:53, 12.91s/it, avr_loss=0.0305]
steps:   1%|          | 63/10016 [13:33<35:41:37, 12.91s/it, avr_loss=0.0305]
steps:   1%|          | 63/10016 [13:33<35:41:37, 12.91s/it, avr_loss=0.0305]
steps:   1%|          | 64/10016 [13:46<35:41:21, 12.91s/it, avr_loss=0.0305]
steps:   1%|          | 64/10016 [13:46<35:41:21, 12.91s/it, avr_loss=0.0305]
steps:   1%|          | 65/10016 [13:59<35:41:08, 12.91s/it, avr_loss=0.0305]
steps:   1%|          | 65/10016 [13:59<35:41:08, 12.91s/it, avr_loss=0.0304]
steps:   1%|          | 66/10016 [14:12<35:40:52, 12.91s/it, avr_loss=0.0304]
steps:   1%|          | 66/10016 [14:12<35:40:52, 12.91s/it, avr_loss=0.0303]
steps:   1%|          | 67/10016 [14:24<35:40:38, 12.91s/it, avr_loss=0.0303]
steps:   1%|          | 67/10016 [14:24<35:40:38, 12.91s/it, avr_loss=0.0301]
steps:   1%|          | 68/10016 [14:37<35:40:24, 12.91s/it, avr_loss=0.0301]
steps:   1%|          | 68/10016 [14:37<35:40:24, 12.91s/it, avr_loss=0.03]  
steps:   1%|          | 69/10016 [14:50<35:40:08, 12.91s/it, avr_loss=0.03]
steps:   1%|          | 69/10016 [14:50<35:40:08, 12.91s/it, avr_loss=0.0299]
steps:   1%|          | 70/10016 [15:03<35:39:54, 12.91s/it, avr_loss=0.0299]
steps:   1%|          | 70/10016 [15:03<35:39:54, 12.91s/it, avr_loss=0.0297]
steps:   1%|          | 71/10016 [15:16<35:39:39, 12.91s/it, avr_loss=0.0297]
steps:   1%|          | 71/10016 [15:16<35:39:39, 12.91s/it, avr_loss=0.0297]
steps:   1%|          | 72/10016 [15:29<35:39:25, 12.91s/it, avr_loss=0.0297]
steps:   1%|          | 72/10016 [15:29<35:39:25, 12.91s/it, avr_loss=0.0296]
steps:   1%|          | 73/10016 [15:42<35:39:11, 12.91s/it, avr_loss=0.0296]
steps:   1%|          | 73/10016 [15:42<35:39:11, 12.91s/it, avr_loss=0.0296]
steps:   1%|          | 74/10016 [15:55<35:38:56, 12.91s/it, avr_loss=0.0296]
steps:   1%|          | 74/10016 [15:55<35:38:56, 12.91s/it, avr_loss=0.0295]
steps:   1%|          | 75/10016 [16:08<35:38:42, 12.91s/it, avr_loss=0.0295]
steps:   1%|          | 75/10016 [16:08<35:38:42, 12.91s/it, avr_loss=0.0294]
steps:   1%|          | 76/10016 [16:21<35:38:27, 12.91s/it, avr_loss=0.0294]
steps:   1%|          | 76/10016 [16:21<35:38:27, 12.91s/it, avr_loss=0.0293]
steps:   1%|          | 77/10016 [16:33<35:38:12, 12.91s/it, avr_loss=0.0293]
steps:   1%|          | 77/10016 [16:33<35:38:12, 12.91s/it, avr_loss=0.0292]
steps:   1%|          | 78/10016 [16:46<35:37:57, 12.91s/it, avr_loss=0.0292]
steps:   1%|          | 78/10016 [16:46<35:37:57, 12.91s/it, avr_loss=0.0295]
steps:   1%|          | 79/10016 [16:59<35:37:42, 12.91s/it, avr_loss=0.0295]
steps:   1%|          | 79/10016 [16:59<35:37:42, 12.91s/it, avr_loss=0.0294]
steps:   1%|          | 80/10016 [17:12<35:37:26, 12.91s/it, avr_loss=0.0294]
steps:   1%|          | 80/10016 [17:12<35:37:26, 12.91s/it, avr_loss=0.0306]
steps:   1%|          | 81/10016 [17:25<35:37:11, 12.91s/it, avr_loss=0.0306]
steps:   1%|          | 81/10016 [17:25<35:37:11, 12.91s/it, avr_loss=0.0305]
steps:   1%|          | 82/10016 [17:38<35:36:55, 12.91s/it, avr_loss=0.0305]
steps:   1%|          | 82/10016 [17:38<35:36:55, 12.91s/it, avr_loss=0.0306]
steps:   1%|          | 83/10016 [17:51<35:36:41, 12.91s/it, avr_loss=0.0306]
steps:   1%|          | 83/10016 [17:51<35:36:41, 12.91s/it, avr_loss=0.0305]
steps:   1%|          | 84/10016 [18:04<35:36:24, 12.91s/it, avr_loss=0.0305]
steps:   1%|          | 84/10016 [18:04<35:36:24, 12.91s/it, avr_loss=0.0318]
steps:   1%|          | 85/10016 [18:17<35:36:10, 12.91s/it, avr_loss=0.0318]
steps:   1%|          | 85/10016 [18:17<35:36:10, 12.91s/it, avr_loss=0.0317]
steps:   1%|          | 86/10016 [18:29<35:35:56, 12.91s/it, avr_loss=0.0317]
steps:   1%|          | 86/10016 [18:29<35:35:56, 12.91s/it, avr_loss=0.0317]
steps:   1%|          | 87/10016 [18:42<35:35:43, 12.91s/it, avr_loss=0.0317]
steps:   1%|          | 87/10016 [18:42<35:35:43, 12.91s/it, avr_loss=0.0316]
steps:   1%|          | 88/10016 [18:55<35:35:28, 12.91s/it, avr_loss=0.0316]
steps:   1%|          | 88/10016 [18:55<35:35:28, 12.91s/it, avr_loss=0.0315]
steps:   1%|          | 89/10016 [19:08<35:35:13, 12.91s/it, avr_loss=0.0315]
steps:   1%|          | 89/10016 [19:08<35:35:13, 12.91s/it, avr_loss=0.0314]
steps:   1%|          | 90/10016 [19:21<35:35:00, 12.91s/it, avr_loss=0.0314]
steps:   1%|          | 90/10016 [19:21<35:35:00, 12.91s/it, avr_loss=0.0316]
steps:   1%|          | 91/10016 [19:34<35:34:45, 12.91s/it, avr_loss=0.0316]
steps:   1%|          | 91/10016 [19:34<35:34:45, 12.91s/it, avr_loss=0.0315]
steps:   1%|          | 92/10016 [19:47<35:34:30, 12.91s/it, avr_loss=0.0315]
steps:   1%|          | 92/10016 [19:47<35:34:30, 12.91s/it, avr_loss=0.0314]
steps:   1%|          | 93/10016 [20:00<35:34:16, 12.91s/it, avr_loss=0.0314]
steps:   1%|          | 93/10016 [20:00<35:34:16, 12.91s/it, avr_loss=0.0315]
steps:   1%|          | 94/10016 [20:13<35:34:13, 12.91s/it, avr_loss=0.0315]
steps:   1%|          | 94/10016 [20:13<35:34:13, 12.91s/it, avr_loss=0.0322]
steps:   1%|          | 95/10016 [20:26<35:33:56, 12.91s/it, avr_loss=0.0322]
steps:   1%|          | 95/10016 [20:26<35:33:56, 12.91s/it, avr_loss=0.0323]
steps:   1%|          | 96/10016 [20:38<35:33:40, 12.91s/it, avr_loss=0.0323]
steps:   1%|          | 96/10016 [20:38<35:33:40, 12.91s/it, avr_loss=0.0323]
steps:   1%|          | 97/10016 [20:51<35:33:23, 12.90s/it, avr_loss=0.0323]
steps:   1%|          | 97/10016 [20:51<35:33:23, 12.90s/it, avr_loss=0.0321]
steps:   1%|          | 98/10016 [21:04<35:33:07, 12.90s/it, avr_loss=0.0321]
steps:   1%|          | 98/10016 [21:04<35:33:07, 12.90s/it, avr_loss=0.0321]
steps:   1%|          | 99/10016 [21:17<35:32:49, 12.90s/it, avr_loss=0.0321]
steps:   1%|          | 99/10016 [21:17<35:32:49, 12.90s/it, avr_loss=0.032] 
steps:   1%|          | 100/10016 [21:30<35:32:34, 12.90s/it, avr_loss=0.032]
steps:   1%|          | 100/10016 [21:30<35:32:34, 12.90s/it, avr_loss=0.0319]
steps:   1%|          | 101/10016 [21:43<35:32:18, 12.90s/it, avr_loss=0.0319]
steps:   1%|          | 101/10016 [21:43<35:32:18, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 102/10016 [21:56<35:32:02, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 102/10016 [21:56<35:32:02, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 103/10016 [22:09<35:31:46, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 103/10016 [22:09<35:31:46, 12.90s/it, avr_loss=0.0319]
steps:   1%|          | 104/10016 [22:21<35:31:30, 12.90s/it, avr_loss=0.0319]
steps:   1%|          | 104/10016 [22:21<35:31:30, 12.90s/it, avr_loss=0.0319]
steps:   1%|          | 105/10016 [22:34<35:31:15, 12.90s/it, avr_loss=0.0319]
steps:   1%|          | 105/10016 [22:34<35:31:15, 12.90s/it, avr_loss=0.0319]
steps:   1%|          | 106/10016 [22:47<35:31:00, 12.90s/it, avr_loss=0.0319]
steps:   1%|          | 106/10016 [22:47<35:31:00, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 107/10016 [23:00<35:30:44, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 107/10016 [23:00<35:30:44, 12.90s/it, avr_loss=0.0319]
steps:   1%|          | 108/10016 [23:13<35:30:29, 12.90s/it, avr_loss=0.0319]
steps:   1%|          | 108/10016 [23:13<35:30:29, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 109/10016 [23:26<35:30:14, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 109/10016 [23:26<35:30:14, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 110/10016 [23:39<35:29:58, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 110/10016 [23:39<35:29:58, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 111/10016 [23:51<35:29:42, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 111/10016 [23:51<35:29:42, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 112/10016 [24:04<35:29:27, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 112/10016 [24:04<35:29:27, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 113/10016 [24:17<35:29:11, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 113/10016 [24:17<35:29:11, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 114/10016 [24:30<35:28:56, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 114/10016 [24:30<35:28:56, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 115/10016 [24:43<35:28:40, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 115/10016 [24:43<35:28:40, 12.90s/it, avr_loss=0.0319]
steps:   1%|          | 116/10016 [24:56<35:28:24, 12.90s/it, avr_loss=0.0319]
steps:   1%|          | 116/10016 [24:56<35:28:25, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 117/10016 [25:09<35:28:09, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 117/10016 [25:09<35:28:09, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 118/10016 [25:22<35:27:54, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 118/10016 [25:22<35:27:54, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 119/10016 [25:35<35:27:49, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 119/10016 [25:35<35:27:49, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 120/10016 [25:47<35:27:34, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 120/10016 [25:47<35:27:34, 12.90s/it, avr_loss=0.0316]
steps:   1%|          | 121/10016 [26:00<35:27:20, 12.90s/it, avr_loss=0.0316]
steps:   1%|          | 121/10016 [26:00<35:27:20, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 122/10016 [26:13<35:27:04, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 122/10016 [26:13<35:27:04, 12.90s/it, avr_loss=0.0319]
steps:   1%|          | 123/10016 [26:26<35:26:49, 12.90s/it, avr_loss=0.0319]
steps:   1%|          | 123/10016 [26:26<35:26:49, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 124/10016 [26:39<35:26:35, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 124/10016 [26:39<35:26:35, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 125/10016 [26:52<35:26:20, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 125/10016 [26:52<35:26:20, 12.90s/it, avr_loss=0.0316]
steps:   1%|▏         | 126/10016 [27:05<35:26:06, 12.90s/it, avr_loss=0.0316]
steps:   1%|▏         | 126/10016 [27:05<35:26:06, 12.90s/it, avr_loss=0.0316]
steps:   1%|▏         | 127/10016 [27:18<35:25:51, 12.90s/it, avr_loss=0.0316]
steps:   1%|▏         | 127/10016 [27:18<35:25:51, 12.90s/it, avr_loss=0.0315]
steps:   1%|▏         | 128/10016 [27:30<35:25:36, 12.90s/it, avr_loss=0.0315]
steps:   1%|▏         | 128/10016 [27:30<35:25:36, 12.90s/it, avr_loss=0.0314]
steps:   1%|▏         | 129/10016 [27:43<35:25:21, 12.90s/it, avr_loss=0.0314]
steps:   1%|▏         | 129/10016 [27:43<35:25:21, 12.90s/it, avr_loss=0.0314]
steps:   1%|▏         | 130/10016 [27:56<35:25:06, 12.90s/it, avr_loss=0.0314]
steps:   1%|▏         | 130/10016 [27:56<35:25:06, 12.90s/it, avr_loss=0.0314]
steps:   1%|▏         | 131/10016 [28:09<35:24:51, 12.90s/it, avr_loss=0.0314]
steps:   1%|▏         | 131/10016 [28:09<35:24:51, 12.90s/it, avr_loss=0.0313]
steps:   1%|▏         | 132/10016 [28:22<35:24:37, 12.90s/it, avr_loss=0.0313]
steps:   1%|▏         | 132/10016 [28:22<35:24:37, 12.90s/it, avr_loss=0.0312]
steps:   1%|▏         | 133/10016 [28:35<35:24:22, 12.90s/it, avr_loss=0.0312]
steps:   1%|▏         | 133/10016 [28:35<35:24:22, 12.90s/it, avr_loss=0.0311]
steps:   1%|▏         | 134/10016 [28:48<35:24:07, 12.90s/it, avr_loss=0.0311]
steps:   1%|▏         | 134/10016 [28:48<35:24:07, 12.90s/it, avr_loss=0.0312]
steps:   1%|▏         | 135/10016 [29:01<35:23:52, 12.90s/it, avr_loss=0.0312]
steps:   1%|▏         | 135/10016 [29:01<35:23:52, 12.90s/it, avr_loss=0.0311]
steps:   1%|▏         | 136/10016 [29:13<35:23:37, 12.90s/it, avr_loss=0.0311]
steps:   1%|▏         | 136/10016 [29:13<35:23:37, 12.90s/it, avr_loss=0.031] 
steps:   1%|▏         | 137/10016 [29:26<35:23:22, 12.90s/it, avr_loss=0.031]
steps:   1%|▏         | 137/10016 [29:26<35:23:22, 12.90s/it, avr_loss=0.0309]
steps:   1%|▏         | 138/10016 [29:39<35:23:07, 12.90s/it, avr_loss=0.0309]
steps:   1%|▏         | 138/10016 [29:39<35:23:07, 12.90s/it, avr_loss=0.0308]
steps:   1%|▏         | 139/10016 [29:52<35:22:53, 12.90s/it, avr_loss=0.0308]
steps:   1%|▏         | 139/10016 [29:52<35:22:53, 12.90s/it, avr_loss=0.0308]
steps:   1%|▏         | 140/10016 [30:05<35:22:38, 12.90s/it, avr_loss=0.0308]
steps:   1%|▏         | 140/10016 [30:05<35:22:38, 12.90s/it, avr_loss=0.0307]
steps:   1%|▏         | 141/10016 [30:18<35:22:24, 12.90s/it, avr_loss=0.0307]
steps:   1%|▏         | 141/10016 [30:18<35:22:24, 12.90s/it, avr_loss=0.0307]
steps:   1%|▏         | 142/10016 [30:31<35:22:10, 12.90s/it, avr_loss=0.0307]
steps:   1%|▏         | 142/10016 [30:31<35:22:10, 12.90s/it, avr_loss=0.0306]
steps:   1%|▏         | 143/10016 [30:44<35:21:55, 12.90s/it, avr_loss=0.0306]
steps:   1%|▏         | 143/10016 [30:44<35:21:55, 12.90s/it, avr_loss=0.0306]
steps:   1%|▏         | 144/10016 [30:56<35:21:42, 12.90s/it, avr_loss=0.0306]
steps:   1%|▏         | 144/10016 [30:56<35:21:42, 12.90s/it, avr_loss=0.0305]
steps:   1%|▏         | 145/10016 [31:09<35:21:28, 12.90s/it, avr_loss=0.0305]
steps:   1%|▏         | 145/10016 [31:09<35:21:28, 12.90s/it, avr_loss=0.0305]
steps:   1%|▏         | 146/10016 [31:22<35:21:14, 12.90s/it, avr_loss=0.0305]
steps:   1%|▏         | 146/10016 [31:22<35:21:14, 12.90s/it, avr_loss=0.0309]
steps:   1%|▏         | 147/10016 [31:35<35:21:00, 12.89s/it, avr_loss=0.0309]
steps:   1%|▏         | 147/10016 [31:35<35:21:00, 12.90s/it, avr_loss=0.0308]
steps:   1%|▏         | 148/10016 [31:48<35:20:46, 12.89s/it, avr_loss=0.0308]
steps:   1%|▏         | 148/10016 [31:48<35:20:46, 12.89s/it, avr_loss=0.0308]
steps:   1%|▏         | 149/10016 [32:01<35:20:32, 12.89s/it, avr_loss=0.0308]
steps:   1%|▏         | 149/10016 [32:01<35:20:32, 12.89s/it, avr_loss=0.0307]
steps:   1%|▏         | 150/10016 [32:14<35:20:17, 12.89s/it, avr_loss=0.0307]
steps:   1%|▏         | 150/10016 [32:14<35:20:17, 12.89s/it, avr_loss=0.0309]
steps:   2%|▏         | 151/10016 [32:27<35:20:03, 12.89s/it, avr_loss=0.0309]
steps:   2%|▏         | 151/10016 [32:27<35:20:03, 12.89s/it, avr_loss=0.0308]
steps:   2%|▏         | 152/10016 [32:39<35:19:48, 12.89s/it, avr_loss=0.0308]
steps:   2%|▏         | 152/10016 [32:39<35:19:48, 12.89s/it, avr_loss=0.0307]
steps:   2%|▏         | 153/10016 [32:52<35:19:34, 12.89s/it, avr_loss=0.0307]
steps:   2%|▏         | 153/10016 [32:52<35:19:34, 12.89s/it, avr_loss=0.0306]
steps:   2%|▏         | 154/10016 [33:05<35:19:20, 12.89s/it, avr_loss=0.0306]
steps:   2%|▏         | 154/10016 [33:05<35:19:20, 12.89s/it, avr_loss=0.0305]
steps:   2%|▏         | 155/10016 [33:18<35:19:06, 12.89s/it, avr_loss=0.0305]
steps:   2%|▏         | 155/10016 [33:18<35:19:06, 12.89s/it, avr_loss=0.0305]
steps:   2%|▏         | 156/10016 [33:31<35:18:51, 12.89s/it, avr_loss=0.0305]
steps:   2%|▏         | 156/10016 [33:31<35:18:51, 12.89s/it, avr_loss=0.0307]
steps:   2%|▏         | 157/10016 [33:44<35:18:37, 12.89s/it, avr_loss=0.0307]
steps:   2%|▏         | 157/10016 [33:44<35:18:37, 12.89s/it, avr_loss=0.0306]
steps:   2%|▏         | 158/10016 [33:57<35:18:22, 12.89s/it, avr_loss=0.0306]
steps:   2%|▏         | 158/10016 [33:57<35:18:22, 12.89s/it, avr_loss=0.0306]
steps:   2%|▏         | 159/10016 [34:10<35:18:08, 12.89s/it, avr_loss=0.0306]
steps:   2%|▏         | 159/10016 [34:10<35:18:09, 12.89s/it, avr_loss=0.0306]
steps:   2%|▏         | 160/10016 [34:22<35:17:54, 12.89s/it, avr_loss=0.0306]
steps:   2%|▏         | 160/10016 [34:22<35:17:54, 12.89s/it, avr_loss=0.0306]
steps:   2%|▏         | 161/10016 [34:35<35:17:40, 12.89s/it, avr_loss=0.0306]
steps:   2%|▏         | 161/10016 [34:35<35:17:40, 12.89s/it, avr_loss=0.0306]
steps:   2%|▏         | 162/10016 [34:48<35:17:28, 12.89s/it, avr_loss=0.0306]
steps:   2%|▏         | 162/10016 [34:48<35:17:28, 12.89s/it, avr_loss=0.0309]
steps:   2%|▏         | 163/10016 [35:01<35:17:17, 12.89s/it, avr_loss=0.0309]
steps:   2%|▏         | 163/10016 [35:01<35:17:17, 12.89s/it, avr_loss=0.0308]
steps:   2%|▏         | 164/10016 [35:14<35:17:02, 12.89s/it, avr_loss=0.0308]
steps:   2%|▏         | 164/10016 [35:14<35:17:02, 12.89s/it, avr_loss=0.0308]
steps:   2%|▏         | 165/10016 [35:27<35:16:48, 12.89s/it, avr_loss=0.0308]
steps:   2%|▏         | 165/10016 [35:27<35:16:48, 12.89s/it, avr_loss=0.0308]
steps:   2%|▏         | 166/10016 [35:40<35:16:35, 12.89s/it, avr_loss=0.0308]
steps:   2%|▏         | 166/10016 [35:40<35:16:35, 12.89s/it, avr_loss=0.0307]
steps:   2%|▏         | 167/10016 [35:53<35:16:21, 12.89s/it, avr_loss=0.0307]
steps:   2%|▏         | 167/10016 [35:53<35:16:21, 12.89s/it, avr_loss=0.0307]
steps:   2%|▏         | 168/10016 [36:05<35:16:07, 12.89s/it, avr_loss=0.0307]
steps:   2%|▏         | 168/10016 [36:05<35:16:07, 12.89s/it, avr_loss=0.0306]
steps:   2%|▏         | 169/10016 [36:18<35:15:53, 12.89s/it, avr_loss=0.0306]
steps:   2%|▏         | 169/10016 [36:18<35:15:53, 12.89s/it, avr_loss=0.0306]
steps:   2%|▏         | 170/10016 [36:31<35:15:39, 12.89s/it, avr_loss=0.0306]
steps:   2%|▏         | 170/10016 [36:31<35:15:39, 12.89s/it, avr_loss=0.0305]
steps:   2%|▏         | 171/10016 [36:44<35:15:26, 12.89s/it, avr_loss=0.0305]
steps:   2%|▏         | 171/10016 [36:44<35:15:26, 12.89s/it, avr_loss=0.0304]
steps:   2%|▏         | 172/10016 [36:57<35:15:12, 12.89s/it, avr_loss=0.0304]
steps:   2%|▏         | 172/10016 [36:57<35:15:12, 12.89s/it, avr_loss=0.0304]
steps:   2%|▏         | 173/10016 [37:10<35:14:58, 12.89s/it, avr_loss=0.0304]
steps:   2%|▏         | 173/10016 [37:10<35:14:58, 12.89s/it, avr_loss=0.0303]
steps:   2%|▏         | 174/10016 [37:23<35:14:44, 12.89s/it, avr_loss=0.0303]
steps:   2%|▏         | 174/10016 [37:23<35:14:44, 12.89s/it, avr_loss=0.0304]
steps:   2%|▏         | 175/10016 [37:36<35:14:29, 12.89s/it, avr_loss=0.0304]
steps:   2%|▏         | 175/10016 [37:36<35:14:30, 12.89s/it, avr_loss=0.0304]
steps:   2%|▏         | 176/10016 [37:48<35:14:15, 12.89s/it, avr_loss=0.0304]
steps:   2%|▏         | 176/10016 [37:48<35:14:15, 12.89s/it, avr_loss=0.0304]
steps:   2%|▏         | 177/10016 [38:01<35:14:01, 12.89s/it, avr_loss=0.0304]
steps:   2%|▏         | 177/10016 [38:01<35:14:01, 12.89s/it, avr_loss=0.0304]
steps:   2%|▏         | 178/10016 [38:14<35:13:47, 12.89s/it, avr_loss=0.0304]
steps:   2%|▏         | 178/10016 [38:14<35:13:47, 12.89s/it, avr_loss=0.0303]
steps:   2%|▏         | 179/10016 [38:27<35:13:33, 12.89s/it, avr_loss=0.0303]
steps:   2%|▏         | 179/10016 [38:27<35:13:33, 12.89s/it, avr_loss=0.0303]
steps:   2%|▏         | 180/10016 [38:40<35:13:18, 12.89s/it, avr_loss=0.0303]
steps:   2%|▏         | 180/10016 [38:40<35:13:18, 12.89s/it, avr_loss=0.0305]
steps:   2%|▏         | 181/10016 [38:53<35:13:04, 12.89s/it, avr_loss=0.0305]
steps:   2%|▏         | 181/10016 [38:53<35:13:04, 12.89s/it, avr_loss=0.0304]
steps:   2%|▏         | 182/10016 [39:06<35:12:50, 12.89s/it, avr_loss=0.0304]
steps:   2%|▏         | 182/10016 [39:06<35:12:50, 12.89s/it, avr_loss=0.0308]
steps:   2%|▏         | 183/10016 [39:19<35:12:36, 12.89s/it, avr_loss=0.0308]
steps:   2%|▏         | 183/10016 [39:19<35:12:36, 12.89s/it, avr_loss=0.0308]
steps:   2%|▏         | 184/10016 [39:31<35:12:22, 12.89s/it, avr_loss=0.0308]
steps:   2%|▏         | 184/10016 [39:31<35:12:22, 12.89s/it, avr_loss=0.0309]
steps:   2%|▏         | 185/10016 [39:44<35:12:07, 12.89s/it, avr_loss=0.0309]
steps:   2%|▏         | 185/10016 [39:44<35:12:07, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 186/10016 [39:57<35:11:53, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 186/10016 [39:57<35:11:53, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 187/10016 [40:10<35:11:39, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 187/10016 [40:10<35:11:39, 12.89s/it, avr_loss=0.0318]
steps:   2%|▏         | 188/10016 [40:23<35:11:25, 12.89s/it, avr_loss=0.0318]
steps:   2%|▏         | 188/10016 [40:23<35:11:25, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 189/10016 [40:36<35:11:12, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 189/10016 [40:36<35:11:12, 12.89s/it, avr_loss=0.0321]
steps:   2%|▏         | 190/10016 [40:49<35:10:58, 12.89s/it, avr_loss=0.0321]
steps:   2%|▏         | 190/10016 [40:49<35:10:58, 12.89s/it, avr_loss=0.0321]
steps:   2%|▏         | 191/10016 [41:02<35:10:44, 12.89s/it, avr_loss=0.0321]
steps:   2%|▏         | 191/10016 [41:02<35:10:44, 12.89s/it, avr_loss=0.032] 
steps:   2%|▏         | 192/10016 [41:14<35:10:30, 12.89s/it, avr_loss=0.032]
steps:   2%|▏         | 192/10016 [41:14<35:10:30, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 193/10016 [41:27<35:10:17, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 193/10016 [41:27<35:10:17, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 194/10016 [41:40<35:10:02, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 194/10016 [41:40<35:10:02, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 195/10016 [41:53<35:09:49, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 195/10016 [41:53<35:09:49, 12.89s/it, avr_loss=0.0318]
steps:   2%|▏         | 196/10016 [42:06<35:09:35, 12.89s/it, avr_loss=0.0318]
steps:   2%|▏         | 196/10016 [42:06<35:09:35, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 197/10016 [42:19<35:09:21, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 197/10016 [42:19<35:09:21, 12.89s/it, avr_loss=0.0318]
steps:   2%|▏         | 198/10016 [42:32<35:09:07, 12.89s/it, avr_loss=0.0318]
steps:   2%|▏         | 198/10016 [42:32<35:09:07, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 199/10016 [42:44<35:08:54, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 199/10016 [42:44<35:08:54, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 200/10016 [42:57<35:08:40, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 200/10016 [42:57<35:08:40, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 201/10016 [43:10<35:08:27, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 201/10016 [43:10<35:08:27, 12.89s/it, avr_loss=0.0316]
steps:   2%|▏         | 202/10016 [43:23<35:08:13, 12.89s/it, avr_loss=0.0316]
steps:   2%|▏         | 202/10016 [43:23<35:08:13, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 203/10016 [43:36<35:07:59, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 203/10016 [43:36<35:07:59, 12.89s/it, avr_loss=0.0321]
steps:   2%|▏         | 204/10016 [43:49<35:07:46, 12.89s/it, avr_loss=0.0321]
steps:   2%|▏         | 204/10016 [43:49<35:07:46, 12.89s/it, avr_loss=0.0321]
steps:   2%|▏         | 205/10016 [44:02<35:07:33, 12.89s/it, avr_loss=0.0321]
steps:   2%|▏         | 205/10016 [44:02<35:07:33, 12.89s/it, avr_loss=0.032] 
steps:   2%|▏         | 206/10016 [44:15<35:07:19, 12.89s/it, avr_loss=0.032]
steps:   2%|▏         | 206/10016 [44:15<35:07:19, 12.89s/it, avr_loss=0.032]
steps:   2%|▏         | 207/10016 [44:27<35:07:05, 12.89s/it, avr_loss=0.032]
steps:   2%|▏         | 207/10016 [44:27<35:07:05, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 208/10016 [44:40<35:06:52, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 208/10016 [44:40<35:06:52, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 209/10016 [44:53<35:06:38, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 209/10016 [44:53<35:06:38, 12.89s/it, avr_loss=0.0318]
steps:   2%|▏         | 210/10016 [45:06<35:06:25, 12.89s/it, avr_loss=0.0318]
steps:   2%|▏         | 210/10016 [45:06<35:06:25, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 211/10016 [45:19<35:06:11, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 211/10016 [45:19<35:06:11, 12.89s/it, avr_loss=0.0318]
steps:   2%|▏         | 212/10016 [45:32<35:05:57, 12.89s/it, avr_loss=0.0318]
steps:   2%|▏         | 212/10016 [45:32<35:05:57, 12.89s/it, avr_loss=0.0325]
steps:   2%|▏         | 213/10016 [45:45<35:05:43, 12.89s/it, avr_loss=0.0325]
steps:   2%|▏         | 213/10016 [45:45<35:05:43, 12.89s/it, avr_loss=0.0325]
steps:   2%|▏         | 214/10016 [45:58<35:05:29, 12.89s/it, avr_loss=0.0325]
steps:   2%|▏         | 214/10016 [45:58<35:05:30, 12.89s/it, avr_loss=0.0324]
steps:   2%|▏         | 215/10016 [46:10<35:05:16, 12.89s/it, avr_loss=0.0324]
steps:   2%|▏         | 215/10016 [46:10<35:05:16, 12.89s/it, avr_loss=0.0323]
steps:   2%|▏         | 216/10016 [46:23<35:05:03, 12.89s/it, avr_loss=0.0323]
steps:   2%|▏         | 216/10016 [46:23<35:05:03, 12.89s/it, avr_loss=0.0323]
steps:   2%|▏         | 217/10016 [46:36<35:04:49, 12.89s/it, avr_loss=0.0323]
steps:   2%|▏         | 217/10016 [46:36<35:04:49, 12.89s/it, avr_loss=0.0323]
steps:   2%|▏         | 218/10016 [46:49<35:04:36, 12.89s/it, avr_loss=0.0323]
steps:   2%|▏         | 218/10016 [46:49<35:04:36, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 219/10016 [47:02<35:04:22, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 219/10016 [47:02<35:04:22, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 220/10016 [47:15<35:04:09, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 220/10016 [47:15<35:04:09, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 221/10016 [47:28<35:03:56, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 221/10016 [47:28<35:03:56, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 222/10016 [47:41<35:03:42, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 222/10016 [47:41<35:03:42, 12.89s/it, avr_loss=0.0321]
steps:   2%|▏         | 223/10016 [47:53<35:03:29, 12.89s/it, avr_loss=0.0321]
steps:   2%|▏         | 223/10016 [47:53<35:03:29, 12.89s/it, avr_loss=0.032] 
steps:   2%|▏         | 224/10016 [48:06<35:03:16, 12.89s/it, avr_loss=0.032]
steps:   2%|▏         | 224/10016 [48:06<35:03:16, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 225/10016 [48:19<35:03:02, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 225/10016 [48:19<35:03:02, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 226/10016 [48:32<35:02:48, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 226/10016 [48:32<35:02:48, 12.89s/it, avr_loss=0.0323]
steps:   2%|▏         | 227/10016 [48:45<35:02:35, 12.89s/it, avr_loss=0.0323]
steps:   2%|▏         | 227/10016 [48:45<35:02:35, 12.89s/it, avr_loss=0.0323]
steps:   2%|▏         | 228/10016 [48:58<35:02:27, 12.89s/it, avr_loss=0.0323]
steps:   2%|▏         | 228/10016 [48:58<35:02:27, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 229/10016 [49:11<35:02:13, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 229/10016 [49:11<35:02:13, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 230/10016 [49:24<35:02:00, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 230/10016 [49:24<35:02:00, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 231/10016 [49:37<35:01:47, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 231/10016 [49:37<35:01:47, 12.89s/it, avr_loss=0.0321]
steps:   2%|▏         | 232/10016 [49:49<35:01:33, 12.89s/it, avr_loss=0.0321]
steps:   2%|▏         | 232/10016 [49:49<35:01:33, 12.89s/it, avr_loss=0.0321]
steps:   2%|▏         | 233/10016 [50:02<35:01:20, 12.89s/it, avr_loss=0.0321]
steps:   2%|▏         | 233/10016 [50:02<35:01:20, 12.89s/it, avr_loss=0.032] 
steps:   2%|▏         | 234/10016 [50:15<35:01:06, 12.89s/it, avr_loss=0.032]
steps:   2%|▏         | 234/10016 [50:15<35:01:06, 12.89s/it, avr_loss=0.032]
steps:   2%|▏         | 235/10016 [50:28<35:00:53, 12.89s/it, avr_loss=0.032]
steps:   2%|▏         | 235/10016 [50:28<35:00:53, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 236/10016 [50:41<35:00:39, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 236/10016 [50:41<35:00:39, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 237/10016 [50:54<35:00:26, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 237/10016 [50:54<35:00:26, 12.89s/it, avr_loss=0.0318]
steps:   2%|▏         | 238/10016 [51:07<35:00:12, 12.89s/it, avr_loss=0.0318]
steps:   2%|▏         | 238/10016 [51:07<35:00:12, 12.89s/it, avr_loss=0.0318]
steps:   2%|▏         | 239/10016 [51:20<34:59:59, 12.89s/it, avr_loss=0.0318]
steps:   2%|▏         | 239/10016 [51:20<34:59:59, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 240/10016 [51:32<34:59:46, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 240/10016 [51:32<34:59:46, 12.89s/it, avr_loss=0.0318]
steps:   2%|▏         | 241/10016 [51:45<34:59:33, 12.89s/it, avr_loss=0.0318]
steps:   2%|▏         | 241/10016 [51:45<34:59:33, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 242/10016 [51:58<34:59:20, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 242/10016 [51:58<34:59:20, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 243/10016 [52:11<34:59:07, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 243/10016 [52:11<34:59:07, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 244/10016 [52:24<34:58:53, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 244/10016 [52:24<34:58:53, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 245/10016 [52:37<34:58:39, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 245/10016 [52:37<34:58:39, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 246/10016 [52:50<34:58:26, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 246/10016 [52:50<34:58:26, 12.89s/it, avr_loss=0.0316]
steps:   2%|▏         | 247/10016 [53:03<34:58:13, 12.89s/it, avr_loss=0.0316]
steps:   2%|▏         | 247/10016 [53:03<34:58:13, 12.89s/it, avr_loss=0.0316]
steps:   2%|▏         | 248/10016 [53:15<34:57:59, 12.89s/it, avr_loss=0.0316]
steps:   2%|▏         | 248/10016 [53:15<34:57:59, 12.89s/it, avr_loss=0.0316]
steps:   2%|▏         | 249/10016 [53:28<34:57:45, 12.89s/it, avr_loss=0.0316]
steps:   2%|▏         | 249/10016 [53:28<34:57:46, 12.89s/it, avr_loss=0.0315]
steps:   2%|▏         | 250/10016 [53:41<34:57:32, 12.89s/it, avr_loss=0.0315]
steps:   2%|▏         | 250/10016 [53:41<34:57:32, 12.89s/it, avr_loss=0.0315]
steps:   3%|▎         | 251/10016 [53:54<34:57:19, 12.89s/it, avr_loss=0.0315]
steps:   3%|▎         | 251/10016 [53:54<34:57:19, 12.89s/it, avr_loss=0.0314]
steps:   3%|▎         | 252/10016 [54:07<34:57:06, 12.89s/it, avr_loss=0.0314]
steps:   3%|▎         | 252/10016 [54:07<34:57:06, 12.89s/it, avr_loss=0.0314]
steps:   3%|▎         | 253/10016 [54:20<34:56:52, 12.89s/it, avr_loss=0.0314]
steps:   3%|▎         | 253/10016 [54:20<34:56:52, 12.89s/it, avr_loss=0.0313]
steps:   3%|▎         | 254/10016 [54:33<34:56:39, 12.89s/it, avr_loss=0.0313]
steps:   3%|▎         | 254/10016 [54:33<34:56:39, 12.89s/it, avr_loss=0.0313]
steps:   3%|▎         | 255/10016 [54:46<34:56:25, 12.89s/it, avr_loss=0.0313]
steps:   3%|▎         | 255/10016 [54:46<34:56:25, 12.89s/it, avr_loss=0.0313]
steps:   3%|▎         | 256/10016 [54:58<34:56:12, 12.89s/it, avr_loss=0.0313]
steps:   3%|▎         | 256/10016 [54:58<34:56:12, 12.89s/it, avr_loss=0.0312]
steps:   3%|▎         | 257/10016 [55:11<34:55:59, 12.89s/it, avr_loss=0.0312]
steps:   3%|▎         | 257/10016 [55:11<34:55:59, 12.89s/it, avr_loss=0.0312]
steps:   3%|▎         | 258/10016 [55:24<34:55:46, 12.89s/it, avr_loss=0.0312]
steps:   3%|▎         | 258/10016 [55:24<34:55:46, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 259/10016 [55:37<34:55:32, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 259/10016 [55:37<34:55:32, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 260/10016 [55:50<34:55:19, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 260/10016 [55:50<34:55:19, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 261/10016 [56:03<34:55:06, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 261/10016 [56:03<34:55:06, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 262/10016 [56:16<34:54:53, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 262/10016 [56:16<34:54:53, 12.89s/it, avr_loss=0.031] 
steps:   3%|▎         | 263/10016 [56:29<34:54:39, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 263/10016 [56:29<34:54:39, 12.89s/it, avr_loss=0.0312]
steps:   3%|▎         | 264/10016 [56:41<34:54:26, 12.89s/it, avr_loss=0.0312]
steps:   3%|▎         | 264/10016 [56:41<34:54:26, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 265/10016 [56:54<34:54:12, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 265/10016 [56:54<34:54:12, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 266/10016 [57:07<34:53:59, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 266/10016 [57:07<34:53:59, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 267/10016 [57:20<34:53:47, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 267/10016 [57:20<34:53:47, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 268/10016 [57:33<34:53:32, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 268/10016 [57:33<34:53:32, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 269/10016 [57:46<34:53:19, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 269/10016 [57:46<34:53:19, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 270/10016 [57:59<34:53:05, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 270/10016 [57:59<34:53:05, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 271/10016 [58:12<34:52:52, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 271/10016 [58:12<34:52:52, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 272/10016 [58:24<34:52:39, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 272/10016 [58:24<34:52:39, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 273/10016 [58:37<34:52:25, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 273/10016 [58:37<34:52:25, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 274/10016 [58:50<34:52:11, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 274/10016 [58:50<34:52:11, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 275/10016 [59:03<34:51:58, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 275/10016 [59:03<34:51:58, 12.89s/it, avr_loss=0.031] 
steps:   3%|▎         | 276/10016 [59:16<34:51:44, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 276/10016 [59:16<34:51:44, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 277/10016 [59:29<34:51:31, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 277/10016 [59:29<34:51:31, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 278/10016 [59:42<34:51:18, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 278/10016 [59:42<34:51:18, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 279/10016 [59:55<34:51:04, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 279/10016 [59:55<34:51:04, 12.89s/it, avr_loss=0.0314]
steps:   3%|▎         | 280/10016 [1:00:07<34:50:50, 12.89s/it, avr_loss=0.0314]
steps:   3%|▎         | 280/10016 [1:00:07<34:50:50, 12.89s/it, avr_loss=0.0314]
steps:   3%|▎         | 281/10016 [1:00:20<34:50:37, 12.89s/it, avr_loss=0.0314]
steps:   3%|▎         | 281/10016 [1:00:20<34:50:37, 12.89s/it, avr_loss=0.0314]
steps:   3%|▎         | 282/10016 [1:00:33<34:50:24, 12.89s/it, avr_loss=0.0314]
steps:   3%|▎         | 282/10016 [1:00:33<34:50:24, 12.89s/it, avr_loss=0.0316]
steps:   3%|▎         | 283/10016 [1:00:46<34:50:11, 12.89s/it, avr_loss=0.0316]
steps:   3%|▎         | 283/10016 [1:00:46<34:50:11, 12.89s/it, avr_loss=0.0316]
steps:   3%|▎         | 284/10016 [1:00:59<34:49:58, 12.89s/it, avr_loss=0.0316]
steps:   3%|▎         | 284/10016 [1:00:59<34:49:58, 12.89s/it, avr_loss=0.0316]
steps:   3%|▎         | 285/10016 [1:01:12<34:49:45, 12.89s/it, avr_loss=0.0316]
steps:   3%|▎         | 285/10016 [1:01:12<34:49:45, 12.89s/it, avr_loss=0.0315]
steps:   3%|▎         | 286/10016 [1:01:25<34:49:31, 12.89s/it, avr_loss=0.0315]
steps:   3%|▎         | 286/10016 [1:01:25<34:49:31, 12.89s/it, avr_loss=0.0315]
steps:   3%|▎         | 287/10016 [1:01:38<34:49:18, 12.89s/it, avr_loss=0.0315]
steps:   3%|▎         | 287/10016 [1:01:38<34:49:18, 12.89s/it, avr_loss=0.0315]
steps:   3%|▎         | 288/10016 [1:01:50<34:49:05, 12.89s/it, avr_loss=0.0315]
steps:   3%|▎         | 288/10016 [1:01:50<34:49:05, 12.89s/it, avr_loss=0.0316]
steps:   3%|▎         | 289/10016 [1:02:03<34:48:51, 12.88s/it, avr_loss=0.0316]
steps:   3%|▎         | 289/10016 [1:02:03<34:48:51, 12.88s/it, avr_loss=0.0315]
steps:   3%|▎         | 290/10016 [1:02:16<34:48:38, 12.88s/it, avr_loss=0.0315]
steps:   3%|▎         | 290/10016 [1:02:16<34:48:38, 12.88s/it, avr_loss=0.0315]
steps:   3%|▎         | 291/10016 [1:02:29<34:48:25, 12.88s/it, avr_loss=0.0315]
steps:   3%|▎         | 291/10016 [1:02:29<34:48:25, 12.88s/it, avr_loss=0.0315]
steps:   3%|▎         | 292/10016 [1:02:42<34:48:11, 12.88s/it, avr_loss=0.0315]
steps:   3%|▎         | 292/10016 [1:02:42<34:48:11, 12.88s/it, avr_loss=0.0314]
steps:   3%|▎         | 293/10016 [1:02:55<34:47:58, 12.88s/it, avr_loss=0.0314]
steps:   3%|▎         | 293/10016 [1:02:55<34:47:58, 12.88s/it, avr_loss=0.0314]
steps:   3%|▎         | 294/10016 [1:03:08<34:47:44, 12.88s/it, avr_loss=0.0314]
steps:   3%|▎         | 294/10016 [1:03:08<34:47:44, 12.88s/it, avr_loss=0.0314]
steps:   3%|▎         | 295/10016 [1:03:20<34:47:31, 12.88s/it, avr_loss=0.0314]
steps:   3%|▎         | 295/10016 [1:03:20<34:47:31, 12.88s/it, avr_loss=0.0314]
steps:   3%|▎         | 296/10016 [1:03:33<34:47:18, 12.88s/it, avr_loss=0.0314]
steps:   3%|▎         | 296/10016 [1:03:33<34:47:18, 12.88s/it, avr_loss=0.0313]
steps:   3%|▎         | 297/10016 [1:03:46<34:47:05, 12.88s/it, avr_loss=0.0313]
steps:   3%|▎         | 297/10016 [1:03:46<34:47:05, 12.88s/it, avr_loss=0.0314]
steps:   3%|▎         | 298/10016 [1:03:59<34:46:51, 12.88s/it, avr_loss=0.0314]
steps:   3%|▎         | 298/10016 [1:03:59<34:46:51, 12.88s/it, avr_loss=0.0314]
steps:   3%|▎         | 299/10016 [1:04:12<34:46:38, 12.88s/it, avr_loss=0.0314]
steps:   3%|▎         | 299/10016 [1:04:12<34:46:38, 12.88s/it, avr_loss=0.0314]
steps:   3%|▎         | 300/10016 [1:04:25<34:46:25, 12.88s/it, avr_loss=0.0314]
steps:   3%|▎         | 300/10016 [1:04:25<34:46:25, 12.88s/it, avr_loss=0.0313]
steps:   3%|▎         | 301/10016 [1:04:38<34:46:12, 12.88s/it, avr_loss=0.0313]
steps:   3%|▎         | 301/10016 [1:04:38<34:46:12, 12.88s/it, avr_loss=0.0313]
steps:   3%|▎         | 302/10016 [1:04:51<34:45:59, 12.88s/it, avr_loss=0.0313]
steps:   3%|▎         | 302/10016 [1:04:51<34:45:59, 12.88s/it, avr_loss=0.0313]
steps:   3%|▎         | 303/10016 [1:05:03<34:45:46, 12.88s/it, avr_loss=0.0313]
steps:   3%|▎         | 303/10016 [1:05:03<34:45:46, 12.88s/it, avr_loss=0.0312]
steps:   3%|▎         | 304/10016 [1:05:16<34:45:33, 12.88s/it, avr_loss=0.0312]
steps:   3%|▎         | 304/10016 [1:05:16<34:45:33, 12.88s/it, avr_loss=0.0312]
steps:   3%|▎         | 305/10016 [1:05:29<34:45:19, 12.88s/it, avr_loss=0.0312]
steps:   3%|▎         | 305/10016 [1:05:29<34:45:19, 12.88s/it, avr_loss=0.0311]
steps:   3%|▎         | 306/10016 [1:05:42<34:45:06, 12.88s/it, avr_loss=0.0311]
steps:   3%|▎         | 306/10016 [1:05:42<34:45:06, 12.88s/it, avr_loss=0.0311]
steps:   3%|▎         | 307/10016 [1:05:55<34:44:53, 12.88s/it, avr_loss=0.0311]
steps:   3%|▎         | 307/10016 [1:05:55<34:44:53, 12.88s/it, avr_loss=0.0311]
steps:   3%|▎         | 308/10016 [1:06:08<34:44:40, 12.88s/it, avr_loss=0.0311]
steps:   3%|▎         | 308/10016 [1:06:08<34:44:40, 12.88s/it, avr_loss=0.031] 
steps:   3%|▎         | 309/10016 [1:06:21<34:44:27, 12.88s/it, avr_loss=0.031]
steps:   3%|▎         | 309/10016 [1:06:21<34:44:27, 12.88s/it, avr_loss=0.0311]
steps:   3%|▎         | 310/10016 [1:06:34<34:44:13, 12.88s/it, avr_loss=0.0311]
steps:   3%|▎         | 310/10016 [1:06:34<34:44:13, 12.88s/it, avr_loss=0.0311]
steps:   3%|▎         | 311/10016 [1:06:46<34:44:00, 12.88s/it, avr_loss=0.0311]
steps:   3%|▎         | 311/10016 [1:06:46<34:44:00, 12.88s/it, avr_loss=0.031] 
steps:   3%|▎         | 312/10016 [1:06:59<34:43:47, 12.88s/it, avr_loss=0.031]
steps:   3%|▎         | 312/10016 [1:06:59<34:43:47, 12.88s/it, avr_loss=0.031]
steps:   3%|▎         | 313/10016 [1:07:12<34:43:33, 12.88s/it, avr_loss=0.031]
steps:   3%|▎         | 313/10016 [1:07:12<34:43:33, 12.88s/it, avr_loss=0.031]
steps:   3%|▎         | 314/10016 [1:07:25<34:43:20, 12.88s/it, avr_loss=0.031]
steps:   3%|▎         | 314/10016 [1:07:25<34:43:20, 12.88s/it, avr_loss=0.0309]
steps:   3%|▎         | 315/10016 [1:07:38<34:43:06, 12.88s/it, avr_loss=0.0309]
steps:   3%|▎         | 315/10016 [1:07:38<34:43:06, 12.88s/it, avr_loss=0.0309]
steps:   3%|▎         | 316/10016 [1:07:51<34:42:53, 12.88s/it, avr_loss=0.0309]
steps:   3%|▎         | 316/10016 [1:07:51<34:42:53, 12.88s/it, avr_loss=0.0309]
steps:   3%|▎         | 317/10016 [1:08:04<34:42:40, 12.88s/it, avr_loss=0.0309]
steps:   3%|▎         | 317/10016 [1:08:04<34:42:40, 12.88s/it, avr_loss=0.0309]
steps:   3%|▎         | 318/10016 [1:08:17<34:42:27, 12.88s/it, avr_loss=0.0309]
steps:   3%|▎         | 318/10016 [1:08:17<34:42:27, 12.88s/it, avr_loss=0.031] 
steps:   3%|▎         | 319/10016 [1:08:29<34:42:14, 12.88s/it, avr_loss=0.031]
steps:   3%|▎         | 319/10016 [1:08:29<34:42:14, 12.88s/it, avr_loss=0.031]
steps:   3%|▎         | 320/10016 [1:08:42<34:42:01, 12.88s/it, avr_loss=0.031]
steps:   3%|▎         | 320/10016 [1:08:42<34:42:01, 12.88s/it, avr_loss=0.031]
steps:   3%|▎         | 321/10016 [1:08:55<34:41:48, 12.88s/it, avr_loss=0.031]
steps:   3%|▎         | 321/10016 [1:08:55<34:41:48, 12.88s/it, avr_loss=0.0309]
steps:   3%|▎         | 322/10016 [1:09:08<34:41:35, 12.88s/it, avr_loss=0.0309]
steps:   3%|▎         | 322/10016 [1:09:08<34:41:35, 12.88s/it, avr_loss=0.0309]
steps:   3%|▎         | 323/10016 [1:09:21<34:41:22, 12.88s/it, avr_loss=0.0309]
steps:   3%|▎         | 323/10016 [1:09:21<34:41:22, 12.88s/it, avr_loss=0.0309]
steps:   3%|▎         | 324/10016 [1:09:34<34:41:08, 12.88s/it, avr_loss=0.0309]
steps:   3%|▎         | 324/10016 [1:09:34<34:41:08, 12.88s/it, avr_loss=0.0309]
steps:   3%|▎         | 325/10016 [1:09:47<34:40:55, 12.88s/it, avr_loss=0.0309]
steps:   3%|▎         | 325/10016 [1:09:47<34:40:55, 12.88s/it, avr_loss=0.0308]
steps:   3%|▎         | 326/10016 [1:10:00<34:40:42, 12.88s/it, avr_loss=0.0308]
steps:   3%|▎         | 326/10016 [1:10:00<34:40:42, 12.88s/it, avr_loss=0.031] 
steps:   3%|▎         | 327/10016 [1:10:12<34:40:29, 12.88s/it, avr_loss=0.031]
steps:   3%|▎         | 327/10016 [1:10:12<34:40:29, 12.88s/it, avr_loss=0.031]
steps:   3%|▎         | 328/10016 [1:10:25<34:40:16, 12.88s/it, avr_loss=0.031]
steps:   3%|▎         | 328/10016 [1:10:25<34:40:16, 12.88s/it, avr_loss=0.0313]
steps:   3%|▎         | 329/10016 [1:10:38<34:40:02, 12.88s/it, avr_loss=0.0313]
steps:   3%|▎         | 329/10016 [1:10:38<34:40:02, 12.88s/it, avr_loss=0.0312]
steps:   3%|▎         | 330/10016 [1:10:51<34:39:49, 12.88s/it, avr_loss=0.0312]
steps:   3%|▎         | 330/10016 [1:10:51<34:39:49, 12.88s/it, avr_loss=0.0312]
steps:   3%|▎         | 331/10016 [1:11:04<34:39:36, 12.88s/it, avr_loss=0.0312]
steps:   3%|▎         | 331/10016 [1:11:04<34:39:36, 12.88s/it, avr_loss=0.0312]
steps:   3%|▎         | 332/10016 [1:11:17<34:39:23, 12.88s/it, avr_loss=0.0312]
steps:   3%|▎         | 332/10016 [1:11:17<34:39:23, 12.88s/it, avr_loss=0.0312]
steps:   3%|▎         | 333/10016 [1:11:30<34:39:10, 12.88s/it, avr_loss=0.0312]
steps:   3%|▎         | 333/10016 [1:11:30<34:39:10, 12.88s/it, avr_loss=0.0312]
steps:   3%|▎         | 334/10016 [1:11:43<34:38:56, 12.88s/it, avr_loss=0.0312]
steps:   3%|▎         | 334/10016 [1:11:43<34:38:56, 12.88s/it, avr_loss=0.0311]
steps:   3%|▎         | 335/10016 [1:11:55<34:38:43, 12.88s/it, avr_loss=0.0311]
steps:   3%|▎         | 335/10016 [1:11:55<34:38:43, 12.88s/it, avr_loss=0.0311]
steps:   3%|▎         | 336/10016 [1:12:08<34:38:30, 12.88s/it, avr_loss=0.0311]
steps:   3%|▎         | 336/10016 [1:12:08<34:38:30, 12.88s/it, avr_loss=0.0311]
steps:   3%|▎         | 337/10016 [1:12:21<34:38:21, 12.88s/it, avr_loss=0.0311]
steps:   3%|▎         | 337/10016 [1:12:21<34:38:21, 12.88s/it, avr_loss=0.031] 
steps:   3%|▎         | 338/10016 [1:12:34<34:38:07, 12.88s/it, avr_loss=0.031]
steps:   3%|▎         | 338/10016 [1:12:34<34:38:07, 12.88s/it, avr_loss=0.031]
steps:   3%|▎         | 339/10016 [1:12:47<34:37:54, 12.88s/it, avr_loss=0.031]
steps:   3%|▎         | 339/10016 [1:12:47<34:37:54, 12.88s/it, avr_loss=0.031]
steps:   3%|▎         | 340/10016 [1:13:00<34:37:41, 12.88s/it, avr_loss=0.031]
steps:   3%|▎         | 340/10016 [1:13:00<34:37:41, 12.88s/it, avr_loss=0.031]
steps:   3%|▎         | 341/10016 [1:13:13<34:37:28, 12.88s/it, avr_loss=0.031]
steps:   3%|▎         | 341/10016 [1:13:13<34:37:28, 12.88s/it, avr_loss=0.0309]
steps:   3%|▎         | 342/10016 [1:13:26<34:37:15, 12.88s/it, avr_loss=0.0309]
steps:   3%|▎         | 342/10016 [1:13:26<34:37:15, 12.88s/it, avr_loss=0.0309]
steps:   3%|▎         | 343/10016 [1:13:39<34:37:02, 12.88s/it, avr_loss=0.0309]
steps:   3%|▎         | 343/10016 [1:13:39<34:37:02, 12.88s/it, avr_loss=0.0309]
steps:   3%|▎         | 344/10016 [1:13:51<34:36:48, 12.88s/it, avr_loss=0.0309]
steps:   3%|▎         | 344/10016 [1:13:51<34:36:48, 12.88s/it, avr_loss=0.031] 
steps:   3%|▎         | 345/10016 [1:14:04<34:36:35, 12.88s/it, avr_loss=0.031]
steps:   3%|▎         | 345/10016 [1:14:04<34:36:35, 12.88s/it, avr_loss=0.031]
steps:   3%|▎         | 346/10016 [1:14:17<34:36:22, 12.88s/it, avr_loss=0.031]
steps:   3%|▎         | 346/10016 [1:14:17<34:36:22, 12.88s/it, avr_loss=0.0309]
steps:   3%|▎         | 347/10016 [1:14:30<34:36:09, 12.88s/it, avr_loss=0.0309]
steps:   3%|▎         | 347/10016 [1:14:30<34:36:09, 12.88s/it, avr_loss=0.0312]
steps:   3%|▎         | 348/10016 [1:14:43<34:35:56, 12.88s/it, avr_loss=0.0312]
steps:   3%|▎         | 348/10016 [1:14:43<34:35:56, 12.88s/it, avr_loss=0.0311]
steps:   3%|▎         | 349/10016 [1:14:56<34:35:42, 12.88s/it, avr_loss=0.0311]
steps:   3%|▎         | 349/10016 [1:14:56<34:35:42, 12.88s/it, avr_loss=0.0311]
steps:   3%|▎         | 350/10016 [1:15:09<34:35:29, 12.88s/it, avr_loss=0.0311]
steps:   3%|▎         | 350/10016 [1:15:09<34:35:29, 12.88s/it, avr_loss=0.0311]
steps:   4%|▎         | 351/10016 [1:15:22<34:35:16, 12.88s/it, avr_loss=0.0311]
steps:   4%|▎         | 351/10016 [1:15:22<34:35:16, 12.88s/it, avr_loss=0.0311]
steps:   4%|▎         | 352/10016 [1:15:34<34:35:03, 12.88s/it, avr_loss=0.0311]
steps:   4%|▎         | 352/10016 [1:15:34<34:35:03, 12.88s/it, avr_loss=0.031] 
steps:   4%|▎         | 353/10016 [1:15:47<34:34:50, 12.88s/it, avr_loss=0.031]
steps:   4%|▎         | 353/10016 [1:15:47<34:34:50, 12.88s/it, avr_loss=0.031]
steps:   4%|▎         | 354/10016 [1:16:00<34:34:37, 12.88s/it, avr_loss=0.031]
steps:   4%|▎         | 354/10016 [1:16:00<34:34:37, 12.88s/it, avr_loss=0.031]
steps:   4%|▎         | 355/10016 [1:16:13<34:34:24, 12.88s/it, avr_loss=0.031]
steps:   4%|▎         | 355/10016 [1:16:13<34:34:24, 12.88s/it, avr_loss=0.031]
steps:   4%|▎         | 356/10016 [1:16:26<34:34:12, 12.88s/it, avr_loss=0.031]
steps:   4%|▎         | 356/10016 [1:16:26<34:34:12, 12.88s/it, avr_loss=0.0309]
steps:   4%|▎         | 357/10016 [1:16:39<34:33:59, 12.88s/it, avr_loss=0.0309]
steps:   4%|▎         | 357/10016 [1:16:39<34:33:59, 12.88s/it, avr_loss=0.0309]
steps:   4%|▎         | 358/10016 [1:16:52<34:33:46, 12.88s/it, avr_loss=0.0309]
steps:   4%|▎         | 358/10016 [1:16:52<34:33:46, 12.88s/it, avr_loss=0.0309]
steps:   4%|▎         | 359/10016 [1:17:05<34:33:34, 12.88s/it, avr_loss=0.0309]
steps:   4%|▎         | 359/10016 [1:17:05<34:33:34, 12.88s/it, avr_loss=0.0309]
steps:   4%|▎         | 360/10016 [1:17:18<34:33:21, 12.88s/it, avr_loss=0.0309]
steps:   4%|▎         | 360/10016 [1:17:18<34:33:21, 12.88s/it, avr_loss=0.0308]
steps:   4%|▎         | 361/10016 [1:17:30<34:33:08, 12.88s/it, avr_loss=0.0308]
steps:   4%|▎         | 361/10016 [1:17:30<34:33:08, 12.88s/it, avr_loss=0.0312]
steps:   4%|▎         | 362/10016 [1:17:43<34:32:56, 12.88s/it, avr_loss=0.0312]
steps:   4%|▎         | 362/10016 [1:17:43<34:32:56, 12.88s/it, avr_loss=0.0312]
steps:   4%|▎         | 363/10016 [1:17:56<34:32:43, 12.88s/it, avr_loss=0.0312]
steps:   4%|▎         | 363/10016 [1:17:56<34:32:43, 12.88s/it, avr_loss=0.0312]
steps:   4%|▎         | 364/10016 [1:18:09<34:32:31, 12.88s/it, avr_loss=0.0312]
steps:   4%|▎         | 364/10016 [1:18:09<34:32:31, 12.88s/it, avr_loss=0.0311]
steps:   4%|▎         | 365/10016 [1:18:22<34:32:18, 12.88s/it, avr_loss=0.0311]
steps:   4%|▎         | 365/10016 [1:18:22<34:32:18, 12.88s/it, avr_loss=0.0311]
steps:   4%|▎         | 366/10016 [1:18:35<34:32:05, 12.88s/it, avr_loss=0.0311]
steps:   4%|▎         | 366/10016 [1:18:35<34:32:05, 12.88s/it, avr_loss=0.0311]
steps:   4%|▎         | 367/10016 [1:18:48<34:31:52, 12.88s/it, avr_loss=0.0311]
steps:   4%|▎         | 367/10016 [1:18:48<34:31:52, 12.88s/it, avr_loss=0.0311]
steps:   4%|▎         | 368/10016 [1:19:01<34:31:39, 12.88s/it, avr_loss=0.0311]
steps:   4%|▎         | 368/10016 [1:19:01<34:31:39, 12.88s/it, avr_loss=0.031] 
steps:   4%|▎         | 369/10016 [1:19:13<34:31:26, 12.88s/it, avr_loss=0.031]
steps:   4%|▎         | 369/10016 [1:19:13<34:31:26, 12.88s/it, avr_loss=0.031]
steps:   4%|▎         | 370/10016 [1:19:26<34:31:13, 12.88s/it, avr_loss=0.031]
steps:   4%|▎         | 370/10016 [1:19:26<34:31:13, 12.88s/it, avr_loss=0.031]
steps:   4%|▎         | 371/10016 [1:19:39<34:31:00, 12.88s/it, avr_loss=0.031]
steps:   4%|▎         | 371/10016 [1:19:39<34:31:00, 12.88s/it, avr_loss=0.031]
steps:   4%|▎         | 372/10016 [1:19:52<34:30:47, 12.88s/it, avr_loss=0.031]
steps:   4%|▎         | 372/10016 [1:19:52<34:30:47, 12.88s/it, avr_loss=0.031]
steps:   4%|▎         | 373/10016 [1:20:05<34:30:35, 12.88s/it, avr_loss=0.031]
steps:   4%|▎         | 373/10016 [1:20:05<34:30:35, 12.88s/it, avr_loss=0.031]
steps:   4%|▎         | 374/10016 [1:20:18<34:30:22, 12.88s/it, avr_loss=0.031]
steps:   4%|▎         | 374/10016 [1:20:18<34:30:22, 12.88s/it, avr_loss=0.0309]
steps:   4%|▎         | 375/10016 [1:20:31<34:30:10, 12.88s/it, avr_loss=0.0309]
steps:   4%|▎         | 375/10016 [1:20:31<34:30:10, 12.88s/it, avr_loss=0.0309]
steps:   4%|▍         | 376/10016 [1:20:44<34:29:57, 12.88s/it, avr_loss=0.0309]
steps:   4%|▍         | 376/10016 [1:20:44<34:29:57, 12.88s/it, avr_loss=0.0309]
steps:   4%|▍         | 377/10016 [1:20:57<34:29:44, 12.88s/it, avr_loss=0.0309]
steps:   4%|▍         | 377/10016 [1:20:57<34:29:45, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 378/10016 [1:21:10<34:29:32, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 378/10016 [1:21:10<34:29:32, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 379/10016 [1:21:22<34:29:19, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 379/10016 [1:21:22<34:29:19, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 380/10016 [1:21:35<34:29:06, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 380/10016 [1:21:35<34:29:06, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 381/10016 [1:21:48<34:28:54, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 381/10016 [1:21:48<34:28:54, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 382/10016 [1:22:01<34:28:41, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 382/10016 [1:22:01<34:28:41, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 383/10016 [1:22:14<34:28:28, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 383/10016 [1:22:14<34:28:28, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 384/10016 [1:22:27<34:28:16, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 384/10016 [1:22:27<34:28:16, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 385/10016 [1:22:40<34:28:03, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 385/10016 [1:22:40<34:28:03, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 386/10016 [1:22:53<34:27:51, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 386/10016 [1:22:53<34:27:51, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 387/10016 [1:23:06<34:27:38, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 387/10016 [1:23:06<34:27:38, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 388/10016 [1:23:18<34:27:25, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 388/10016 [1:23:18<34:27:25, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 389/10016 [1:23:31<34:27:13, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 389/10016 [1:23:31<34:27:13, 12.88s/it, avr_loss=0.0306]
steps:   4%|▍         | 390/10016 [1:23:44<34:27:00, 12.88s/it, avr_loss=0.0306]
steps:   4%|▍         | 390/10016 [1:23:44<34:27:00, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 391/10016 [1:23:57<34:26:48, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 391/10016 [1:23:57<34:26:48, 12.88s/it, avr_loss=0.0306]
steps:   4%|▍         | 392/10016 [1:24:10<34:26:35, 12.88s/it, avr_loss=0.0306]
steps:   4%|▍         | 392/10016 [1:24:10<34:26:35, 12.88s/it, avr_loss=0.0306]
steps:   4%|▍         | 393/10016 [1:24:23<34:26:22, 12.88s/it, avr_loss=0.0306]
steps:   4%|▍         | 393/10016 [1:24:23<34:26:22, 12.88s/it, avr_loss=0.0306]
steps:   4%|▍         | 394/10016 [1:24:36<34:26:10, 12.88s/it, avr_loss=0.0306]
steps:   4%|▍         | 394/10016 [1:24:36<34:26:10, 12.88s/it, avr_loss=0.0306]
steps:   4%|▍         | 395/10016 [1:24:49<34:25:57, 12.88s/it, avr_loss=0.0306]
steps:   4%|▍         | 395/10016 [1:24:49<34:25:57, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 396/10016 [1:25:02<34:25:44, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 396/10016 [1:25:02<34:25:44, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 397/10016 [1:25:14<34:25:32, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 397/10016 [1:25:14<34:25:32, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 398/10016 [1:25:27<34:25:19, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 398/10016 [1:25:27<34:25:19, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 399/10016 [1:25:40<34:25:06, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 399/10016 [1:25:40<34:25:06, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 400/10016 [1:25:53<34:24:53, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 400/10016 [1:25:53<34:24:53, 12.88s/it, avr_loss=0.0309]
steps:   4%|▍         | 401/10016 [1:26:06<34:24:41, 12.88s/it, avr_loss=0.0309]
steps:   4%|▍         | 401/10016 [1:26:06<34:24:41, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 402/10016 [1:26:19<34:24:28, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 402/10016 [1:26:19<34:24:28, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 403/10016 [1:26:32<34:24:15, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 403/10016 [1:26:32<34:24:15, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 404/10016 [1:26:45<34:24:02, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 404/10016 [1:26:45<34:24:02, 12.88s/it, avr_loss=0.0309]
steps:   4%|▍         | 405/10016 [1:26:58<34:23:49, 12.88s/it, avr_loss=0.0309]
steps:   4%|▍         | 405/10016 [1:26:58<34:23:49, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 406/10016 [1:27:10<34:23:37, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 406/10016 [1:27:10<34:23:37, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 407/10016 [1:27:23<34:23:24, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 407/10016 [1:27:23<34:23:24, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 408/10016 [1:27:36<34:23:11, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 408/10016 [1:27:36<34:23:11, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 409/10016 [1:27:49<34:22:58, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 409/10016 [1:27:49<34:22:58, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 410/10016 [1:28:02<34:22:46, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 410/10016 [1:28:02<34:22:46, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 411/10016 [1:28:15<34:22:33, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 411/10016 [1:28:15<34:22:33, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 412/10016 [1:28:28<34:22:21, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 412/10016 [1:28:28<34:22:21, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 413/10016 [1:28:41<34:22:08, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 413/10016 [1:28:41<34:22:08, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 414/10016 [1:28:54<34:21:55, 12.88s/it, avr_loss=0.0308]
steps:   4%|▍         | 414/10016 [1:28:54<34:21:55, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 415/10016 [1:29:06<34:21:42, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 415/10016 [1:29:06<34:21:42, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 416/10016 [1:29:19<34:21:29, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 416/10016 [1:29:19<34:21:29, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 417/10016 [1:29:32<34:21:17, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 417/10016 [1:29:32<34:21:17, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 418/10016 [1:29:45<34:21:04, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 418/10016 [1:29:45<34:21:04, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 419/10016 [1:29:58<34:20:51, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 419/10016 [1:29:58<34:20:51, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 420/10016 [1:30:11<34:20:38, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 420/10016 [1:30:11<34:20:38, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 421/10016 [1:30:24<34:20:25, 12.88s/it, avr_loss=0.0307]
steps:   4%|▍         | 421/10016 [1:30:24<34:20:25, 12.88s/it, avr_loss=0.0306]
steps:   4%|▍         | 422/10016 [1:30:37<34:20:12, 12.88s/it, avr_loss=0.0306]
steps:   4%|▍         | 422/10016 [1:30:37<34:20:12, 12.88s/it, avr_loss=0.0306]
steps:   4%|▍         | 423/10016 [1:30:50<34:20:00, 12.88s/it, avr_loss=0.0306]
steps:   4%|▍         | 423/10016 [1:30:50<34:20:00, 12.88s/it, avr_loss=0.0305]
steps:   4%|▍         | 424/10016 [1:31:02<34:19:47, 12.88s/it, avr_loss=0.0305]
steps:   4%|▍         | 424/10016 [1:31:02<34:19:47, 12.88s/it, avr_loss=0.0305]
steps:   4%|▍         | 425/10016 [1:31:15<34:19:34, 12.88s/it, avr_loss=0.0305]
steps:   4%|▍         | 425/10016 [1:31:15<34:19:34, 12.88s/it, avr_loss=0.0305]
steps:   4%|▍         | 426/10016 [1:31:28<34:19:21, 12.88s/it, avr_loss=0.0305]
steps:   4%|▍         | 426/10016 [1:31:28<34:19:21, 12.88s/it, avr_loss=0.0305]
steps:   4%|▍         | 427/10016 [1:31:41<34:19:09, 12.88s/it, avr_loss=0.0305]
steps:   4%|▍         | 427/10016 [1:31:41<34:19:09, 12.88s/it, avr_loss=0.0305]
steps:   4%|▍         | 428/10016 [1:31:54<34:18:56, 12.88s/it, avr_loss=0.0305]
steps:   4%|▍         | 428/10016 [1:31:54<34:18:56, 12.88s/it, avr_loss=0.0305]
steps:   4%|▍         | 429/10016 [1:32:07<34:18:43, 12.88s/it, avr_loss=0.0305]
steps:   4%|▍         | 429/10016 [1:32:07<34:18:43, 12.88s/it, avr_loss=0.0304]
steps:   4%|▍         | 430/10016 [1:32:20<34:18:30, 12.88s/it, avr_loss=0.0304]
steps:   4%|▍         | 430/10016 [1:32:20<34:18:30, 12.88s/it, avr_loss=0.0304]
steps:   4%|▍         | 431/10016 [1:32:33<34:18:17, 12.88s/it, avr_loss=0.0304]
steps:   4%|▍         | 431/10016 [1:32:33<34:18:17, 12.88s/it, avr_loss=0.0304]
steps:   4%|▍         | 432/10016 [1:32:46<34:18:04, 12.88s/it, avr_loss=0.0304]
steps:   4%|▍         | 432/10016 [1:32:46<34:18:04, 12.88s/it, avr_loss=0.0304]
steps:   4%|▍         | 433/10016 [1:32:59<34:17:52, 12.88s/it, avr_loss=0.0304]
steps:   4%|▍         | 433/10016 [1:32:59<34:17:52, 12.88s/it, avr_loss=0.0304]
steps:   4%|▍         | 434/10016 [1:33:11<34:17:39, 12.88s/it, avr_loss=0.0304]
steps:   4%|▍         | 434/10016 [1:33:11<34:17:39, 12.88s/it, avr_loss=0.0304]
steps:   4%|▍         | 435/10016 [1:33:24<34:17:27, 12.88s/it, avr_loss=0.0304]
steps:   4%|▍         | 435/10016 [1:33:24<34:17:27, 12.88s/it, avr_loss=0.0303]
steps:   4%|▍         | 436/10016 [1:33:37<34:17:14, 12.88s/it, avr_loss=0.0303]
steps:   4%|▍         | 436/10016 [1:33:37<34:17:14, 12.88s/it, avr_loss=0.0303]
steps:   4%|▍         | 437/10016 [1:33:50<34:17:01, 12.88s/it, avr_loss=0.0303]
steps:   4%|▍         | 437/10016 [1:33:50<34:17:01, 12.88s/it, avr_loss=0.0303]
steps:   4%|▍         | 438/10016 [1:34:03<34:16:49, 12.88s/it, avr_loss=0.0303]
steps:   4%|▍         | 438/10016 [1:34:03<34:16:49, 12.88s/it, avr_loss=0.0303]
steps:   4%|▍         | 439/10016 [1:34:16<34:16:36, 12.88s/it, avr_loss=0.0303]
steps:   4%|▍         | 439/10016 [1:34:16<34:16:36, 12.88s/it, avr_loss=0.0303]
steps:   4%|▍         | 440/10016 [1:34:29<34:16:23, 12.88s/it, avr_loss=0.0303]
steps:   4%|▍         | 440/10016 [1:34:29<34:16:23, 12.88s/it, avr_loss=0.0303]
steps:   4%|▍         | 441/10016 [1:34:42<34:16:11, 12.88s/it, avr_loss=0.0303]
steps:   4%|▍         | 441/10016 [1:34:42<34:16:11, 12.88s/it, avr_loss=0.0303]
steps:   4%|▍         | 442/10016 [1:34:55<34:15:58, 12.88s/it, avr_loss=0.0303]
steps:   4%|▍         | 442/10016 [1:34:55<34:15:58, 12.88s/it, avr_loss=0.0303]
steps:   4%|▍         | 443/10016 [1:35:07<34:15:45, 12.88s/it, avr_loss=0.0303]
steps:   4%|▍         | 443/10016 [1:35:07<34:15:45, 12.88s/it, avr_loss=0.0302]
steps:   4%|▍         | 444/10016 [1:35:20<34:15:33, 12.88s/it, avr_loss=0.0302]
steps:   4%|▍         | 444/10016 [1:35:20<34:15:33, 12.88s/it, avr_loss=0.0302]
steps:   4%|▍         | 445/10016 [1:35:33<34:15:20, 12.88s/it, avr_loss=0.0302]
steps:   4%|▍         | 445/10016 [1:35:33<34:15:20, 12.88s/it, avr_loss=0.0302]
steps:   4%|▍         | 446/10016 [1:35:46<34:15:10, 12.89s/it, avr_loss=0.0302]
steps:   4%|▍         | 446/10016 [1:35:46<34:15:10, 12.89s/it, avr_loss=0.0302]
steps:   4%|▍         | 447/10016 [1:35:59<34:14:56, 12.89s/it, avr_loss=0.0302]
steps:   4%|▍         | 447/10016 [1:35:59<34:14:56, 12.89s/it, avr_loss=0.0302]
steps:   4%|▍         | 448/10016 [1:36:12<34:14:43, 12.89s/it, avr_loss=0.0302]
steps:   4%|▍         | 448/10016 [1:36:12<34:14:43, 12.89s/it, avr_loss=0.0302]
steps:   4%|▍         | 449/10016 [1:36:25<34:14:30, 12.88s/it, avr_loss=0.0302]
steps:   4%|▍         | 449/10016 [1:36:25<34:14:30, 12.88s/it, avr_loss=0.0301]
steps:   4%|▍         | 450/10016 [1:36:38<34:14:17, 12.88s/it, avr_loss=0.0301]
steps:   4%|▍         | 450/10016 [1:36:38<34:14:17, 12.88s/it, avr_loss=0.0301]
steps:   5%|▍         | 451/10016 [1:36:51<34:14:04, 12.88s/it, avr_loss=0.0301]
steps:   5%|▍         | 451/10016 [1:36:51<34:14:04, 12.88s/it, avr_loss=0.0302]
steps:   5%|▍         | 452/10016 [1:37:03<34:13:50, 12.88s/it, avr_loss=0.0302]
steps:   5%|▍         | 452/10016 [1:37:03<34:13:51, 12.88s/it, avr_loss=0.0303]
steps:   5%|▍         | 453/10016 [1:37:16<34:13:37, 12.88s/it, avr_loss=0.0303]
steps:   5%|▍         | 453/10016 [1:37:16<34:13:37, 12.88s/it, avr_loss=0.0302]
steps:   5%|▍         | 454/10016 [1:37:29<34:13:24, 12.88s/it, avr_loss=0.0302]
steps:   5%|▍         | 454/10016 [1:37:29<34:13:24, 12.88s/it, avr_loss=0.0303]
steps:   5%|▍         | 455/10016 [1:37:42<34:13:11, 12.88s/it, avr_loss=0.0303]
steps:   5%|▍         | 455/10016 [1:37:42<34:13:11, 12.88s/it, avr_loss=0.0303]
steps:   5%|▍         | 456/10016 [1:37:55<34:12:58, 12.88s/it, avr_loss=0.0303]
steps:   5%|▍         | 456/10016 [1:37:55<34:12:58, 12.88s/it, avr_loss=0.0303]
steps:   5%|▍         | 457/10016 [1:38:08<34:12:45, 12.88s/it, avr_loss=0.0303]
steps:   5%|▍         | 457/10016 [1:38:08<34:12:45, 12.88s/it, avr_loss=0.0303]
steps:   5%|▍         | 458/10016 [1:38:21<34:12:32, 12.88s/it, avr_loss=0.0303]
steps:   5%|▍         | 458/10016 [1:38:21<34:12:32, 12.88s/it, avr_loss=0.0303]
steps:   5%|▍         | 459/10016 [1:38:34<34:12:19, 12.88s/it, avr_loss=0.0303]
steps:   5%|▍         | 459/10016 [1:38:34<34:12:19, 12.88s/it, avr_loss=0.0303]
steps:   5%|▍         | 460/10016 [1:38:46<34:12:05, 12.88s/it, avr_loss=0.0303]
steps:   5%|▍         | 460/10016 [1:38:46<34:12:05, 12.88s/it, avr_loss=0.0303]
steps:   5%|▍         | 461/10016 [1:38:59<34:11:52, 12.88s/it, avr_loss=0.0303]
steps:   5%|▍         | 461/10016 [1:38:59<34:11:52, 12.88s/it, avr_loss=0.0303]
steps:   5%|▍         | 462/10016 [1:39:12<34:11:39, 12.88s/it, avr_loss=0.0303]
steps:   5%|▍         | 462/10016 [1:39:12<34:11:39, 12.88s/it, avr_loss=0.0303]
steps:   5%|▍         | 463/10016 [1:39:25<34:11:26, 12.88s/it, avr_loss=0.0303]
steps:   5%|▍         | 463/10016 [1:39:25<34:11:26, 12.88s/it, avr_loss=0.0302]
steps:   5%|▍         | 464/10016 [1:39:38<34:11:13, 12.88s/it, avr_loss=0.0302]
steps:   5%|▍         | 464/10016 [1:39:38<34:11:13, 12.88s/it, avr_loss=0.0302]
steps:   5%|▍         | 465/10016 [1:39:51<34:11:00, 12.88s/it, avr_loss=0.0302]
steps:   5%|▍         | 465/10016 [1:39:51<34:11:00, 12.88s/it, avr_loss=0.0302]
steps:   5%|▍         | 466/10016 [1:40:04<34:10:46, 12.88s/it, avr_loss=0.0302]
steps:   5%|▍         | 466/10016 [1:40:04<34:10:46, 12.88s/it, avr_loss=0.0302]
steps:   5%|▍         | 467/10016 [1:40:17<34:10:33, 12.88s/it, avr_loss=0.0302]
steps:   5%|▍         | 467/10016 [1:40:17<34:10:33, 12.88s/it, avr_loss=0.0302]
steps:   5%|▍         | 468/10016 [1:40:29<34:10:20, 12.88s/it, avr_loss=0.0302]
steps:   5%|▍         | 468/10016 [1:40:29<34:10:20, 12.88s/it, avr_loss=0.0301]
steps:   5%|▍         | 469/10016 [1:40:42<34:10:07, 12.88s/it, avr_loss=0.0301]
steps:   5%|▍         | 469/10016 [1:40:42<34:10:07, 12.88s/it, avr_loss=0.0301]
steps:   5%|▍         | 470/10016 [1:40:55<34:09:54, 12.88s/it, avr_loss=0.0301]
steps:   5%|▍         | 470/10016 [1:40:55<34:09:54, 12.88s/it, avr_loss=0.0301]
steps:   5%|▍         | 471/10016 [1:41:08<34:09:41, 12.88s/it, avr_loss=0.0301]
steps:   5%|▍         | 471/10016 [1:41:08<34:09:41, 12.88s/it, avr_loss=0.0301]
steps:   5%|▍         | 472/10016 [1:41:21<34:09:28, 12.88s/it, avr_loss=0.0301]
steps:   5%|▍         | 472/10016 [1:41:21<34:09:28, 12.88s/it, avr_loss=0.0301]
steps:   5%|▍         | 473/10016 [1:41:34<34:09:14, 12.88s/it, avr_loss=0.0301]
steps:   5%|▍         | 473/10016 [1:41:34<34:09:14, 12.88s/it, avr_loss=0.0301]
steps:   5%|▍         | 474/10016 [1:41:47<34:09:01, 12.88s/it, avr_loss=0.0301]
steps:   5%|▍         | 474/10016 [1:41:47<34:09:01, 12.88s/it, avr_loss=0.03]  
steps:   5%|▍         | 475/10016 [1:42:00<34:08:48, 12.88s/it, avr_loss=0.03]
steps:   5%|▍         | 475/10016 [1:42:00<34:08:48, 12.88s/it, avr_loss=0.03]
steps:   5%|▍         | 476/10016 [1:42:12<34:08:35, 12.88s/it, avr_loss=0.03]
steps:   5%|▍         | 476/10016 [1:42:12<34:08:35, 12.88s/it, avr_loss=0.0301]
steps:   5%|▍         | 477/10016 [1:42:25<34:08:21, 12.88s/it, avr_loss=0.0301]
steps:   5%|▍         | 477/10016 [1:42:25<34:08:21, 12.88s/it, avr_loss=0.03]  
steps:   5%|▍         | 478/10016 [1:42:38<34:08:08, 12.88s/it, avr_loss=0.03]
steps:   5%|▍         | 478/10016 [1:42:38<34:08:08, 12.88s/it, avr_loss=0.03]
steps:   5%|▍         | 479/10016 [1:42:51<34:07:55, 12.88s/it, avr_loss=0.03]
steps:   5%|▍         | 479/10016 [1:42:51<34:07:55, 12.88s/it, avr_loss=0.03]
steps:   5%|▍         | 480/10016 [1:43:04<34:07:42, 12.88s/it, avr_loss=0.03]
steps:   5%|▍         | 480/10016 [1:43:04<34:07:42, 12.88s/it, avr_loss=0.03]
steps:   5%|▍         | 481/10016 [1:43:17<34:07:29, 12.88s/it, avr_loss=0.03]
steps:   5%|▍         | 481/10016 [1:43:17<34:07:29, 12.88s/it, avr_loss=0.03]
steps:   5%|▍         | 482/10016 [1:43:30<34:07:16, 12.88s/it, avr_loss=0.03]
steps:   5%|▍         | 482/10016 [1:43:30<34:07:16, 12.88s/it, avr_loss=0.03]
steps:   5%|▍         | 483/10016 [1:43:42<34:07:03, 12.88s/it, avr_loss=0.03]
steps:   5%|▍         | 483/10016 [1:43:42<34:07:03, 12.88s/it, avr_loss=0.03]
steps:   5%|▍         | 484/10016 [1:43:55<34:06:49, 12.88s/it, avr_loss=0.03]
steps:   5%|▍         | 484/10016 [1:43:55<34:06:49, 12.88s/it, avr_loss=0.0299]
steps:   5%|▍         | 485/10016 [1:44:08<34:06:36, 12.88s/it, avr_loss=0.0299]
steps:   5%|▍         | 485/10016 [1:44:08<34:06:36, 12.88s/it, avr_loss=0.0299]
steps:   5%|▍         | 486/10016 [1:44:21<34:06:23, 12.88s/it, avr_loss=0.0299]
steps:   5%|▍         | 486/10016 [1:44:21<34:06:23, 12.88s/it, avr_loss=0.0299]
steps:   5%|▍         | 487/10016 [1:44:34<34:06:10, 12.88s/it, avr_loss=0.0299]
steps:   5%|▍         | 487/10016 [1:44:34<34:06:10, 12.88s/it, avr_loss=0.0298]
steps:   5%|▍         | 488/10016 [1:44:47<34:05:57, 12.88s/it, avr_loss=0.0298]
steps:   5%|▍         | 488/10016 [1:44:47<34:05:57, 12.88s/it, avr_loss=0.0298]
steps:   5%|▍         | 489/10016 [1:45:00<34:05:44, 12.88s/it, avr_loss=0.0298]
steps:   5%|▍         | 489/10016 [1:45:00<34:05:44, 12.88s/it, avr_loss=0.0299]
steps:   5%|▍         | 490/10016 [1:45:13<34:05:31, 12.88s/it, avr_loss=0.0299]
steps:   5%|▍         | 490/10016 [1:45:13<34:05:31, 12.88s/it, avr_loss=0.0298]
steps:   5%|▍         | 491/10016 [1:45:25<34:05:18, 12.88s/it, avr_loss=0.0298]
steps:   5%|▍         | 491/10016 [1:45:25<34:05:18, 12.88s/it, avr_loss=0.0298]
steps:   5%|▍         | 492/10016 [1:45:38<34:05:04, 12.88s/it, avr_loss=0.0298]
steps:   5%|▍         | 492/10016 [1:45:38<34:05:04, 12.88s/it, avr_loss=0.0298]
steps:   5%|▍         | 493/10016 [1:45:51<34:04:51, 12.88s/it, avr_loss=0.0298]
steps:   5%|▍         | 493/10016 [1:45:51<34:04:51, 12.88s/it, avr_loss=0.0298]
steps:   5%|▍         | 494/10016 [1:46:04<34:04:38, 12.88s/it, avr_loss=0.0298]
steps:   5%|▍         | 494/10016 [1:46:04<34:04:38, 12.88s/it, avr_loss=0.0298]
steps:   5%|▍         | 495/10016 [1:46:17<34:04:25, 12.88s/it, avr_loss=0.0298]
steps:   5%|▍         | 495/10016 [1:46:17<34:04:25, 12.88s/it, avr_loss=0.0298]
steps:   5%|▍         | 496/10016 [1:46:30<34:04:12, 12.88s/it, avr_loss=0.0298]
steps:   5%|▍         | 496/10016 [1:46:30<34:04:12, 12.88s/it, avr_loss=0.0298]
steps:   5%|▍         | 497/10016 [1:46:43<34:03:59, 12.88s/it, avr_loss=0.0298]
steps:   5%|▍         | 497/10016 [1:46:43<34:03:59, 12.88s/it, avr_loss=0.0298]
steps:   5%|▍         | 498/10016 [1:46:56<34:03:46, 12.88s/it, avr_loss=0.0298]
steps:   5%|▍         | 498/10016 [1:46:56<34:03:46, 12.88s/it, avr_loss=0.0298]
steps:   5%|▍         | 499/10016 [1:47:08<34:03:33, 12.88s/it, avr_loss=0.0298]
steps:   5%|▍         | 499/10016 [1:47:08<34:03:33, 12.88s/it, avr_loss=0.0298]
steps:   5%|▍         | 500/10016 [1:47:21<34:03:20, 12.88s/it, avr_loss=0.0298]
steps:   5%|▍         | 500/10016 [1:47:21<34:03:20, 12.88s/it, avr_loss=0.0298]
steps:   5%|▌         | 501/10016 [1:47:34<34:03:07, 12.88s/it, avr_loss=0.0298]
steps:   5%|▌         | 501/10016 [1:47:34<34:03:07, 12.88s/it, avr_loss=0.0298]
steps:   5%|▌         | 502/10016 [1:47:47<34:02:54, 12.88s/it, avr_loss=0.0298]
steps:   5%|▌         | 502/10016 [1:47:47<34:02:54, 12.88s/it, avr_loss=0.0297]
steps:   5%|▌         | 503/10016 [1:48:00<34:02:41, 12.88s/it, avr_loss=0.0297]
steps:   5%|▌         | 503/10016 [1:48:00<34:02:41, 12.88s/it, avr_loss=0.0297]
steps:   5%|▌         | 504/10016 [1:48:13<34:02:27, 12.88s/it, avr_loss=0.0297]
steps:   5%|▌         | 504/10016 [1:48:13<34:02:27, 12.88s/it, avr_loss=0.0297]
steps:   5%|▌         | 505/10016 [1:48:26<34:02:14, 12.88s/it, avr_loss=0.0297]
steps:   5%|▌         | 505/10016 [1:48:26<34:02:14, 12.88s/it, avr_loss=0.0297]
steps:   5%|▌         | 506/10016 [1:48:39<34:02:01, 12.88s/it, avr_loss=0.0297]
steps:   5%|▌         | 506/10016 [1:48:39<34:02:01, 12.88s/it, avr_loss=0.0296]
steps:   5%|▌         | 507/10016 [1:48:51<34:01:48, 12.88s/it, avr_loss=0.0296]
steps:   5%|▌         | 507/10016 [1:48:51<34:01:48, 12.88s/it, avr_loss=0.0297]
steps:   5%|▌         | 508/10016 [1:49:04<34:01:35, 12.88s/it, avr_loss=0.0297]
steps:   5%|▌         | 508/10016 [1:49:04<34:01:35, 12.88s/it, avr_loss=0.0297]
steps:   5%|▌         | 509/10016 [1:49:17<34:01:22, 12.88s/it, avr_loss=0.0297]
steps:   5%|▌         | 509/10016 [1:49:17<34:01:22, 12.88s/it, avr_loss=0.0297]
steps:   5%|▌         | 510/10016 [1:49:30<34:01:09, 12.88s/it, avr_loss=0.0297]
steps:   5%|▌         | 510/10016 [1:49:30<34:01:09, 12.88s/it, avr_loss=0.0297]
steps:   5%|▌         | 511/10016 [1:49:43<34:00:55, 12.88s/it, avr_loss=0.0297]
steps:   5%|▌         | 511/10016 [1:49:43<34:00:55, 12.88s/it, avr_loss=0.0297]
steps:   5%|▌         | 512/10016 [1:49:56<34:00:42, 12.88s/it, avr_loss=0.0297]
steps:   5%|▌         | 512/10016 [1:49:56<34:00:42, 12.88s/it, avr_loss=0.0297]
steps:   5%|▌         | 513/10016 [1:50:09<34:00:29, 12.88s/it, avr_loss=0.0297]
steps:   5%|▌         | 513/10016 [1:50:09<34:00:29, 12.88s/it, avr_loss=0.0297]
steps:   5%|▌         | 514/10016 [1:50:21<34:00:16, 12.88s/it, avr_loss=0.0297]
steps:   5%|▌         | 514/10016 [1:50:21<34:00:16, 12.88s/it, avr_loss=0.0297]
steps:   5%|▌         | 515/10016 [1:50:34<34:00:03, 12.88s/it, avr_loss=0.0297]
steps:   5%|▌         | 515/10016 [1:50:34<34:00:03, 12.88s/it, avr_loss=0.0297]
steps:   5%|▌         | 516/10016 [1:50:47<33:59:50, 12.88s/it, avr_loss=0.0297]
steps:   5%|▌         | 516/10016 [1:50:47<33:59:50, 12.88s/it, avr_loss=0.0297]
steps:   5%|▌         | 517/10016 [1:51:00<33:59:37, 12.88s/it, avr_loss=0.0297]
steps:   5%|▌         | 517/10016 [1:51:00<33:59:37, 12.88s/it, avr_loss=0.0296]
steps:   5%|▌         | 518/10016 [1:51:13<33:59:24, 12.88s/it, avr_loss=0.0296]
steps:   5%|▌         | 518/10016 [1:51:13<33:59:24, 12.88s/it, avr_loss=0.0296]
steps:   5%|▌         | 519/10016 [1:51:26<33:59:11, 12.88s/it, avr_loss=0.0296]
steps:   5%|▌         | 519/10016 [1:51:26<33:59:11, 12.88s/it, avr_loss=0.0296]
steps:   5%|▌         | 520/10016 [1:51:39<33:58:58, 12.88s/it, avr_loss=0.0296]
steps:   5%|▌         | 520/10016 [1:51:39<33:58:58, 12.88s/it, avr_loss=0.0296]
steps:   5%|▌         | 521/10016 [1:51:52<33:58:45, 12.88s/it, avr_loss=0.0296]
steps:   5%|▌         | 521/10016 [1:51:52<33:58:45, 12.88s/it, avr_loss=0.0296]
steps:   5%|▌         | 522/10016 [1:52:04<33:58:32, 12.88s/it, avr_loss=0.0296]
steps:   5%|▌         | 522/10016 [1:52:04<33:58:32, 12.88s/it, avr_loss=0.0296]
steps:   5%|▌         | 523/10016 [1:52:17<33:58:19, 12.88s/it, avr_loss=0.0296]
steps:   5%|▌         | 523/10016 [1:52:17<33:58:19, 12.88s/it, avr_loss=0.0296]
steps:   5%|▌         | 524/10016 [1:52:30<33:58:05, 12.88s/it, avr_loss=0.0296]
steps:   5%|▌         | 524/10016 [1:52:30<33:58:05, 12.88s/it, avr_loss=0.0295]
steps:   5%|▌         | 525/10016 [1:52:43<33:57:52, 12.88s/it, avr_loss=0.0295]
steps:   5%|▌         | 525/10016 [1:52:43<33:57:52, 12.88s/it, avr_loss=0.0295]
steps:   5%|▌         | 526/10016 [1:52:56<33:57:39, 12.88s/it, avr_loss=0.0295]
steps:   5%|▌         | 526/10016 [1:52:56<33:57:39, 12.88s/it, avr_loss=0.0295]
steps:   5%|▌         | 527/10016 [1:53:09<33:57:26, 12.88s/it, avr_loss=0.0295]
steps:   5%|▌         | 527/10016 [1:53:09<33:57:26, 12.88s/it, avr_loss=0.0295]
steps:   5%|▌         | 528/10016 [1:53:22<33:57:13, 12.88s/it, avr_loss=0.0295]
steps:   5%|▌         | 528/10016 [1:53:22<33:57:13, 12.88s/it, avr_loss=0.0295]
steps:   5%|▌         | 529/10016 [1:53:35<33:57:00, 12.88s/it, avr_loss=0.0295]
steps:   5%|▌         | 529/10016 [1:53:35<33:57:00, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 530/10016 [1:53:47<33:56:47, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 530/10016 [1:53:47<33:56:47, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 531/10016 [1:54:00<33:56:33, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 531/10016 [1:54:00<33:56:33, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 532/10016 [1:54:13<33:56:20, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 532/10016 [1:54:13<33:56:20, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 533/10016 [1:54:26<33:56:07, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 533/10016 [1:54:26<33:56:07, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 534/10016 [1:54:39<33:55:54, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 534/10016 [1:54:39<33:55:54, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 535/10016 [1:54:52<33:55:41, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 535/10016 [1:54:52<33:55:41, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 536/10016 [1:55:05<33:55:28, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 536/10016 [1:55:05<33:55:28, 12.88s/it, avr_loss=0.0293]
steps:   5%|▌         | 537/10016 [1:55:18<33:55:15, 12.88s/it, avr_loss=0.0293]
steps:   5%|▌         | 537/10016 [1:55:18<33:55:15, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 538/10016 [1:55:30<33:55:02, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 538/10016 [1:55:30<33:55:02, 12.88s/it, avr_loss=0.0293]
steps:   5%|▌         | 539/10016 [1:55:43<33:54:49, 12.88s/it, avr_loss=0.0293]
steps:   5%|▌         | 539/10016 [1:55:43<33:54:49, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 540/10016 [1:55:56<33:54:36, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 540/10016 [1:55:56<33:54:36, 12.88s/it, avr_loss=0.0293]
steps:   5%|▌         | 541/10016 [1:56:09<33:54:23, 12.88s/it, avr_loss=0.0293]
steps:   5%|▌         | 541/10016 [1:56:09<33:54:23, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 542/10016 [1:56:22<33:54:10, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 542/10016 [1:56:22<33:54:10, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 543/10016 [1:56:35<33:53:57, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 543/10016 [1:56:35<33:53:57, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 544/10016 [1:56:48<33:53:44, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 544/10016 [1:56:48<33:53:44, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 545/10016 [1:57:01<33:53:31, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 545/10016 [1:57:01<33:53:31, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 546/10016 [1:57:13<33:53:18, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 546/10016 [1:57:13<33:53:18, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 547/10016 [1:57:26<33:53:05, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 547/10016 [1:57:26<33:53:05, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 548/10016 [1:57:39<33:52:51, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 548/10016 [1:57:39<33:52:51, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 549/10016 [1:57:52<33:52:38, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 549/10016 [1:57:52<33:52:38, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 550/10016 [1:58:05<33:52:25, 12.88s/it, avr_loss=0.0294]
steps:   5%|▌         | 550/10016 [1:58:05<33:52:25, 12.88s/it, avr_loss=0.0294]
steps:   6%|▌         | 551/10016 [1:58:18<33:52:12, 12.88s/it, avr_loss=0.0294]
steps:   6%|▌         | 551/10016 [1:58:18<33:52:12, 12.88s/it, avr_loss=0.0294]
steps:   6%|▌         | 552/10016 [1:58:31<33:51:59, 12.88s/it, avr_loss=0.0294]
steps:   6%|▌         | 552/10016 [1:58:31<33:51:59, 12.88s/it, avr_loss=0.0294]
steps:   6%|▌         | 553/10016 [1:58:44<33:51:46, 12.88s/it, avr_loss=0.0294]
steps:   6%|▌         | 553/10016 [1:58:44<33:51:46, 12.88s/it, avr_loss=0.0294]
steps:   6%|▌         | 554/10016 [1:58:56<33:51:33, 12.88s/it, avr_loss=0.0294]
steps:   6%|▌         | 554/10016 [1:58:56<33:51:33, 12.88s/it, avr_loss=0.0294]
steps:   6%|▌         | 555/10016 [1:59:09<33:51:23, 12.88s/it, avr_loss=0.0294]
steps:   6%|▌         | 555/10016 [1:59:09<33:51:23, 12.88s/it, avr_loss=0.0293]
steps:   6%|▌         | 556/10016 [1:59:22<33:51:10, 12.88s/it, avr_loss=0.0293]
steps:   6%|▌         | 556/10016 [1:59:22<33:51:10, 12.88s/it, avr_loss=0.0293]
steps:   6%|▌         | 557/10016 [1:59:35<33:50:57, 12.88s/it, avr_loss=0.0293]
steps:   6%|▌         | 557/10016 [1:59:35<33:50:57, 12.88s/it, avr_loss=0.0293]
steps:   6%|▌         | 558/10016 [1:59:48<33:50:44, 12.88s/it, avr_loss=0.0293]
steps:   6%|▌         | 558/10016 [1:59:48<33:50:44, 12.88s/it, avr_loss=0.0293]
steps:   6%|▌         | 559/10016 [2:00:01<33:50:31, 12.88s/it, avr_loss=0.0293]
steps:   6%|▌         | 559/10016 [2:00:01<33:50:31, 12.88s/it, avr_loss=0.0293]
steps:   6%|▌         | 560/10016 [2:00:14<33:50:18, 12.88s/it, avr_loss=0.0293]
steps:   6%|▌         | 560/10016 [2:00:14<33:50:18, 12.88s/it, avr_loss=0.0292]
steps:   6%|▌         | 561/10016 [2:00:27<33:50:05, 12.88s/it, avr_loss=0.0292]
steps:   6%|▌         | 561/10016 [2:00:27<33:50:05, 12.88s/it, avr_loss=0.0292]
steps:   6%|▌         | 562/10016 [2:00:40<33:49:52, 12.88s/it, avr_loss=0.0292]
steps:   6%|▌         | 562/10016 [2:00:40<33:49:52, 12.88s/it, avr_loss=0.0292]
steps:   6%|▌         | 563/10016 [2:00:52<33:49:38, 12.88s/it, avr_loss=0.0292]
steps:   6%|▌         | 563/10016 [2:00:52<33:49:38, 12.88s/it, avr_loss=0.0292]
steps:   6%|▌         | 564/10016 [2:01:05<33:49:25, 12.88s/it, avr_loss=0.0292]
steps:   6%|▌         | 564/10016 [2:01:05<33:49:25, 12.88s/it, avr_loss=0.0292]
steps:   6%|▌         | 565/10016 [2:01:18<33:49:12, 12.88s/it, avr_loss=0.0292]
steps:   6%|▌         | 565/10016 [2:01:18<33:49:12, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 566/10016 [2:01:31<33:48:59, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 566/10016 [2:01:31<33:48:59, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 567/10016 [2:01:44<33:48:46, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 567/10016 [2:01:44<33:48:46, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 568/10016 [2:01:57<33:48:33, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 568/10016 [2:01:57<33:48:33, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 569/10016 [2:02:10<33:48:20, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 569/10016 [2:02:10<33:48:20, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 570/10016 [2:02:22<33:48:07, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 570/10016 [2:02:22<33:48:07, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 571/10016 [2:02:35<33:47:54, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 571/10016 [2:02:35<33:47:54, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 572/10016 [2:02:48<33:47:41, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 572/10016 [2:02:48<33:47:41, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 573/10016 [2:03:01<33:47:28, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 573/10016 [2:03:01<33:47:28, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 574/10016 [2:03:14<33:47:15, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 574/10016 [2:03:14<33:47:15, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 575/10016 [2:03:27<33:47:02, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 575/10016 [2:03:27<33:47:02, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 576/10016 [2:03:40<33:46:49, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 576/10016 [2:03:40<33:46:49, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 577/10016 [2:03:53<33:46:36, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 577/10016 [2:03:53<33:46:36, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 578/10016 [2:04:06<33:46:23, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 578/10016 [2:04:06<33:46:23, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 579/10016 [2:04:18<33:46:10, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 579/10016 [2:04:18<33:46:10, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 580/10016 [2:04:31<33:45:57, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 580/10016 [2:04:31<33:45:57, 12.88s/it, avr_loss=0.029] 
steps:   6%|▌         | 581/10016 [2:04:44<33:45:44, 12.88s/it, avr_loss=0.029]
steps:   6%|▌         | 581/10016 [2:04:44<33:45:44, 12.88s/it, avr_loss=0.029]
steps:   6%|▌         | 582/10016 [2:04:57<33:45:31, 12.88s/it, avr_loss=0.029]
steps:   6%|▌         | 582/10016 [2:04:57<33:45:31, 12.88s/it, avr_loss=0.029]
steps:   6%|▌         | 583/10016 [2:05:10<33:45:18, 12.88s/it, avr_loss=0.029]
steps:   6%|▌         | 583/10016 [2:05:10<33:45:18, 12.88s/it, avr_loss=0.029]
steps:   6%|▌         | 584/10016 [2:05:23<33:45:05, 12.88s/it, avr_loss=0.029]
steps:   6%|▌         | 584/10016 [2:05:23<33:45:05, 12.88s/it, avr_loss=0.029]
steps:   6%|▌         | 585/10016 [2:05:36<33:44:52, 12.88s/it, avr_loss=0.029]
steps:   6%|▌         | 585/10016 [2:05:36<33:44:52, 12.88s/it, avr_loss=0.029]
steps:   6%|▌         | 586/10016 [2:05:48<33:44:38, 12.88s/it, avr_loss=0.029]
steps:   6%|▌         | 586/10016 [2:05:48<33:44:38, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 587/10016 [2:06:01<33:44:26, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 587/10016 [2:06:01<33:44:26, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 588/10016 [2:06:14<33:44:12, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 588/10016 [2:06:14<33:44:12, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 589/10016 [2:06:27<33:43:59, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 589/10016 [2:06:27<33:43:59, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 590/10016 [2:06:40<33:43:46, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 590/10016 [2:06:40<33:43:46, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 591/10016 [2:06:53<33:43:33, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 591/10016 [2:06:53<33:43:33, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 592/10016 [2:07:06<33:43:20, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 592/10016 [2:07:06<33:43:20, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 593/10016 [2:07:19<33:43:07, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 593/10016 [2:07:19<33:43:07, 12.88s/it, avr_loss=0.029] 
steps:   6%|▌         | 594/10016 [2:07:31<33:42:54, 12.88s/it, avr_loss=0.029]
steps:   6%|▌         | 594/10016 [2:07:31<33:42:54, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 595/10016 [2:07:44<33:42:41, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 595/10016 [2:07:44<33:42:41, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 596/10016 [2:07:57<33:42:28, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 596/10016 [2:07:57<33:42:28, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 597/10016 [2:08:10<33:42:15, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 597/10016 [2:08:10<33:42:15, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 598/10016 [2:08:23<33:42:02, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 598/10016 [2:08:23<33:42:02, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 599/10016 [2:08:36<33:41:49, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 599/10016 [2:08:36<33:41:49, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 600/10016 [2:08:49<33:41:36, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 600/10016 [2:08:49<33:41:36, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 601/10016 [2:09:02<33:41:23, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 601/10016 [2:09:02<33:41:23, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 602/10016 [2:09:14<33:41:10, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 602/10016 [2:09:14<33:41:10, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 603/10016 [2:09:27<33:40:57, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 603/10016 [2:09:27<33:40:57, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 604/10016 [2:09:40<33:40:44, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 604/10016 [2:09:40<33:40:44, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 605/10016 [2:09:53<33:40:31, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 605/10016 [2:09:53<33:40:31, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 606/10016 [2:10:06<33:40:18, 12.88s/it, avr_loss=0.0291]
steps:   6%|▌         | 606/10016 [2:10:06<33:40:18, 12.88s/it, avr_loss=0.029] 
steps:   6%|▌         | 607/10016 [2:10:19<33:40:05, 12.88s/it, avr_loss=0.029]
steps:   6%|▌         | 607/10016 [2:10:19<33:40:05, 12.88s/it, avr_loss=0.029]
steps:   6%|▌         | 608/10016 [2:10:32<33:39:52, 12.88s/it, avr_loss=0.029]
steps:   6%|▌         | 608/10016 [2:10:32<33:39:52, 12.88s/it, avr_loss=0.029]
steps:   6%|▌         | 609/10016 [2:10:45<33:39:39, 12.88s/it, avr_loss=0.029]
steps:   6%|▌         | 609/10016 [2:10:45<33:39:39, 12.88s/it, avr_loss=0.029]
steps:   6%|▌         | 610/10016 [2:10:57<33:39:26, 12.88s/it, avr_loss=0.029]
steps:   6%|▌         | 610/10016 [2:10:57<33:39:26, 12.88s/it, avr_loss=0.029]
steps:   6%|▌         | 611/10016 [2:11:10<33:39:13, 12.88s/it, avr_loss=0.029]
steps:   6%|▌         | 611/10016 [2:11:10<33:39:13, 12.88s/it, avr_loss=0.029]
steps:   6%|▌         | 612/10016 [2:11:23<33:39:00, 12.88s/it, avr_loss=0.029]
steps:   6%|▌         | 612/10016 [2:11:23<33:39:00, 12.88s/it, avr_loss=0.029]
steps:   6%|▌         | 613/10016 [2:11:36<33:38:47, 12.88s/it, avr_loss=0.029]
steps:   6%|▌         | 613/10016 [2:11:36<33:38:47, 12.88s/it, avr_loss=0.0289]
steps:   6%|▌         | 614/10016 [2:11:49<33:38:34, 12.88s/it, avr_loss=0.0289]
steps:   6%|▌         | 614/10016 [2:11:49<33:38:34, 12.88s/it, avr_loss=0.0289]
steps:   6%|▌         | 615/10016 [2:12:02<33:38:21, 12.88s/it, avr_loss=0.0289]
steps:   6%|▌         | 615/10016 [2:12:02<33:38:21, 12.88s/it, avr_loss=0.0289]
steps:   6%|▌         | 616/10016 [2:12:15<33:38:08, 12.88s/it, avr_loss=0.0289]
steps:   6%|▌         | 616/10016 [2:12:15<33:38:08, 12.88s/it, avr_loss=0.0289]
steps:   6%|▌         | 617/10016 [2:12:28<33:37:55, 12.88s/it, avr_loss=0.0289]
steps:   6%|▌         | 617/10016 [2:12:28<33:37:55, 12.88s/it, avr_loss=0.0289]
steps:   6%|▌         | 618/10016 [2:12:40<33:37:42, 12.88s/it, avr_loss=0.0289]
steps:   6%|▌         | 618/10016 [2:12:40<33:37:42, 12.88s/it, avr_loss=0.0289]
steps:   6%|▌         | 619/10016 [2:12:53<33:37:29, 12.88s/it, avr_loss=0.0289]
steps:   6%|▌         | 619/10016 [2:12:53<33:37:29, 12.88s/it, avr_loss=0.0289]
steps:   6%|▌         | 620/10016 [2:13:06<33:37:16, 12.88s/it, avr_loss=0.0289]
steps:   6%|▌         | 620/10016 [2:13:06<33:37:16, 12.88s/it, avr_loss=0.0289]
steps:   6%|▌         | 621/10016 [2:13:19<33:37:03, 12.88s/it, avr_loss=0.0289]
steps:   6%|▌         | 621/10016 [2:13:19<33:37:03, 12.88s/it, avr_loss=0.0289]
steps:   6%|▌         | 622/10016 [2:13:32<33:36:50, 12.88s/it, avr_loss=0.0289]
steps:   6%|▌         | 622/10016 [2:13:32<33:36:51, 12.88s/it, avr_loss=0.0289]
steps:   6%|▌         | 623/10016 [2:13:45<33:36:38, 12.88s/it, avr_loss=0.0289]
steps:   6%|▌         | 623/10016 [2:13:45<33:36:38, 12.88s/it, avr_loss=0.0288]
steps:   6%|▌         | 624/10016 [2:13:58<33:36:24, 12.88s/it, avr_loss=0.0288]
steps:   6%|▌         | 624/10016 [2:13:58<33:36:24, 12.88s/it, avr_loss=0.0289]
steps:   6%|▌         | 625/10016 [2:14:11<33:36:11, 12.88s/it, avr_loss=0.0289]
steps:   6%|▌         | 625/10016 [2:14:11<33:36:11, 12.88s/it, avr_loss=0.0289]
steps:   6%|▋         | 626/10016 [2:14:23<33:35:59, 12.88s/it, avr_loss=0.0289]
steps:   6%|▋         | 626/10016 [2:14:23<33:35:59, 12.88s/it, avr_loss=0.0289]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/loss-curve-retrain/train_lora-000001.safetensors

epoch 2/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2

steps:   6%|▋         | 627/10016 [2:14:38<33:36:13, 12.88s/it, avr_loss=0.0289]
steps:   6%|▋         | 627/10016 [2:14:38<33:36:13, 12.88s/it, avr_loss=0.0289]
steps:   6%|▋         | 628/10016 [2:14:51<33:36:00, 12.88s/it, avr_loss=0.0289]
steps:   6%|▋         | 628/10016 [2:14:51<33:36:00, 12.88s/it, avr_loss=0.0289]
steps:   6%|▋         | 629/10016 [2:15:04<33:35:48, 12.88s/it, avr_loss=0.0289]
steps:   6%|▋         | 629/10016 [2:15:04<33:35:48, 12.88s/it, avr_loss=0.0289]
steps:   6%|▋         | 630/10016 [2:15:17<33:35:35, 12.88s/it, avr_loss=0.0289]
steps:   6%|▋         | 630/10016 [2:15:17<33:35:35, 12.88s/it, avr_loss=0.0289]
steps:   6%|▋         | 631/10016 [2:15:30<33:35:23, 12.88s/it, avr_loss=0.0289]
steps:   6%|▋         | 631/10016 [2:15:30<33:35:23, 12.88s/it, avr_loss=0.0289]
steps:   6%|▋         | 632/10016 [2:15:43<33:35:10, 12.88s/it, avr_loss=0.0289]
steps:   6%|▋         | 632/10016 [2:15:43<33:35:10, 12.88s/it, avr_loss=0.0289]
steps:   6%|▋         | 633/10016 [2:15:56<33:34:57, 12.88s/it, avr_loss=0.0289]
steps:   6%|▋         | 633/10016 [2:15:56<33:34:57, 12.88s/it, avr_loss=0.0289]
steps:   6%|▋         | 634/10016 [2:16:08<33:34:45, 12.88s/it, avr_loss=0.0289]
steps:   6%|▋         | 634/10016 [2:16:08<33:34:45, 12.88s/it, avr_loss=0.0289]
steps:   6%|▋         | 635/10016 [2:16:21<33:34:32, 12.88s/it, avr_loss=0.0289]
steps:   6%|▋         | 635/10016 [2:16:21<33:34:32, 12.88s/it, avr_loss=0.0289]
steps:   6%|▋         | 636/10016 [2:16:34<33:34:20, 12.88s/it, avr_loss=0.0289]
steps:   6%|▋         | 636/10016 [2:16:34<33:34:20, 12.88s/it, avr_loss=0.0289]
steps:   6%|▋         | 637/10016 [2:16:47<33:34:07, 12.88s/it, avr_loss=0.0289]
steps:   6%|▋         | 637/10016 [2:16:47<33:34:07, 12.88s/it, avr_loss=0.0289]
steps:   6%|▋         | 638/10016 [2:17:00<33:33:54, 12.88s/it, avr_loss=0.0289]
steps:   6%|▋         | 638/10016 [2:17:00<33:33:54, 12.88s/it, avr_loss=0.0288]
steps:   6%|▋         | 639/10016 [2:17:13<33:33:42, 12.88s/it, avr_loss=0.0288]
steps:   6%|▋         | 639/10016 [2:17:13<33:33:42, 12.88s/it, avr_loss=0.0288]
steps:   6%|▋         | 640/10016 [2:17:26<33:33:29, 12.88s/it, avr_loss=0.0288]
steps:   6%|▋         | 640/10016 [2:17:26<33:33:29, 12.88s/it, avr_loss=0.0288]
steps:   6%|▋         | 641/10016 [2:17:39<33:33:16, 12.89s/it, avr_loss=0.0288]
steps:   6%|▋         | 641/10016 [2:17:39<33:33:16, 12.89s/it, avr_loss=0.0288]
steps:   6%|▋         | 642/10016 [2:17:52<33:33:04, 12.89s/it, avr_loss=0.0288]
steps:   6%|▋         | 642/10016 [2:17:52<33:33:04, 12.89s/it, avr_loss=0.0288]
steps:   6%|▋         | 643/10016 [2:18:05<33:32:51, 12.89s/it, avr_loss=0.0288]
steps:   6%|▋         | 643/10016 [2:18:05<33:32:51, 12.89s/it, avr_loss=0.0288]
steps:   6%|▋         | 644/10016 [2:18:18<33:32:39, 12.89s/it, avr_loss=0.0288]
steps:   6%|▋         | 644/10016 [2:18:18<33:32:39, 12.89s/it, avr_loss=0.0288]
steps:   6%|▋         | 645/10016 [2:18:30<33:32:26, 12.89s/it, avr_loss=0.0288]
steps:   6%|▋         | 645/10016 [2:18:30<33:32:26, 12.89s/it, avr_loss=0.0287]
steps:   6%|▋         | 646/10016 [2:18:43<33:32:14, 12.89s/it, avr_loss=0.0287]
steps:   6%|▋         | 646/10016 [2:18:43<33:32:14, 12.89s/it, avr_loss=0.0287]
steps:   6%|▋         | 647/10016 [2:18:56<33:32:01, 12.89s/it, avr_loss=0.0287]
steps:   6%|▋         | 647/10016 [2:18:56<33:32:01, 12.89s/it, avr_loss=0.0287]
steps:   6%|▋         | 648/10016 [2:19:09<33:31:49, 12.89s/it, avr_loss=0.0287]
steps:   6%|▋         | 648/10016 [2:19:09<33:31:49, 12.89s/it, avr_loss=0.0286]
steps:   6%|▋         | 649/10016 [2:19:22<33:31:36, 12.89s/it, avr_loss=0.0286]
steps:   6%|▋         | 649/10016 [2:19:22<33:31:36, 12.89s/it, avr_loss=0.0286]
steps:   6%|▋         | 650/10016 [2:19:35<33:31:23, 12.89s/it, avr_loss=0.0286]
steps:   6%|▋         | 650/10016 [2:19:35<33:31:23, 12.89s/it, avr_loss=0.0286]
steps:   6%|▋         | 651/10016 [2:19:48<33:31:11, 12.89s/it, avr_loss=0.0286]
steps:   6%|▋         | 651/10016 [2:19:48<33:31:11, 12.89s/it, avr_loss=0.0286]
steps:   7%|▋         | 652/10016 [2:20:01<33:30:58, 12.89s/it, avr_loss=0.0286]
steps:   7%|▋         | 652/10016 [2:20:01<33:30:58, 12.89s/it, avr_loss=0.0286]
steps:   7%|▋         | 653/10016 [2:20:14<33:30:46, 12.89s/it, avr_loss=0.0286]
steps:   7%|▋         | 653/10016 [2:20:14<33:30:46, 12.89s/it, avr_loss=0.0286]
steps:   7%|▋         | 654/10016 [2:20:27<33:30:33, 12.89s/it, avr_loss=0.0286]
steps:   7%|▋         | 654/10016 [2:20:27<33:30:33, 12.89s/it, avr_loss=0.0286]
steps:   7%|▋         | 655/10016 [2:20:40<33:30:21, 12.89s/it, avr_loss=0.0286]
steps:   7%|▋         | 655/10016 [2:20:40<33:30:21, 12.89s/it, avr_loss=0.0286]
steps:   7%|▋         | 656/10016 [2:20:52<33:30:08, 12.89s/it, avr_loss=0.0286]
steps:   7%|▋         | 656/10016 [2:20:52<33:30:08, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 657/10016 [2:21:05<33:29:55, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 657/10016 [2:21:05<33:29:55, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 658/10016 [2:21:18<33:29:43, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 658/10016 [2:21:18<33:29:43, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 659/10016 [2:21:31<33:29:30, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 659/10016 [2:21:31<33:29:30, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 660/10016 [2:21:44<33:29:17, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 660/10016 [2:21:44<33:29:17, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 661/10016 [2:21:57<33:29:04, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 661/10016 [2:21:57<33:29:04, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 662/10016 [2:22:10<33:28:52, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 662/10016 [2:22:10<33:28:52, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 663/10016 [2:22:23<33:28:39, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 663/10016 [2:22:23<33:28:39, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 664/10016 [2:22:36<33:28:28, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 664/10016 [2:22:36<33:28:28, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 665/10016 [2:22:49<33:28:15, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 665/10016 [2:22:49<33:28:15, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 666/10016 [2:23:01<33:28:02, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 666/10016 [2:23:01<33:28:02, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 667/10016 [2:23:14<33:27:49, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 667/10016 [2:23:14<33:27:49, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 668/10016 [2:23:27<33:27:36, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 668/10016 [2:23:27<33:27:36, 12.89s/it, avr_loss=0.0286]
steps:   7%|▋         | 669/10016 [2:23:40<33:27:23, 12.89s/it, avr_loss=0.0286]
steps:   7%|▋         | 669/10016 [2:23:40<33:27:23, 12.89s/it, avr_loss=0.0286]
steps:   7%|▋         | 670/10016 [2:23:53<33:27:10, 12.89s/it, avr_loss=0.0286]
steps:   7%|▋         | 670/10016 [2:23:53<33:27:10, 12.89s/it, avr_loss=0.0286]
steps:   7%|▋         | 671/10016 [2:24:06<33:26:57, 12.89s/it, avr_loss=0.0286]
steps:   7%|▋         | 671/10016 [2:24:06<33:26:57, 12.89s/it, avr_loss=0.0286]
steps:   7%|▋         | 672/10016 [2:24:19<33:26:44, 12.89s/it, avr_loss=0.0286]
steps:   7%|▋         | 672/10016 [2:24:19<33:26:44, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 673/10016 [2:24:32<33:26:31, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 673/10016 [2:24:32<33:26:31, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 674/10016 [2:24:44<33:26:18, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 674/10016 [2:24:44<33:26:18, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 675/10016 [2:24:57<33:26:05, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 675/10016 [2:24:57<33:26:05, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 676/10016 [2:25:10<33:25:51, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 676/10016 [2:25:10<33:25:51, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 677/10016 [2:25:23<33:25:38, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 677/10016 [2:25:23<33:25:38, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 678/10016 [2:25:36<33:25:25, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 678/10016 [2:25:36<33:25:25, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 679/10016 [2:25:49<33:25:12, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 679/10016 [2:25:49<33:25:12, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 680/10016 [2:26:02<33:24:59, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 680/10016 [2:26:02<33:24:59, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 681/10016 [2:26:15<33:24:46, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 681/10016 [2:26:15<33:24:46, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 682/10016 [2:26:27<33:24:33, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 682/10016 [2:26:27<33:24:33, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 683/10016 [2:26:40<33:24:20, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 683/10016 [2:26:40<33:24:20, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 684/10016 [2:26:53<33:24:07, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 684/10016 [2:26:53<33:24:07, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 685/10016 [2:27:06<33:23:54, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 685/10016 [2:27:06<33:23:54, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 686/10016 [2:27:19<33:23:41, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 686/10016 [2:27:19<33:23:41, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 687/10016 [2:27:32<33:23:28, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 687/10016 [2:27:32<33:23:28, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 688/10016 [2:27:45<33:23:15, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 688/10016 [2:27:45<33:23:15, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 689/10016 [2:27:58<33:23:02, 12.89s/it, avr_loss=0.0285]
steps:   7%|▋         | 689/10016 [2:27:58<33:23:02, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 690/10016 [2:28:10<33:22:49, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 690/10016 [2:28:10<33:22:49, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 691/10016 [2:28:23<33:22:36, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 691/10016 [2:28:23<33:22:36, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 692/10016 [2:28:36<33:22:23, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 692/10016 [2:28:36<33:22:23, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 693/10016 [2:28:49<33:22:10, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 693/10016 [2:28:49<33:22:10, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 694/10016 [2:29:02<33:21:57, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 694/10016 [2:29:02<33:21:57, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 695/10016 [2:29:15<33:21:44, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 695/10016 [2:29:15<33:21:44, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 696/10016 [2:29:28<33:21:31, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 696/10016 [2:29:28<33:21:31, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 697/10016 [2:29:41<33:21:18, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 697/10016 [2:29:41<33:21:18, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 698/10016 [2:29:53<33:21:05, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 698/10016 [2:29:53<33:21:05, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 699/10016 [2:30:06<33:20:52, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 699/10016 [2:30:06<33:20:52, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 700/10016 [2:30:19<33:20:39, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 700/10016 [2:30:19<33:20:39, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 701/10016 [2:30:32<33:20:26, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 701/10016 [2:30:32<33:20:26, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 702/10016 [2:30:45<33:20:13, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 702/10016 [2:30:45<33:20:13, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 703/10016 [2:30:58<33:20:00, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 703/10016 [2:30:58<33:20:00, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 704/10016 [2:31:11<33:19:47, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 704/10016 [2:31:11<33:19:47, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 705/10016 [2:31:24<33:19:34, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 705/10016 [2:31:24<33:19:34, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 706/10016 [2:31:37<33:19:21, 12.89s/it, avr_loss=0.0284]
steps:   7%|▋         | 706/10016 [2:31:37<33:19:21, 12.89s/it, avr_loss=0.0282]
steps:   7%|▋         | 707/10016 [2:31:49<33:19:09, 12.89s/it, avr_loss=0.0282]
steps:   7%|▋         | 707/10016 [2:31:49<33:19:09, 12.89s/it, avr_loss=0.0282]
steps:   7%|▋         | 708/10016 [2:32:02<33:18:56, 12.89s/it, avr_loss=0.0282]
steps:   7%|▋         | 708/10016 [2:32:02<33:18:56, 12.89s/it, avr_loss=0.0282]
steps:   7%|▋         | 709/10016 [2:32:15<33:18:43, 12.89s/it, avr_loss=0.0282]
steps:   7%|▋         | 709/10016 [2:32:15<33:18:43, 12.89s/it, avr_loss=0.0281]
steps:   7%|▋         | 710/10016 [2:32:28<33:18:30, 12.89s/it, avr_loss=0.0281]
steps:   7%|▋         | 710/10016 [2:32:28<33:18:30, 12.89s/it, avr_loss=0.028] 
steps:   7%|▋         | 711/10016 [2:32:41<33:18:17, 12.89s/it, avr_loss=0.028]
steps:   7%|▋         | 711/10016 [2:32:41<33:18:17, 12.89s/it, avr_loss=0.028]
steps:   7%|▋         | 712/10016 [2:32:54<33:18:04, 12.89s/it, avr_loss=0.028]
steps:   7%|▋         | 712/10016 [2:32:54<33:18:04, 12.89s/it, avr_loss=0.0279]
steps:   7%|▋         | 713/10016 [2:33:07<33:17:51, 12.89s/it, avr_loss=0.0279]
steps:   7%|▋         | 713/10016 [2:33:07<33:17:51, 12.89s/it, avr_loss=0.0279]
steps:   7%|▋         | 714/10016 [2:33:20<33:17:38, 12.89s/it, avr_loss=0.0279]
steps:   7%|▋         | 714/10016 [2:33:20<33:17:38, 12.89s/it, avr_loss=0.0279]
steps:   7%|▋         | 715/10016 [2:33:32<33:17:25, 12.89s/it, avr_loss=0.0279]
steps:   7%|▋         | 715/10016 [2:33:32<33:17:25, 12.89s/it, avr_loss=0.0279]
steps:   7%|▋         | 716/10016 [2:33:45<33:17:12, 12.89s/it, avr_loss=0.0279]
steps:   7%|▋         | 716/10016 [2:33:45<33:17:12, 12.89s/it, avr_loss=0.0279]
steps:   7%|▋         | 717/10016 [2:33:58<33:16:59, 12.89s/it, avr_loss=0.0279]
steps:   7%|▋         | 717/10016 [2:33:58<33:16:59, 12.89s/it, avr_loss=0.0279]
steps:   7%|▋         | 718/10016 [2:34:11<33:16:46, 12.89s/it, avr_loss=0.0279]
steps:   7%|▋         | 718/10016 [2:34:11<33:16:46, 12.89s/it, avr_loss=0.0279]
steps:   7%|▋         | 719/10016 [2:34:24<33:16:33, 12.89s/it, avr_loss=0.0279]
steps:   7%|▋         | 719/10016 [2:34:24<33:16:33, 12.89s/it, avr_loss=0.0279]
steps:   7%|▋         | 720/10016 [2:34:37<33:16:20, 12.89s/it, avr_loss=0.0279]
steps:   7%|▋         | 720/10016 [2:34:37<33:16:20, 12.89s/it, avr_loss=0.0278]
steps:   7%|▋         | 721/10016 [2:34:50<33:16:07, 12.89s/it, avr_loss=0.0278]
steps:   7%|▋         | 721/10016 [2:34:50<33:16:07, 12.89s/it, avr_loss=0.0277]
steps:   7%|▋         | 722/10016 [2:35:03<33:15:54, 12.89s/it, avr_loss=0.0277]
steps:   7%|▋         | 722/10016 [2:35:03<33:15:54, 12.89s/it, avr_loss=0.0277]
steps:   7%|▋         | 723/10016 [2:35:15<33:15:41, 12.89s/it, avr_loss=0.0277]
steps:   7%|▋         | 723/10016 [2:35:15<33:15:41, 12.89s/it, avr_loss=0.0277]
steps:   7%|▋         | 724/10016 [2:35:28<33:15:28, 12.89s/it, avr_loss=0.0277]
steps:   7%|▋         | 724/10016 [2:35:28<33:15:28, 12.89s/it, avr_loss=0.0278]
steps:   7%|▋         | 725/10016 [2:35:41<33:15:15, 12.89s/it, avr_loss=0.0278]
steps:   7%|▋         | 725/10016 [2:35:41<33:15:15, 12.89s/it, avr_loss=0.0278]
steps:   7%|▋         | 726/10016 [2:35:54<33:15:02, 12.89s/it, avr_loss=0.0278]
steps:   7%|▋         | 726/10016 [2:35:54<33:15:02, 12.89s/it, avr_loss=0.0278]
steps:   7%|▋         | 727/10016 [2:36:07<33:14:49, 12.89s/it, avr_loss=0.0278]
steps:   7%|▋         | 727/10016 [2:36:07<33:14:49, 12.89s/it, avr_loss=0.0278]
steps:   7%|▋         | 728/10016 [2:36:20<33:14:36, 12.89s/it, avr_loss=0.0278]
steps:   7%|▋         | 728/10016 [2:36:20<33:14:36, 12.89s/it, avr_loss=0.0278]
steps:   7%|▋         | 729/10016 [2:36:33<33:14:23, 12.89s/it, avr_loss=0.0278]
steps:   7%|▋         | 729/10016 [2:36:33<33:14:23, 12.89s/it, avr_loss=0.0277]
steps:   7%|▋         | 730/10016 [2:36:46<33:14:10, 12.89s/it, avr_loss=0.0277]
steps:   7%|▋         | 730/10016 [2:36:46<33:14:10, 12.89s/it, avr_loss=0.0277]
steps:   7%|▋         | 731/10016 [2:36:58<33:13:57, 12.89s/it, avr_loss=0.0277]
steps:   7%|▋         | 731/10016 [2:36:58<33:13:57, 12.89s/it, avr_loss=0.0277]
steps:   7%|▋         | 732/10016 [2:37:11<33:13:45, 12.89s/it, avr_loss=0.0277]
steps:   7%|▋         | 732/10016 [2:37:11<33:13:45, 12.89s/it, avr_loss=0.0277]
steps:   7%|▋         | 733/10016 [2:37:24<33:13:31, 12.89s/it, avr_loss=0.0277]
steps:   7%|▋         | 733/10016 [2:37:24<33:13:31, 12.89s/it, avr_loss=0.0277]
steps:   7%|▋         | 734/10016 [2:37:37<33:13:18, 12.89s/it, avr_loss=0.0277]
steps:   7%|▋         | 734/10016 [2:37:37<33:13:18, 12.89s/it, avr_loss=0.0276]
steps:   7%|▋         | 735/10016 [2:37:50<33:13:06, 12.89s/it, avr_loss=0.0276]
steps:   7%|▋         | 735/10016 [2:37:50<33:13:06, 12.89s/it, avr_loss=0.0276]
steps:   7%|▋         | 736/10016 [2:38:03<33:12:53, 12.89s/it, avr_loss=0.0276]
steps:   7%|▋         | 736/10016 [2:38:03<33:12:53, 12.89s/it, avr_loss=0.0276]
steps:   7%|▋         | 737/10016 [2:38:16<33:12:40, 12.89s/it, avr_loss=0.0276]
steps:   7%|▋         | 737/10016 [2:38:16<33:12:40, 12.89s/it, avr_loss=0.0276]
steps:   7%|▋         | 738/10016 [2:38:29<33:12:27, 12.89s/it, avr_loss=0.0276]
steps:   7%|▋         | 738/10016 [2:38:29<33:12:27, 12.89s/it, avr_loss=0.0276]
steps:   7%|▋         | 739/10016 [2:38:42<33:12:14, 12.89s/it, avr_loss=0.0276]
steps:   7%|▋         | 739/10016 [2:38:42<33:12:14, 12.89s/it, avr_loss=0.0276]
steps:   7%|▋         | 740/10016 [2:38:54<33:12:01, 12.88s/it, avr_loss=0.0276]
steps:   7%|▋         | 740/10016 [2:38:54<33:12:01, 12.88s/it, avr_loss=0.0276]
steps:   7%|▋         | 741/10016 [2:39:07<33:11:48, 12.88s/it, avr_loss=0.0276]
steps:   7%|▋         | 741/10016 [2:39:07<33:11:48, 12.88s/it, avr_loss=0.0275]
steps:   7%|▋         | 742/10016 [2:39:20<33:11:35, 12.88s/it, avr_loss=0.0275]
steps:   7%|▋         | 742/10016 [2:39:20<33:11:35, 12.88s/it, avr_loss=0.0275]
steps:   7%|▋         | 743/10016 [2:39:33<33:11:22, 12.88s/it, avr_loss=0.0275]
steps:   7%|▋         | 743/10016 [2:39:33<33:11:22, 12.88s/it, avr_loss=0.0275]
steps:   7%|▋         | 744/10016 [2:39:46<33:11:09, 12.88s/it, avr_loss=0.0275]
steps:   7%|▋         | 744/10016 [2:39:46<33:11:09, 12.88s/it, avr_loss=0.0275]
steps:   7%|▋         | 745/10016 [2:39:59<33:10:56, 12.88s/it, avr_loss=0.0275]
steps:   7%|▋         | 745/10016 [2:39:59<33:10:56, 12.88s/it, avr_loss=0.0275]
steps:   7%|▋         | 746/10016 [2:40:12<33:10:43, 12.88s/it, avr_loss=0.0275]
steps:   7%|▋         | 746/10016 [2:40:12<33:10:43, 12.88s/it, avr_loss=0.0275]
steps:   7%|▋         | 747/10016 [2:40:25<33:10:29, 12.88s/it, avr_loss=0.0275]
steps:   7%|▋         | 747/10016 [2:40:25<33:10:29, 12.88s/it, avr_loss=0.0275]
steps:   7%|▋         | 748/10016 [2:40:37<33:10:16, 12.88s/it, avr_loss=0.0275]
steps:   7%|▋         | 748/10016 [2:40:37<33:10:16, 12.88s/it, avr_loss=0.0276]
steps:   7%|▋         | 749/10016 [2:40:50<33:10:04, 12.88s/it, avr_loss=0.0276]
steps:   7%|▋         | 749/10016 [2:40:50<33:10:04, 12.88s/it, avr_loss=0.0276]
steps:   7%|▋         | 750/10016 [2:41:03<33:09:51, 12.88s/it, avr_loss=0.0276]
steps:   7%|▋         | 750/10016 [2:41:03<33:09:51, 12.88s/it, avr_loss=0.0276]
steps:   7%|▋         | 751/10016 [2:41:16<33:09:38, 12.88s/it, avr_loss=0.0276]
steps:   7%|▋         | 751/10016 [2:41:16<33:09:38, 12.88s/it, avr_loss=0.0276]
steps:   8%|▊         | 752/10016 [2:41:29<33:09:25, 12.88s/it, avr_loss=0.0276]
steps:   8%|▊         | 752/10016 [2:41:29<33:09:25, 12.88s/it, avr_loss=0.0276]
steps:   8%|▊         | 753/10016 [2:41:42<33:09:12, 12.88s/it, avr_loss=0.0276]
steps:   8%|▊         | 753/10016 [2:41:42<33:09:12, 12.88s/it, avr_loss=0.0276]
steps:   8%|▊         | 754/10016 [2:41:55<33:08:59, 12.88s/it, avr_loss=0.0276]
steps:   8%|▊         | 754/10016 [2:41:55<33:08:59, 12.88s/it, avr_loss=0.0276]
steps:   8%|▊         | 755/10016 [2:42:08<33:08:46, 12.88s/it, avr_loss=0.0276]
steps:   8%|▊         | 755/10016 [2:42:08<33:08:46, 12.88s/it, avr_loss=0.0275]
steps:   8%|▊         | 756/10016 [2:42:20<33:08:33, 12.88s/it, avr_loss=0.0275]
steps:   8%|▊         | 756/10016 [2:42:20<33:08:33, 12.88s/it, avr_loss=0.0275]
steps:   8%|▊         | 757/10016 [2:42:33<33:08:20, 12.88s/it, avr_loss=0.0275]
steps:   8%|▊         | 757/10016 [2:42:33<33:08:20, 12.88s/it, avr_loss=0.0275]
steps:   8%|▊         | 758/10016 [2:42:46<33:08:07, 12.88s/it, avr_loss=0.0275]
steps:   8%|▊         | 758/10016 [2:42:46<33:08:07, 12.88s/it, avr_loss=0.0275]
steps:   8%|▊         | 759/10016 [2:42:59<33:07:54, 12.88s/it, avr_loss=0.0275]
steps:   8%|▊         | 759/10016 [2:42:59<33:07:54, 12.88s/it, avr_loss=0.0275]
steps:   8%|▊         | 760/10016 [2:43:12<33:07:41, 12.88s/it, avr_loss=0.0275]
steps:   8%|▊         | 760/10016 [2:43:12<33:07:41, 12.88s/it, avr_loss=0.0275]
steps:   8%|▊         | 761/10016 [2:43:25<33:07:28, 12.88s/it, avr_loss=0.0275]
steps:   8%|▊         | 761/10016 [2:43:25<33:07:28, 12.88s/it, avr_loss=0.0275]
steps:   8%|▊         | 762/10016 [2:43:38<33:07:15, 12.88s/it, avr_loss=0.0275]
steps:   8%|▊         | 762/10016 [2:43:38<33:07:15, 12.88s/it, avr_loss=0.0275]
steps:   8%|▊         | 763/10016 [2:43:51<33:07:02, 12.88s/it, avr_loss=0.0275]
steps:   8%|▊         | 763/10016 [2:43:51<33:07:02, 12.88s/it, avr_loss=0.0275]
steps:   8%|▊         | 764/10016 [2:44:03<33:06:49, 12.88s/it, avr_loss=0.0275]
steps:   8%|▊         | 764/10016 [2:44:03<33:06:49, 12.88s/it, avr_loss=0.0275]
steps:   8%|▊         | 765/10016 [2:44:16<33:06:36, 12.88s/it, avr_loss=0.0275]
steps:   8%|▊         | 765/10016 [2:44:16<33:06:36, 12.88s/it, avr_loss=0.0275]
steps:   8%|▊         | 766/10016 [2:44:29<33:06:23, 12.88s/it, avr_loss=0.0275]
steps:   8%|▊         | 766/10016 [2:44:29<33:06:23, 12.88s/it, avr_loss=0.0275]
steps:   8%|▊         | 767/10016 [2:44:42<33:06:10, 12.88s/it, avr_loss=0.0275]
steps:   8%|▊         | 767/10016 [2:44:42<33:06:10, 12.88s/it, avr_loss=0.0275]
steps:   8%|▊         | 768/10016 [2:44:55<33:05:57, 12.88s/it, avr_loss=0.0275]
steps:   8%|▊         | 768/10016 [2:44:55<33:05:57, 12.88s/it, avr_loss=0.0275]
steps:   8%|▊         | 769/10016 [2:45:08<33:05:44, 12.88s/it, avr_loss=0.0275]
steps:   8%|▊         | 769/10016 [2:45:08<33:05:44, 12.88s/it, avr_loss=0.0275]
steps:   8%|▊         | 770/10016 [2:45:21<33:05:31, 12.88s/it, avr_loss=0.0275]
steps:   8%|▊         | 770/10016 [2:45:21<33:05:31, 12.88s/it, avr_loss=0.0275]
steps:   8%|▊         | 771/10016 [2:45:34<33:05:18, 12.88s/it, avr_loss=0.0275]
steps:   8%|▊         | 771/10016 [2:45:34<33:05:18, 12.88s/it, avr_loss=0.0275]
steps:   8%|▊         | 772/10016 [2:45:46<33:05:05, 12.88s/it, avr_loss=0.0275]
steps:   8%|▊         | 772/10016 [2:45:46<33:05:05, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 773/10016 [2:45:59<33:04:53, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 773/10016 [2:45:59<33:04:53, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 774/10016 [2:46:12<33:04:40, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 774/10016 [2:46:12<33:04:40, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 775/10016 [2:46:25<33:04:27, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 775/10016 [2:46:25<33:04:27, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 776/10016 [2:46:38<33:04:14, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 776/10016 [2:46:38<33:04:14, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 777/10016 [2:46:51<33:04:01, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 777/10016 [2:46:51<33:04:01, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 778/10016 [2:47:04<33:03:48, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 778/10016 [2:47:04<33:03:48, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 779/10016 [2:47:17<33:03:35, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 779/10016 [2:47:17<33:03:35, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 780/10016 [2:47:30<33:03:22, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 780/10016 [2:47:30<33:03:22, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 781/10016 [2:47:42<33:03:09, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 781/10016 [2:47:42<33:03:09, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 782/10016 [2:47:55<33:02:56, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 782/10016 [2:47:55<33:02:56, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 783/10016 [2:48:08<33:02:43, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 783/10016 [2:48:08<33:02:43, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 784/10016 [2:48:21<33:02:30, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 784/10016 [2:48:21<33:02:30, 12.88s/it, avr_loss=0.0273]
steps:   8%|▊         | 785/10016 [2:48:34<33:02:17, 12.88s/it, avr_loss=0.0273]
steps:   8%|▊         | 785/10016 [2:48:34<33:02:17, 12.88s/it, avr_loss=0.0273]
steps:   8%|▊         | 786/10016 [2:48:47<33:02:04, 12.88s/it, avr_loss=0.0273]
steps:   8%|▊         | 786/10016 [2:48:47<33:02:04, 12.88s/it, avr_loss=0.0273]
steps:   8%|▊         | 787/10016 [2:49:00<33:01:51, 12.88s/it, avr_loss=0.0273]
steps:   8%|▊         | 787/10016 [2:49:00<33:01:51, 12.88s/it, avr_loss=0.0273]
steps:   8%|▊         | 788/10016 [2:49:13<33:01:38, 12.88s/it, avr_loss=0.0273]
steps:   8%|▊         | 788/10016 [2:49:13<33:01:38, 12.88s/it, avr_loss=0.0273]
steps:   8%|▊         | 789/10016 [2:49:25<33:01:25, 12.88s/it, avr_loss=0.0273]
steps:   8%|▊         | 789/10016 [2:49:25<33:01:25, 12.88s/it, avr_loss=0.0273]
steps:   8%|▊         | 790/10016 [2:49:38<33:01:12, 12.88s/it, avr_loss=0.0273]
steps:   8%|▊         | 790/10016 [2:49:38<33:01:12, 12.88s/it, avr_loss=0.0273]
steps:   8%|▊         | 791/10016 [2:49:51<33:00:59, 12.88s/it, avr_loss=0.0273]
steps:   8%|▊         | 791/10016 [2:49:51<33:00:59, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 792/10016 [2:50:04<33:00:46, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 792/10016 [2:50:04<33:00:46, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 793/10016 [2:50:17<33:00:33, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 793/10016 [2:50:17<33:00:33, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 794/10016 [2:50:30<33:00:20, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 794/10016 [2:50:30<33:00:20, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 795/10016 [2:50:43<33:00:07, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 795/10016 [2:50:43<33:00:07, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 796/10016 [2:50:55<32:59:54, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 796/10016 [2:50:55<32:59:54, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 797/10016 [2:51:08<32:59:41, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 797/10016 [2:51:08<32:59:41, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 798/10016 [2:51:21<32:59:28, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 798/10016 [2:51:21<32:59:28, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 799/10016 [2:51:34<32:59:15, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 799/10016 [2:51:34<32:59:15, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 800/10016 [2:51:47<32:59:02, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 800/10016 [2:51:47<32:59:02, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 801/10016 [2:52:00<32:58:49, 12.88s/it, avr_loss=0.0274]
steps:   8%|▊         | 801/10016 [2:52:00<32:58:49, 12.88s/it, avr_loss=0.0273]
steps:   8%|▊         | 802/10016 [2:52:13<32:58:36, 12.88s/it, avr_loss=0.0273]
steps:   8%|▊         | 802/10016 [2:52:13<32:58:36, 12.88s/it, avr_loss=0.0273]
steps:   8%|▊         | 803/10016 [2:52:26<32:58:23, 12.88s/it, avr_loss=0.0273]
steps:   8%|▊         | 803/10016 [2:52:26<32:58:23, 12.88s/it, avr_loss=0.0273]
steps:   8%|▊         | 804/10016 [2:52:38<32:58:10, 12.88s/it, avr_loss=0.0273]
steps:   8%|▊         | 804/10016 [2:52:38<32:58:10, 12.88s/it, avr_loss=0.0273]
steps:   8%|▊         | 805/10016 [2:52:51<32:57:57, 12.88s/it, avr_loss=0.0273]
steps:   8%|▊         | 805/10016 [2:52:51<32:57:57, 12.88s/it, avr_loss=0.0273]
steps:   8%|▊         | 806/10016 [2:53:04<32:57:44, 12.88s/it, avr_loss=0.0273]
steps:   8%|▊         | 806/10016 [2:53:04<32:57:44, 12.88s/it, avr_loss=0.0272]
steps:   8%|▊         | 807/10016 [2:53:17<32:57:31, 12.88s/it, avr_loss=0.0272]
steps:   8%|▊         | 807/10016 [2:53:17<32:57:31, 12.88s/it, avr_loss=0.0272]
steps:   8%|▊         | 808/10016 [2:53:30<32:57:18, 12.88s/it, avr_loss=0.0272]
steps:   8%|▊         | 808/10016 [2:53:30<32:57:18, 12.88s/it, avr_loss=0.0271]
steps:   8%|▊         | 809/10016 [2:53:43<32:57:05, 12.88s/it, avr_loss=0.0271]
steps:   8%|▊         | 809/10016 [2:53:43<32:57:05, 12.88s/it, avr_loss=0.0271]
steps:   8%|▊         | 810/10016 [2:53:56<32:56:52, 12.88s/it, avr_loss=0.0271]
steps:   8%|▊         | 810/10016 [2:53:56<32:56:52, 12.88s/it, avr_loss=0.027] 
steps:   8%|▊         | 811/10016 [2:54:09<32:56:39, 12.88s/it, avr_loss=0.027]
steps:   8%|▊         | 811/10016 [2:54:09<32:56:39, 12.88s/it, avr_loss=0.0267]
steps:   8%|▊         | 812/10016 [2:54:21<32:56:26, 12.88s/it, avr_loss=0.0267]
steps:   8%|▊         | 812/10016 [2:54:21<32:56:26, 12.88s/it, avr_loss=0.0267]
steps:   8%|▊         | 813/10016 [2:54:34<32:56:13, 12.88s/it, avr_loss=0.0267]
steps:   8%|▊         | 813/10016 [2:54:34<32:56:13, 12.88s/it, avr_loss=0.0267]
steps:   8%|▊         | 814/10016 [2:54:47<32:56:00, 12.88s/it, avr_loss=0.0267]
steps:   8%|▊         | 814/10016 [2:54:47<32:56:00, 12.88s/it, avr_loss=0.0266]
steps:   8%|▊         | 815/10016 [2:55:00<32:55:47, 12.88s/it, avr_loss=0.0266]
steps:   8%|▊         | 815/10016 [2:55:00<32:55:47, 12.88s/it, avr_loss=0.0266]
steps:   8%|▊         | 816/10016 [2:55:13<32:55:34, 12.88s/it, avr_loss=0.0266]
steps:   8%|▊         | 816/10016 [2:55:13<32:55:34, 12.88s/it, avr_loss=0.0266]
steps:   8%|▊         | 817/10016 [2:55:26<32:55:21, 12.88s/it, avr_loss=0.0266]
steps:   8%|▊         | 817/10016 [2:55:26<32:55:21, 12.88s/it, avr_loss=0.0266]
steps:   8%|▊         | 818/10016 [2:55:39<32:55:08, 12.88s/it, avr_loss=0.0266]
steps:   8%|▊         | 818/10016 [2:55:39<32:55:08, 12.88s/it, avr_loss=0.0266]
steps:   8%|▊         | 819/10016 [2:55:52<32:54:55, 12.88s/it, avr_loss=0.0266]
steps:   8%|▊         | 819/10016 [2:55:52<32:54:55, 12.88s/it, avr_loss=0.0266]
steps:   8%|▊         | 820/10016 [2:56:04<32:54:42, 12.88s/it, avr_loss=0.0266]
steps:   8%|▊         | 820/10016 [2:56:04<32:54:42, 12.88s/it, avr_loss=0.0266]
steps:   8%|▊         | 821/10016 [2:56:17<32:54:29, 12.88s/it, avr_loss=0.0266]
steps:   8%|▊         | 821/10016 [2:56:17<32:54:29, 12.88s/it, avr_loss=0.0266]
steps:   8%|▊         | 822/10016 [2:56:30<32:54:16, 12.88s/it, avr_loss=0.0266]
steps:   8%|▊         | 822/10016 [2:56:30<32:54:16, 12.88s/it, avr_loss=0.0267]
steps:   8%|▊         | 823/10016 [2:56:43<32:54:03, 12.88s/it, avr_loss=0.0267]
steps:   8%|▊         | 823/10016 [2:56:43<32:54:03, 12.88s/it, avr_loss=0.0268]
steps:   8%|▊         | 824/10016 [2:56:56<32:53:50, 12.88s/it, avr_loss=0.0268]
steps:   8%|▊         | 824/10016 [2:56:56<32:53:50, 12.88s/it, avr_loss=0.0268]
steps:   8%|▊         | 825/10016 [2:57:09<32:53:37, 12.88s/it, avr_loss=0.0268]
steps:   8%|▊         | 825/10016 [2:57:09<32:53:37, 12.88s/it, avr_loss=0.0268]
steps:   8%|▊         | 826/10016 [2:57:22<32:53:24, 12.88s/it, avr_loss=0.0268]
steps:   8%|▊         | 826/10016 [2:57:22<32:53:24, 12.88s/it, avr_loss=0.0267]
steps:   8%|▊         | 827/10016 [2:57:35<32:53:11, 12.88s/it, avr_loss=0.0267]
steps:   8%|▊         | 827/10016 [2:57:35<32:53:11, 12.88s/it, avr_loss=0.0267]
steps:   8%|▊         | 828/10016 [2:57:47<32:52:58, 12.88s/it, avr_loss=0.0267]
steps:   8%|▊         | 828/10016 [2:57:47<32:52:58, 12.88s/it, avr_loss=0.0267]
steps:   8%|▊         | 829/10016 [2:58:00<32:52:45, 12.88s/it, avr_loss=0.0267]
steps:   8%|▊         | 829/10016 [2:58:00<32:52:45, 12.88s/it, avr_loss=0.0266]
steps:   8%|▊         | 830/10016 [2:58:13<32:52:32, 12.88s/it, avr_loss=0.0266]
steps:   8%|▊         | 830/10016 [2:58:13<32:52:32, 12.88s/it, avr_loss=0.0266]
steps:   8%|▊         | 831/10016 [2:58:26<32:52:19, 12.88s/it, avr_loss=0.0266]
steps:   8%|▊         | 831/10016 [2:58:26<32:52:19, 12.88s/it, avr_loss=0.0265]
steps:   8%|▊         | 832/10016 [2:58:39<32:52:06, 12.88s/it, avr_loss=0.0265]
steps:   8%|▊         | 832/10016 [2:58:39<32:52:06, 12.88s/it, avr_loss=0.0265]
steps:   8%|▊         | 833/10016 [2:58:52<32:51:53, 12.88s/it, avr_loss=0.0265]
steps:   8%|▊         | 833/10016 [2:58:52<32:51:53, 12.88s/it, avr_loss=0.0265]
steps:   8%|▊         | 834/10016 [2:59:05<32:51:40, 12.88s/it, avr_loss=0.0265]
steps:   8%|▊         | 834/10016 [2:59:05<32:51:40, 12.88s/it, avr_loss=0.0265]
steps:   8%|▊         | 835/10016 [2:59:18<32:51:27, 12.88s/it, avr_loss=0.0265]
steps:   8%|▊         | 835/10016 [2:59:18<32:51:27, 12.88s/it, avr_loss=0.0265]
steps:   8%|▊         | 836/10016 [2:59:30<32:51:14, 12.88s/it, avr_loss=0.0265]
steps:   8%|▊         | 836/10016 [2:59:30<32:51:14, 12.88s/it, avr_loss=0.0265]
steps:   8%|▊         | 837/10016 [2:59:43<32:51:01, 12.88s/it, avr_loss=0.0265]
steps:   8%|▊         | 837/10016 [2:59:43<32:51:01, 12.88s/it, avr_loss=0.0265]
steps:   8%|▊         | 838/10016 [2:59:56<32:50:48, 12.88s/it, avr_loss=0.0265]
steps:   8%|▊         | 838/10016 [2:59:56<32:50:48, 12.88s/it, avr_loss=0.0263]
steps:   8%|▊         | 839/10016 [3:00:09<32:50:35, 12.88s/it, avr_loss=0.0263]
steps:   8%|▊         | 839/10016 [3:00:09<32:50:35, 12.88s/it, avr_loss=0.0263]
steps:   8%|▊         | 840/10016 [3:00:22<32:50:22, 12.88s/it, avr_loss=0.0263]
steps:   8%|▊         | 840/10016 [3:00:22<32:50:22, 12.88s/it, avr_loss=0.0263]
steps:   8%|▊         | 841/10016 [3:00:35<32:50:09, 12.88s/it, avr_loss=0.0263]
steps:   8%|▊         | 841/10016 [3:00:35<32:50:09, 12.88s/it, avr_loss=0.0263]
steps:   8%|▊         | 842/10016 [3:00:48<32:49:56, 12.88s/it, avr_loss=0.0263]
steps:   8%|▊         | 842/10016 [3:00:48<32:49:56, 12.88s/it, avr_loss=0.0263]
steps:   8%|▊         | 843/10016 [3:01:01<32:49:43, 12.88s/it, avr_loss=0.0263]
steps:   8%|▊         | 843/10016 [3:01:01<32:49:43, 12.88s/it, avr_loss=0.0263]
steps:   8%|▊         | 844/10016 [3:01:13<32:49:30, 12.88s/it, avr_loss=0.0263]
steps:   8%|▊         | 844/10016 [3:01:13<32:49:30, 12.88s/it, avr_loss=0.0263]
steps:   8%|▊         | 845/10016 [3:01:26<32:49:17, 12.88s/it, avr_loss=0.0263]
steps:   8%|▊         | 845/10016 [3:01:26<32:49:17, 12.88s/it, avr_loss=0.0263]
steps:   8%|▊         | 846/10016 [3:01:39<32:49:04, 12.88s/it, avr_loss=0.0263]
steps:   8%|▊         | 846/10016 [3:01:39<32:49:04, 12.88s/it, avr_loss=0.0263]
steps:   8%|▊         | 847/10016 [3:01:52<32:48:51, 12.88s/it, avr_loss=0.0263]
steps:   8%|▊         | 847/10016 [3:01:52<32:48:51, 12.88s/it, avr_loss=0.0263]
steps:   8%|▊         | 848/10016 [3:02:05<32:48:38, 12.88s/it, avr_loss=0.0263]
steps:   8%|▊         | 848/10016 [3:02:05<32:48:38, 12.88s/it, avr_loss=0.0263]
steps:   8%|▊         | 849/10016 [3:02:18<32:48:25, 12.88s/it, avr_loss=0.0263]
steps:   8%|▊         | 849/10016 [3:02:18<32:48:25, 12.88s/it, avr_loss=0.0265]
steps:   8%|▊         | 850/10016 [3:02:31<32:48:12, 12.88s/it, avr_loss=0.0265]
steps:   8%|▊         | 850/10016 [3:02:31<32:48:12, 12.88s/it, avr_loss=0.0264]
steps:   8%|▊         | 851/10016 [3:02:44<32:47:59, 12.88s/it, avr_loss=0.0264]
steps:   8%|▊         | 851/10016 [3:02:44<32:47:59, 12.88s/it, avr_loss=0.0264]
steps:   9%|▊         | 852/10016 [3:02:56<32:47:46, 12.88s/it, avr_loss=0.0264]
steps:   9%|▊         | 852/10016 [3:02:56<32:47:46, 12.88s/it, avr_loss=0.0264]
steps:   9%|▊         | 853/10016 [3:03:09<32:47:33, 12.88s/it, avr_loss=0.0264]
steps:   9%|▊         | 853/10016 [3:03:09<32:47:33, 12.88s/it, avr_loss=0.0263]
steps:   9%|▊         | 854/10016 [3:03:22<32:47:20, 12.88s/it, avr_loss=0.0263]
steps:   9%|▊         | 854/10016 [3:03:22<32:47:20, 12.88s/it, avr_loss=0.0263]
steps:   9%|▊         | 855/10016 [3:03:35<32:47:07, 12.88s/it, avr_loss=0.0263]
steps:   9%|▊         | 855/10016 [3:03:35<32:47:07, 12.88s/it, avr_loss=0.0263]
steps:   9%|▊         | 856/10016 [3:03:48<32:46:54, 12.88s/it, avr_loss=0.0263]
steps:   9%|▊         | 856/10016 [3:03:48<32:46:54, 12.88s/it, avr_loss=0.0264]
steps:   9%|▊         | 857/10016 [3:04:01<32:46:41, 12.88s/it, avr_loss=0.0264]
steps:   9%|▊         | 857/10016 [3:04:01<32:46:41, 12.88s/it, avr_loss=0.0264]
steps:   9%|▊         | 858/10016 [3:04:14<32:46:28, 12.88s/it, avr_loss=0.0264]
steps:   9%|▊         | 858/10016 [3:04:14<32:46:28, 12.88s/it, avr_loss=0.0264]
steps:   9%|▊         | 859/10016 [3:04:27<32:46:15, 12.88s/it, avr_loss=0.0264]
steps:   9%|▊         | 859/10016 [3:04:27<32:46:15, 12.88s/it, avr_loss=0.0264]
steps:   9%|▊         | 860/10016 [3:04:39<32:46:02, 12.88s/it, avr_loss=0.0264]
steps:   9%|▊         | 860/10016 [3:04:39<32:46:02, 12.88s/it, avr_loss=0.0264]
steps:   9%|▊         | 861/10016 [3:04:52<32:45:49, 12.88s/it, avr_loss=0.0264]
steps:   9%|▊         | 861/10016 [3:04:52<32:45:49, 12.88s/it, avr_loss=0.0265]
steps:   9%|▊         | 862/10016 [3:05:05<32:45:36, 12.88s/it, avr_loss=0.0265]
steps:   9%|▊         | 862/10016 [3:05:05<32:45:36, 12.88s/it, avr_loss=0.0266]
steps:   9%|▊         | 863/10016 [3:05:18<32:45:23, 12.88s/it, avr_loss=0.0266]
steps:   9%|▊         | 863/10016 [3:05:18<32:45:23, 12.88s/it, avr_loss=0.0266]
steps:   9%|▊         | 864/10016 [3:05:31<32:45:10, 12.88s/it, avr_loss=0.0266]
steps:   9%|▊         | 864/10016 [3:05:31<32:45:10, 12.88s/it, avr_loss=0.0266]
steps:   9%|▊         | 865/10016 [3:05:44<32:44:57, 12.88s/it, avr_loss=0.0266]
steps:   9%|▊         | 865/10016 [3:05:44<32:44:57, 12.88s/it, avr_loss=0.0266]
steps:   9%|▊         | 866/10016 [3:05:57<32:44:44, 12.88s/it, avr_loss=0.0266]
steps:   9%|▊         | 866/10016 [3:05:57<32:44:44, 12.88s/it, avr_loss=0.0266]
steps:   9%|▊         | 867/10016 [3:06:10<32:44:31, 12.88s/it, avr_loss=0.0266]
steps:   9%|▊         | 867/10016 [3:06:10<32:44:31, 12.88s/it, avr_loss=0.0266]
steps:   9%|▊         | 868/10016 [3:06:22<32:44:18, 12.88s/it, avr_loss=0.0266]
steps:   9%|▊         | 868/10016 [3:06:22<32:44:18, 12.88s/it, avr_loss=0.0266]
steps:   9%|▊         | 869/10016 [3:06:35<32:44:05, 12.88s/it, avr_loss=0.0266]
steps:   9%|▊         | 869/10016 [3:06:35<32:44:05, 12.88s/it, avr_loss=0.0266]
steps:   9%|▊         | 870/10016 [3:06:48<32:43:52, 12.88s/it, avr_loss=0.0266]
steps:   9%|▊         | 870/10016 [3:06:48<32:43:52, 12.88s/it, avr_loss=0.0266]
steps:   9%|▊         | 871/10016 [3:07:01<32:43:39, 12.88s/it, avr_loss=0.0266]
steps:   9%|▊         | 871/10016 [3:07:01<32:43:39, 12.88s/it, avr_loss=0.0266]
steps:   9%|▊         | 872/10016 [3:07:14<32:43:26, 12.88s/it, avr_loss=0.0266]
steps:   9%|▊         | 872/10016 [3:07:14<32:43:26, 12.88s/it, avr_loss=0.0266]
steps:   9%|▊         | 873/10016 [3:07:27<32:43:13, 12.88s/it, avr_loss=0.0266]
steps:   9%|▊         | 873/10016 [3:07:27<32:43:13, 12.88s/it, avr_loss=0.0266]
steps:   9%|▊         | 874/10016 [3:07:40<32:43:01, 12.88s/it, avr_loss=0.0266]
steps:   9%|▊         | 874/10016 [3:07:40<32:43:01, 12.88s/it, avr_loss=0.0266]
steps:   9%|▊         | 875/10016 [3:07:53<32:42:48, 12.88s/it, avr_loss=0.0266]
steps:   9%|▊         | 875/10016 [3:07:53<32:42:48, 12.88s/it, avr_loss=0.0266]
steps:   9%|▊         | 876/10016 [3:08:05<32:42:35, 12.88s/it, avr_loss=0.0266]
steps:   9%|▊         | 876/10016 [3:08:05<32:42:35, 12.88s/it, avr_loss=0.0266]
steps:   9%|▉         | 877/10016 [3:08:18<32:42:22, 12.88s/it, avr_loss=0.0266]
steps:   9%|▉         | 877/10016 [3:08:18<32:42:22, 12.88s/it, avr_loss=0.0266]
steps:   9%|▉         | 878/10016 [3:08:31<32:42:09, 12.88s/it, avr_loss=0.0266]
steps:   9%|▉         | 878/10016 [3:08:31<32:42:09, 12.88s/it, avr_loss=0.0266]
steps:   9%|▉         | 879/10016 [3:08:44<32:41:56, 12.88s/it, avr_loss=0.0266]
steps:   9%|▉         | 879/10016 [3:08:44<32:41:56, 12.88s/it, avr_loss=0.0266]
steps:   9%|▉         | 880/10016 [3:08:57<32:41:43, 12.88s/it, avr_loss=0.0266]
steps:   9%|▉         | 880/10016 [3:08:57<32:41:43, 12.88s/it, avr_loss=0.0266]
steps:   9%|▉         | 881/10016 [3:09:10<32:41:30, 12.88s/it, avr_loss=0.0266]
steps:   9%|▉         | 881/10016 [3:09:10<32:41:30, 12.88s/it, avr_loss=0.0266]
steps:   9%|▉         | 882/10016 [3:09:23<32:41:18, 12.88s/it, avr_loss=0.0266]
steps:   9%|▉         | 882/10016 [3:09:23<32:41:18, 12.88s/it, avr_loss=0.0266]
steps:   9%|▉         | 883/10016 [3:09:36<32:41:05, 12.88s/it, avr_loss=0.0266]
steps:   9%|▉         | 883/10016 [3:09:36<32:41:05, 12.88s/it, avr_loss=0.0266]
steps:   9%|▉         | 884/10016 [3:09:49<32:40:53, 12.88s/it, avr_loss=0.0266]
steps:   9%|▉         | 884/10016 [3:09:49<32:40:53, 12.88s/it, avr_loss=0.0266]
steps:   9%|▉         | 885/10016 [3:10:01<32:40:40, 12.88s/it, avr_loss=0.0266]
steps:   9%|▉         | 885/10016 [3:10:01<32:40:40, 12.88s/it, avr_loss=0.0266]
steps:   9%|▉         | 886/10016 [3:10:14<32:40:27, 12.88s/it, avr_loss=0.0266]
steps:   9%|▉         | 886/10016 [3:10:14<32:40:27, 12.88s/it, avr_loss=0.0266]
steps:   9%|▉         | 887/10016 [3:10:27<32:40:14, 12.88s/it, avr_loss=0.0266]
steps:   9%|▉         | 887/10016 [3:10:27<32:40:14, 12.88s/it, avr_loss=0.0266]
steps:   9%|▉         | 888/10016 [3:10:40<32:40:01, 12.88s/it, avr_loss=0.0266]
steps:   9%|▉         | 888/10016 [3:10:40<32:40:01, 12.88s/it, avr_loss=0.0266]
steps:   9%|▉         | 889/10016 [3:10:53<32:39:49, 12.88s/it, avr_loss=0.0266]
steps:   9%|▉         | 889/10016 [3:10:53<32:39:49, 12.88s/it, avr_loss=0.0265]
steps:   9%|▉         | 890/10016 [3:11:06<32:39:36, 12.88s/it, avr_loss=0.0265]
steps:   9%|▉         | 890/10016 [3:11:06<32:39:36, 12.88s/it, avr_loss=0.0265]
steps:   9%|▉         | 891/10016 [3:11:19<32:39:23, 12.88s/it, avr_loss=0.0265]
steps:   9%|▉         | 891/10016 [3:11:19<32:39:23, 12.88s/it, avr_loss=0.0265]
steps:   9%|▉         | 892/10016 [3:11:32<32:39:10, 12.88s/it, avr_loss=0.0265]
steps:   9%|▉         | 892/10016 [3:11:32<32:39:10, 12.88s/it, avr_loss=0.0265]
steps:   9%|▉         | 893/10016 [3:11:45<32:38:58, 12.88s/it, avr_loss=0.0265]
steps:   9%|▉         | 893/10016 [3:11:45<32:38:58, 12.88s/it, avr_loss=0.0265]
steps:   9%|▉         | 894/10016 [3:11:58<32:38:45, 12.88s/it, avr_loss=0.0265]
steps:   9%|▉         | 894/10016 [3:11:58<32:38:45, 12.88s/it, avr_loss=0.0265]
steps:   9%|▉         | 895/10016 [3:12:10<32:38:32, 12.88s/it, avr_loss=0.0265]
steps:   9%|▉         | 895/10016 [3:12:10<32:38:32, 12.88s/it, avr_loss=0.0265]
steps:   9%|▉         | 896/10016 [3:12:23<32:38:19, 12.88s/it, avr_loss=0.0265]
steps:   9%|▉         | 896/10016 [3:12:23<32:38:19, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 897/10016 [3:12:36<32:38:07, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 897/10016 [3:12:36<32:38:07, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 898/10016 [3:12:49<32:37:54, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 898/10016 [3:12:49<32:37:54, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 899/10016 [3:13:02<32:37:41, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 899/10016 [3:13:02<32:37:41, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 900/10016 [3:13:15<32:37:28, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 900/10016 [3:13:15<32:37:28, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 901/10016 [3:13:28<32:37:16, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 901/10016 [3:13:28<32:37:16, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 902/10016 [3:13:41<32:37:03, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 902/10016 [3:13:41<32:37:03, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 903/10016 [3:13:54<32:36:50, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 903/10016 [3:13:54<32:36:50, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 904/10016 [3:14:06<32:36:37, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 904/10016 [3:14:06<32:36:37, 12.88s/it, avr_loss=0.0265]
steps:   9%|▉         | 905/10016 [3:14:19<32:36:24, 12.88s/it, avr_loss=0.0265]
steps:   9%|▉         | 905/10016 [3:14:19<32:36:24, 12.88s/it, avr_loss=0.0262]
steps:   9%|▉         | 906/10016 [3:14:32<32:36:12, 12.88s/it, avr_loss=0.0262]
steps:   9%|▉         | 906/10016 [3:14:32<32:36:12, 12.88s/it, avr_loss=0.0262]
steps:   9%|▉         | 907/10016 [3:14:45<32:35:59, 12.88s/it, avr_loss=0.0262]
steps:   9%|▉         | 907/10016 [3:14:45<32:35:59, 12.88s/it, avr_loss=0.0262]
steps:   9%|▉         | 908/10016 [3:14:58<32:35:46, 12.88s/it, avr_loss=0.0262]
steps:   9%|▉         | 908/10016 [3:14:58<32:35:46, 12.88s/it, avr_loss=0.0261]
steps:   9%|▉         | 909/10016 [3:15:11<32:35:33, 12.88s/it, avr_loss=0.0261]
steps:   9%|▉         | 909/10016 [3:15:11<32:35:33, 12.88s/it, avr_loss=0.0261]
steps:   9%|▉         | 910/10016 [3:15:24<32:35:21, 12.88s/it, avr_loss=0.0261]
steps:   9%|▉         | 910/10016 [3:15:24<32:35:21, 12.88s/it, avr_loss=0.0261]
steps:   9%|▉         | 911/10016 [3:15:37<32:35:08, 12.88s/it, avr_loss=0.0261]
steps:   9%|▉         | 911/10016 [3:15:37<32:35:08, 12.88s/it, avr_loss=0.0261]
steps:   9%|▉         | 912/10016 [3:15:50<32:34:55, 12.88s/it, avr_loss=0.0261]
steps:   9%|▉         | 912/10016 [3:15:50<32:34:55, 12.88s/it, avr_loss=0.0261]
steps:   9%|▉         | 913/10016 [3:16:03<32:34:42, 12.88s/it, avr_loss=0.0261]
steps:   9%|▉         | 913/10016 [3:16:03<32:34:42, 12.88s/it, avr_loss=0.0261]
steps:   9%|▉         | 914/10016 [3:16:15<32:34:30, 12.88s/it, avr_loss=0.0261]
steps:   9%|▉         | 914/10016 [3:16:15<32:34:30, 12.88s/it, avr_loss=0.026] 
steps:   9%|▉         | 915/10016 [3:16:28<32:34:17, 12.88s/it, avr_loss=0.026]
steps:   9%|▉         | 915/10016 [3:16:28<32:34:17, 12.88s/it, avr_loss=0.026]
steps:   9%|▉         | 916/10016 [3:16:41<32:34:04, 12.88s/it, avr_loss=0.026]
steps:   9%|▉         | 916/10016 [3:16:41<32:34:04, 12.88s/it, avr_loss=0.026]
steps:   9%|▉         | 917/10016 [3:16:54<32:33:51, 12.88s/it, avr_loss=0.026]
steps:   9%|▉         | 917/10016 [3:16:54<32:33:51, 12.88s/it, avr_loss=0.026]
steps:   9%|▉         | 918/10016 [3:17:07<32:33:38, 12.88s/it, avr_loss=0.026]
steps:   9%|▉         | 918/10016 [3:17:07<32:33:38, 12.88s/it, avr_loss=0.026]
steps:   9%|▉         | 919/10016 [3:17:20<32:33:26, 12.88s/it, avr_loss=0.026]
steps:   9%|▉         | 919/10016 [3:17:20<32:33:26, 12.88s/it, avr_loss=0.026]
steps:   9%|▉         | 920/10016 [3:17:33<32:33:13, 12.88s/it, avr_loss=0.026]
steps:   9%|▉         | 920/10016 [3:17:33<32:33:13, 12.88s/it, avr_loss=0.026]
steps:   9%|▉         | 921/10016 [3:17:46<32:33:00, 12.88s/it, avr_loss=0.026]
steps:   9%|▉         | 921/10016 [3:17:46<32:33:00, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 922/10016 [3:17:59<32:32:47, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 922/10016 [3:17:59<32:32:47, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 923/10016 [3:18:12<32:32:34, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 923/10016 [3:18:12<32:32:34, 12.88s/it, avr_loss=0.0263]
steps:   9%|▉         | 924/10016 [3:18:24<32:32:22, 12.88s/it, avr_loss=0.0263]
steps:   9%|▉         | 924/10016 [3:18:24<32:32:22, 12.88s/it, avr_loss=0.0263]
steps:   9%|▉         | 925/10016 [3:18:37<32:32:09, 12.88s/it, avr_loss=0.0263]
steps:   9%|▉         | 925/10016 [3:18:37<32:32:09, 12.88s/it, avr_loss=0.0263]
steps:   9%|▉         | 926/10016 [3:18:50<32:31:56, 12.88s/it, avr_loss=0.0263]
steps:   9%|▉         | 926/10016 [3:18:50<32:31:56, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 927/10016 [3:19:03<32:31:44, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 927/10016 [3:19:03<32:31:44, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 928/10016 [3:19:16<32:31:31, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 928/10016 [3:19:16<32:31:31, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 929/10016 [3:19:29<32:31:18, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 929/10016 [3:19:29<32:31:18, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 930/10016 [3:19:42<32:31:05, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 930/10016 [3:19:42<32:31:05, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 931/10016 [3:19:55<32:30:52, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 931/10016 [3:19:55<32:30:52, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 932/10016 [3:20:08<32:30:40, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 932/10016 [3:20:08<32:30:40, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 933/10016 [3:20:20<32:30:27, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 933/10016 [3:20:20<32:30:27, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 934/10016 [3:20:33<32:30:14, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 934/10016 [3:20:33<32:30:14, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 935/10016 [3:20:46<32:30:01, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 935/10016 [3:20:46<32:30:01, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 936/10016 [3:20:59<32:29:48, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 936/10016 [3:20:59<32:29:48, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 937/10016 [3:21:12<32:29:36, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 937/10016 [3:21:12<32:29:36, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 938/10016 [3:21:25<32:29:23, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 938/10016 [3:21:25<32:29:23, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 939/10016 [3:21:38<32:29:10, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 939/10016 [3:21:38<32:29:10, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 940/10016 [3:21:51<32:28:57, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 940/10016 [3:21:51<32:28:57, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 941/10016 [3:22:04<32:28:44, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 941/10016 [3:22:04<32:28:44, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 942/10016 [3:22:16<32:28:31, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 942/10016 [3:22:16<32:28:31, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 943/10016 [3:22:29<32:28:19, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 943/10016 [3:22:29<32:28:19, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 944/10016 [3:22:42<32:28:06, 12.88s/it, avr_loss=0.0264]
steps:   9%|▉         | 944/10016 [3:22:42<32:28:06, 12.88s/it, avr_loss=0.0263]
steps:   9%|▉         | 945/10016 [3:22:55<32:27:53, 12.88s/it, avr_loss=0.0263]
steps:   9%|▉         | 945/10016 [3:22:55<32:27:53, 12.88s/it, avr_loss=0.0263]
steps:   9%|▉         | 946/10016 [3:23:08<32:27:40, 12.88s/it, avr_loss=0.0263]
steps:   9%|▉         | 946/10016 [3:23:08<32:27:40, 12.88s/it, avr_loss=0.0263]
steps:   9%|▉         | 947/10016 [3:23:21<32:27:28, 12.88s/it, avr_loss=0.0263]
steps:   9%|▉         | 947/10016 [3:23:21<32:27:28, 12.88s/it, avr_loss=0.0263]
steps:   9%|▉         | 948/10016 [3:23:34<32:27:15, 12.88s/it, avr_loss=0.0263]
steps:   9%|▉         | 948/10016 [3:23:34<32:27:15, 12.88s/it, avr_loss=0.0263]
steps:   9%|▉         | 949/10016 [3:23:47<32:27:02, 12.88s/it, avr_loss=0.0263]
steps:   9%|▉         | 949/10016 [3:23:47<32:27:02, 12.88s/it, avr_loss=0.0263]
steps:   9%|▉         | 950/10016 [3:24:00<32:26:49, 12.88s/it, avr_loss=0.0263]
steps:   9%|▉         | 950/10016 [3:24:00<32:26:49, 12.88s/it, avr_loss=0.0262]
steps:   9%|▉         | 951/10016 [3:24:13<32:26:37, 12.88s/it, avr_loss=0.0262]
steps:   9%|▉         | 951/10016 [3:24:13<32:26:37, 12.88s/it, avr_loss=0.0262]
steps:  10%|▉         | 952/10016 [3:24:25<32:26:24, 12.88s/it, avr_loss=0.0262]
steps:  10%|▉         | 952/10016 [3:24:25<32:26:24, 12.88s/it, avr_loss=0.0261]
steps:  10%|▉         | 953/10016 [3:24:38<32:26:11, 12.88s/it, avr_loss=0.0261]
steps:  10%|▉         | 953/10016 [3:24:38<32:26:11, 12.88s/it, avr_loss=0.0261]
steps:  10%|▉         | 954/10016 [3:24:51<32:25:58, 12.88s/it, avr_loss=0.0261]
steps:  10%|▉         | 954/10016 [3:24:51<32:25:58, 12.88s/it, avr_loss=0.026] 
steps:  10%|▉         | 955/10016 [3:25:04<32:25:45, 12.88s/it, avr_loss=0.026]
steps:  10%|▉         | 955/10016 [3:25:04<32:25:45, 12.88s/it, avr_loss=0.026]
steps:  10%|▉         | 956/10016 [3:25:17<32:25:33, 12.88s/it, avr_loss=0.026]
steps:  10%|▉         | 956/10016 [3:25:17<32:25:33, 12.88s/it, avr_loss=0.026]
steps:  10%|▉         | 957/10016 [3:25:30<32:25:20, 12.88s/it, avr_loss=0.026]
steps:  10%|▉         | 957/10016 [3:25:30<32:25:20, 12.88s/it, avr_loss=0.026]
steps:  10%|▉         | 958/10016 [3:25:43<32:25:07, 12.88s/it, avr_loss=0.026]
steps:  10%|▉         | 958/10016 [3:25:43<32:25:07, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 959/10016 [3:25:56<32:24:54, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 959/10016 [3:25:56<32:24:54, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 960/10016 [3:26:09<32:24:42, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 960/10016 [3:26:09<32:24:42, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 961/10016 [3:26:22<32:24:29, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 961/10016 [3:26:22<32:24:29, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 962/10016 [3:26:34<32:24:16, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 962/10016 [3:26:34<32:24:16, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 963/10016 [3:26:47<32:24:03, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 963/10016 [3:26:47<32:24:03, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 964/10016 [3:27:00<32:23:50, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 964/10016 [3:27:00<32:23:50, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 965/10016 [3:27:13<32:23:38, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 965/10016 [3:27:13<32:23:38, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 966/10016 [3:27:26<32:23:25, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 966/10016 [3:27:26<32:23:25, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 967/10016 [3:27:39<32:23:12, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 967/10016 [3:27:39<32:23:12, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 968/10016 [3:27:52<32:22:59, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 968/10016 [3:27:52<32:22:59, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 969/10016 [3:28:05<32:22:47, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 969/10016 [3:28:05<32:22:47, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 970/10016 [3:28:18<32:22:34, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 970/10016 [3:28:18<32:22:34, 12.88s/it, avr_loss=0.0258]
steps:  10%|▉         | 971/10016 [3:28:31<32:22:22, 12.88s/it, avr_loss=0.0258]
steps:  10%|▉         | 971/10016 [3:28:31<32:22:22, 12.88s/it, avr_loss=0.0258]
steps:  10%|▉         | 972/10016 [3:28:43<32:22:09, 12.88s/it, avr_loss=0.0258]
steps:  10%|▉         | 972/10016 [3:28:43<32:22:09, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 973/10016 [3:28:56<32:21:56, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 973/10016 [3:28:56<32:21:56, 12.88s/it, avr_loss=0.0258]
steps:  10%|▉         | 974/10016 [3:29:09<32:21:43, 12.88s/it, avr_loss=0.0258]
steps:  10%|▉         | 974/10016 [3:29:09<32:21:43, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 975/10016 [3:29:22<32:21:31, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 975/10016 [3:29:22<32:21:31, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 976/10016 [3:29:35<32:21:18, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 976/10016 [3:29:35<32:21:18, 12.88s/it, avr_loss=0.0258]
steps:  10%|▉         | 977/10016 [3:29:48<32:21:05, 12.88s/it, avr_loss=0.0258]
steps:  10%|▉         | 977/10016 [3:29:48<32:21:05, 12.88s/it, avr_loss=0.0258]
steps:  10%|▉         | 978/10016 [3:30:01<32:20:52, 12.88s/it, avr_loss=0.0258]
steps:  10%|▉         | 978/10016 [3:30:01<32:20:52, 12.88s/it, avr_loss=0.0258]
steps:  10%|▉         | 979/10016 [3:30:14<32:20:39, 12.88s/it, avr_loss=0.0258]
steps:  10%|▉         | 979/10016 [3:30:14<32:20:39, 12.88s/it, avr_loss=0.0258]
steps:  10%|▉         | 980/10016 [3:30:27<32:20:26, 12.88s/it, avr_loss=0.0258]
steps:  10%|▉         | 980/10016 [3:30:27<32:20:26, 12.88s/it, avr_loss=0.0258]
steps:  10%|▉         | 981/10016 [3:30:39<32:20:14, 12.88s/it, avr_loss=0.0258]
steps:  10%|▉         | 981/10016 [3:30:39<32:20:14, 12.88s/it, avr_loss=0.0258]
steps:  10%|▉         | 982/10016 [3:30:52<32:20:01, 12.88s/it, avr_loss=0.0258]
steps:  10%|▉         | 982/10016 [3:30:52<32:20:01, 12.88s/it, avr_loss=0.0258]
steps:  10%|▉         | 983/10016 [3:31:05<32:19:48, 12.88s/it, avr_loss=0.0258]
steps:  10%|▉         | 983/10016 [3:31:05<32:19:48, 12.88s/it, avr_loss=0.0258]
steps:  10%|▉         | 984/10016 [3:31:18<32:19:35, 12.88s/it, avr_loss=0.0258]
steps:  10%|▉         | 984/10016 [3:31:18<32:19:35, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 985/10016 [3:31:31<32:19:22, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 985/10016 [3:31:31<32:19:22, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 986/10016 [3:31:44<32:19:09, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 986/10016 [3:31:44<32:19:09, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 987/10016 [3:31:57<32:18:57, 12.88s/it, avr_loss=0.0259]
steps:  10%|▉         | 987/10016 [3:31:57<32:18:57, 12.88s/it, avr_loss=0.0257]
steps:  10%|▉         | 988/10016 [3:32:10<32:18:44, 12.88s/it, avr_loss=0.0257]
steps:  10%|▉         | 988/10016 [3:32:10<32:18:44, 12.88s/it, avr_loss=0.0256]
steps:  10%|▉         | 989/10016 [3:32:23<32:18:31, 12.88s/it, avr_loss=0.0256]
steps:  10%|▉         | 989/10016 [3:32:23<32:18:31, 12.88s/it, avr_loss=0.0256]
steps:  10%|▉         | 990/10016 [3:32:35<32:18:18, 12.88s/it, avr_loss=0.0256]
steps:  10%|▉         | 990/10016 [3:32:35<32:18:18, 12.88s/it, avr_loss=0.0257]
steps:  10%|▉         | 991/10016 [3:32:49<32:18:06, 12.88s/it, avr_loss=0.0257]
steps:  10%|▉         | 991/10016 [3:32:49<32:18:06, 12.88s/it, avr_loss=0.0257]
steps:  10%|▉         | 992/10016 [3:33:01<32:17:53, 12.88s/it, avr_loss=0.0257]
steps:  10%|▉         | 992/10016 [3:33:01<32:17:53, 12.88s/it, avr_loss=0.0257]
steps:  10%|▉         | 993/10016 [3:33:14<32:17:40, 12.88s/it, avr_loss=0.0257]
steps:  10%|▉         | 993/10016 [3:33:14<32:17:40, 12.88s/it, avr_loss=0.0258]
steps:  10%|▉         | 994/10016 [3:33:27<32:17:27, 12.88s/it, avr_loss=0.0258]
steps:  10%|▉         | 994/10016 [3:33:27<32:17:27, 12.88s/it, avr_loss=0.0257]
steps:  10%|▉         | 995/10016 [3:33:40<32:17:14, 12.88s/it, avr_loss=0.0257]
steps:  10%|▉         | 995/10016 [3:33:40<32:17:14, 12.88s/it, avr_loss=0.0257]
steps:  10%|▉         | 996/10016 [3:33:53<32:17:01, 12.88s/it, avr_loss=0.0257]
steps:  10%|▉         | 996/10016 [3:33:53<32:17:01, 12.88s/it, avr_loss=0.0257]
steps:  10%|▉         | 997/10016 [3:34:06<32:16:48, 12.88s/it, avr_loss=0.0257]
steps:  10%|▉         | 997/10016 [3:34:06<32:16:48, 12.88s/it, avr_loss=0.0257]
steps:  10%|▉         | 998/10016 [3:34:19<32:16:35, 12.88s/it, avr_loss=0.0257]
steps:  10%|▉         | 998/10016 [3:34:19<32:16:35, 12.88s/it, avr_loss=0.0257]
steps:  10%|▉         | 999/10016 [3:34:31<32:16:22, 12.88s/it, avr_loss=0.0257]
steps:  10%|▉         | 999/10016 [3:34:31<32:16:22, 12.88s/it, avr_loss=0.0258]
steps:  10%|▉         | 1000/10016 [3:34:44<32:16:09, 12.88s/it, avr_loss=0.0258]
steps:  10%|▉         | 1000/10016 [3:34:44<32:16:09, 12.88s/it, avr_loss=0.0258]
steps:  10%|▉         | 1001/10016 [3:34:57<32:15:56, 12.88s/it, avr_loss=0.0258]
steps:  10%|▉         | 1001/10016 [3:34:57<32:15:56, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1002/10016 [3:35:10<32:15:43, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1002/10016 [3:35:10<32:15:43, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1003/10016 [3:35:23<32:15:30, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1003/10016 [3:35:23<32:15:30, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1004/10016 [3:35:36<32:15:17, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1004/10016 [3:35:36<32:15:17, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1005/10016 [3:35:49<32:15:04, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1005/10016 [3:35:49<32:15:04, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1006/10016 [3:36:02<32:14:51, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1006/10016 [3:36:02<32:14:51, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1007/10016 [3:36:14<32:14:38, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1007/10016 [3:36:14<32:14:38, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1008/10016 [3:36:27<32:14:25, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1008/10016 [3:36:27<32:14:25, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1009/10016 [3:36:40<32:14:12, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1009/10016 [3:36:40<32:14:12, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1010/10016 [3:36:53<32:13:59, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1010/10016 [3:36:53<32:13:59, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1011/10016 [3:37:06<32:13:46, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1011/10016 [3:37:06<32:13:46, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1012/10016 [3:37:19<32:13:33, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1012/10016 [3:37:19<32:13:33, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1013/10016 [3:37:32<32:13:20, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1013/10016 [3:37:32<32:13:20, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1014/10016 [3:37:45<32:13:07, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1014/10016 [3:37:45<32:13:07, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1015/10016 [3:37:57<32:12:54, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1015/10016 [3:37:57<32:12:54, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1016/10016 [3:38:10<32:12:41, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1016/10016 [3:38:10<32:12:41, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1017/10016 [3:38:23<32:12:28, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1017/10016 [3:38:23<32:12:28, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1018/10016 [3:38:36<32:12:15, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1018/10016 [3:38:36<32:12:15, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1019/10016 [3:38:49<32:12:01, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1019/10016 [3:38:49<32:12:01, 12.88s/it, avr_loss=0.0259]
steps:  10%|█         | 1020/10016 [3:39:02<32:11:48, 12.88s/it, avr_loss=0.0259]
steps:  10%|█         | 1020/10016 [3:39:02<32:11:48, 12.88s/it, avr_loss=0.0259]
steps:  10%|█         | 1021/10016 [3:39:15<32:11:35, 12.88s/it, avr_loss=0.0259]
steps:  10%|█         | 1021/10016 [3:39:15<32:11:35, 12.88s/it, avr_loss=0.0257]
steps:  10%|█         | 1022/10016 [3:39:27<32:11:22, 12.88s/it, avr_loss=0.0257]
steps:  10%|█         | 1022/10016 [3:39:27<32:11:22, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1023/10016 [3:39:40<32:11:09, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1023/10016 [3:39:40<32:11:09, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1024/10016 [3:39:53<32:10:56, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1024/10016 [3:39:53<32:10:56, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1025/10016 [3:40:06<32:10:43, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1025/10016 [3:40:06<32:10:43, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1026/10016 [3:40:19<32:10:30, 12.88s/it, avr_loss=0.0258]
steps:  10%|█         | 1026/10016 [3:40:19<32:10:30, 12.88s/it, avr_loss=0.0257]
steps:  10%|█         | 1027/10016 [3:40:32<32:10:18, 12.88s/it, avr_loss=0.0257]
steps:  10%|█         | 1027/10016 [3:40:32<32:10:18, 12.88s/it, avr_loss=0.0257]
steps:  10%|█         | 1028/10016 [3:40:45<32:10:05, 12.88s/it, avr_loss=0.0257]
steps:  10%|█         | 1028/10016 [3:40:45<32:10:05, 12.88s/it, avr_loss=0.0257]
steps:  10%|█         | 1029/10016 [3:40:58<32:09:52, 12.88s/it, avr_loss=0.0257]
steps:  10%|█         | 1029/10016 [3:40:58<32:09:52, 12.88s/it, avr_loss=0.0257]
steps:  10%|█         | 1030/10016 [3:41:10<32:09:39, 12.88s/it, avr_loss=0.0257]
steps:  10%|█         | 1030/10016 [3:41:10<32:09:39, 12.88s/it, avr_loss=0.0257]
steps:  10%|█         | 1031/10016 [3:41:23<32:09:26, 12.88s/it, avr_loss=0.0257]
steps:  10%|█         | 1031/10016 [3:41:23<32:09:26, 12.88s/it, avr_loss=0.0257]
steps:  10%|█         | 1032/10016 [3:41:36<32:09:14, 12.88s/it, avr_loss=0.0257]
steps:  10%|█         | 1032/10016 [3:41:36<32:09:14, 12.88s/it, avr_loss=0.0257]
steps:  10%|█         | 1033/10016 [3:41:49<32:09:01, 12.88s/it, avr_loss=0.0257]
steps:  10%|█         | 1033/10016 [3:41:49<32:09:01, 12.88s/it, avr_loss=0.0257]
steps:  10%|█         | 1034/10016 [3:42:02<32:08:48, 12.88s/it, avr_loss=0.0257]
steps:  10%|█         | 1034/10016 [3:42:02<32:08:48, 12.88s/it, avr_loss=0.0257]
steps:  10%|█         | 1035/10016 [3:42:15<32:08:35, 12.88s/it, avr_loss=0.0257]
steps:  10%|█         | 1035/10016 [3:42:15<32:08:35, 12.88s/it, avr_loss=0.0257]
steps:  10%|█         | 1036/10016 [3:42:28<32:08:22, 12.88s/it, avr_loss=0.0257]
steps:  10%|█         | 1036/10016 [3:42:28<32:08:22, 12.88s/it, avr_loss=0.0257]
steps:  10%|█         | 1037/10016 [3:42:41<32:08:09, 12.88s/it, avr_loss=0.0257]
steps:  10%|█         | 1037/10016 [3:42:41<32:08:09, 12.88s/it, avr_loss=0.0257]
steps:  10%|█         | 1038/10016 [3:42:54<32:07:56, 12.88s/it, avr_loss=0.0257]
steps:  10%|█         | 1038/10016 [3:42:54<32:07:56, 12.88s/it, avr_loss=0.0256]
steps:  10%|█         | 1039/10016 [3:43:06<32:07:43, 12.88s/it, avr_loss=0.0256]
steps:  10%|█         | 1039/10016 [3:43:06<32:07:43, 12.88s/it, avr_loss=0.0256]
steps:  10%|█         | 1040/10016 [3:43:19<32:07:30, 12.88s/it, avr_loss=0.0256]
steps:  10%|█         | 1040/10016 [3:43:19<32:07:30, 12.88s/it, avr_loss=0.0256]
steps:  10%|█         | 1041/10016 [3:43:32<32:07:17, 12.88s/it, avr_loss=0.0256]
steps:  10%|█         | 1041/10016 [3:43:32<32:07:17, 12.88s/it, avr_loss=0.0256]
steps:  10%|█         | 1042/10016 [3:43:45<32:07:04, 12.88s/it, avr_loss=0.0256]
steps:  10%|█         | 1042/10016 [3:43:45<32:07:04, 12.88s/it, avr_loss=0.0256]
steps:  10%|█         | 1043/10016 [3:43:58<32:06:51, 12.88s/it, avr_loss=0.0256]
steps:  10%|█         | 1043/10016 [3:43:58<32:06:51, 12.88s/it, avr_loss=0.0256]
steps:  10%|█         | 1044/10016 [3:44:11<32:06:38, 12.88s/it, avr_loss=0.0256]
steps:  10%|█         | 1044/10016 [3:44:11<32:06:38, 12.88s/it, avr_loss=0.0256]
steps:  10%|█         | 1045/10016 [3:44:24<32:06:26, 12.88s/it, avr_loss=0.0256]
steps:  10%|█         | 1045/10016 [3:44:24<32:06:26, 12.88s/it, avr_loss=0.0256]
steps:  10%|█         | 1046/10016 [3:44:37<32:06:13, 12.88s/it, avr_loss=0.0256]
steps:  10%|█         | 1046/10016 [3:44:37<32:06:13, 12.88s/it, avr_loss=0.0256]
steps:  10%|█         | 1047/10016 [3:44:50<32:06:00, 12.88s/it, avr_loss=0.0256]
steps:  10%|█         | 1047/10016 [3:44:50<32:06:00, 12.88s/it, avr_loss=0.0256]
steps:  10%|█         | 1048/10016 [3:45:02<32:05:48, 12.88s/it, avr_loss=0.0256]
steps:  10%|█         | 1048/10016 [3:45:02<32:05:48, 12.88s/it, avr_loss=0.0256]
steps:  10%|█         | 1049/10016 [3:45:15<32:05:35, 12.88s/it, avr_loss=0.0256]
steps:  10%|█         | 1049/10016 [3:45:15<32:05:35, 12.88s/it, avr_loss=0.0256]
steps:  10%|█         | 1050/10016 [3:45:28<32:05:22, 12.88s/it, avr_loss=0.0256]
steps:  10%|█         | 1050/10016 [3:45:28<32:05:22, 12.88s/it, avr_loss=0.0256]
steps:  10%|█         | 1051/10016 [3:45:41<32:05:09, 12.88s/it, avr_loss=0.0256]
steps:  10%|█         | 1051/10016 [3:45:41<32:05:09, 12.88s/it, avr_loss=0.0257]
steps:  11%|█         | 1052/10016 [3:45:54<32:04:56, 12.88s/it, avr_loss=0.0257]
steps:  11%|█         | 1052/10016 [3:45:54<32:04:56, 12.88s/it, avr_loss=0.0256]
steps:  11%|█         | 1053/10016 [3:46:07<32:04:43, 12.88s/it, avr_loss=0.0256]
steps:  11%|█         | 1053/10016 [3:46:07<32:04:43, 12.88s/it, avr_loss=0.0256]
steps:  11%|█         | 1054/10016 [3:46:20<32:04:30, 12.88s/it, avr_loss=0.0256]
steps:  11%|█         | 1054/10016 [3:46:20<32:04:30, 12.88s/it, avr_loss=0.0256]
steps:  11%|█         | 1055/10016 [3:46:33<32:04:17, 12.88s/it, avr_loss=0.0256]
steps:  11%|█         | 1055/10016 [3:46:33<32:04:17, 12.88s/it, avr_loss=0.0256]
steps:  11%|█         | 1056/10016 [3:46:45<32:04:04, 12.88s/it, avr_loss=0.0256]
steps:  11%|█         | 1056/10016 [3:46:45<32:04:04, 12.88s/it, avr_loss=0.0257]
steps:  11%|█         | 1057/10016 [3:46:58<32:03:51, 12.88s/it, avr_loss=0.0257]
steps:  11%|█         | 1057/10016 [3:46:58<32:03:51, 12.88s/it, avr_loss=0.0259]
steps:  11%|█         | 1058/10016 [3:47:11<32:03:38, 12.88s/it, avr_loss=0.0259]
steps:  11%|█         | 1058/10016 [3:47:11<32:03:38, 12.88s/it, avr_loss=0.0259]
steps:  11%|█         | 1059/10016 [3:47:24<32:03:25, 12.88s/it, avr_loss=0.0259]
steps:  11%|█         | 1059/10016 [3:47:24<32:03:25, 12.88s/it, avr_loss=0.0259]
steps:  11%|█         | 1060/10016 [3:47:37<32:03:13, 12.88s/it, avr_loss=0.0259]
steps:  11%|█         | 1060/10016 [3:47:37<32:03:13, 12.88s/it, avr_loss=0.0259]
steps:  11%|█         | 1061/10016 [3:47:50<32:03:00, 12.88s/it, avr_loss=0.0259]
steps:  11%|█         | 1061/10016 [3:47:50<32:03:00, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1062/10016 [3:48:03<32:02:47, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1062/10016 [3:48:03<32:02:47, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1063/10016 [3:48:16<32:02:34, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1063/10016 [3:48:16<32:02:34, 12.88s/it, avr_loss=0.0259]
steps:  11%|█         | 1064/10016 [3:48:29<32:02:21, 12.88s/it, avr_loss=0.0259]
steps:  11%|█         | 1064/10016 [3:48:29<32:02:21, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1065/10016 [3:48:41<32:02:08, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1065/10016 [3:48:41<32:02:08, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1066/10016 [3:48:54<32:01:55, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1066/10016 [3:48:54<32:01:55, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1067/10016 [3:49:07<32:01:42, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1067/10016 [3:49:07<32:01:42, 12.88s/it, avr_loss=0.0259]
steps:  11%|█         | 1068/10016 [3:49:20<32:01:29, 12.88s/it, avr_loss=0.0259]
steps:  11%|█         | 1068/10016 [3:49:20<32:01:29, 12.88s/it, avr_loss=0.0259]
steps:  11%|█         | 1069/10016 [3:49:33<32:01:16, 12.88s/it, avr_loss=0.0259]
steps:  11%|█         | 1069/10016 [3:49:33<32:01:16, 12.88s/it, avr_loss=0.0259]
steps:  11%|█         | 1070/10016 [3:49:46<32:01:04, 12.88s/it, avr_loss=0.0259]
steps:  11%|█         | 1070/10016 [3:49:46<32:01:04, 12.88s/it, avr_loss=0.0259]
steps:  11%|█         | 1071/10016 [3:49:59<32:00:51, 12.88s/it, avr_loss=0.0259]
steps:  11%|█         | 1071/10016 [3:49:59<32:00:51, 12.88s/it, avr_loss=0.0259]
steps:  11%|█         | 1072/10016 [3:50:12<32:00:39, 12.88s/it, avr_loss=0.0259]
steps:  11%|█         | 1072/10016 [3:50:12<32:00:39, 12.88s/it, avr_loss=0.0259]
steps:  11%|█         | 1073/10016 [3:50:25<32:00:26, 12.88s/it, avr_loss=0.0259]
steps:  11%|█         | 1073/10016 [3:50:25<32:00:26, 12.88s/it, avr_loss=0.0259]
steps:  11%|█         | 1074/10016 [3:50:38<32:00:14, 12.88s/it, avr_loss=0.0259]
steps:  11%|█         | 1074/10016 [3:50:38<32:00:14, 12.88s/it, avr_loss=0.0259]
steps:  11%|█         | 1075/10016 [3:50:50<32:00:01, 12.88s/it, avr_loss=0.0259]
steps:  11%|█         | 1075/10016 [3:50:50<32:00:01, 12.88s/it, avr_loss=0.0259]
steps:  11%|█         | 1076/10016 [3:51:03<31:59:48, 12.88s/it, avr_loss=0.0259]
steps:  11%|█         | 1076/10016 [3:51:03<31:59:48, 12.88s/it, avr_loss=0.0259]
steps:  11%|█         | 1077/10016 [3:51:16<31:59:35, 12.88s/it, avr_loss=0.0259]
steps:  11%|█         | 1077/10016 [3:51:16<31:59:35, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1078/10016 [3:51:29<31:59:23, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1078/10016 [3:51:29<31:59:23, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1079/10016 [3:51:42<31:59:10, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1079/10016 [3:51:42<31:59:10, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1080/10016 [3:51:55<31:58:57, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1080/10016 [3:51:55<31:58:57, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1081/10016 [3:52:08<31:58:44, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1081/10016 [3:52:08<31:58:44, 12.88s/it, avr_loss=0.0257]
steps:  11%|█         | 1082/10016 [3:52:21<31:58:32, 12.88s/it, avr_loss=0.0257]
steps:  11%|█         | 1082/10016 [3:52:21<31:58:32, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1083/10016 [3:52:34<31:58:19, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1083/10016 [3:52:34<31:58:19, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1084/10016 [3:52:47<31:58:06, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1084/10016 [3:52:47<31:58:06, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1085/10016 [3:52:59<31:57:53, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1085/10016 [3:52:59<31:57:53, 12.88s/it, avr_loss=0.0257]
steps:  11%|█         | 1086/10016 [3:53:12<31:57:40, 12.88s/it, avr_loss=0.0257]
steps:  11%|█         | 1086/10016 [3:53:12<31:57:40, 12.88s/it, avr_loss=0.0257]
steps:  11%|█         | 1087/10016 [3:53:25<31:57:28, 12.88s/it, avr_loss=0.0257]
steps:  11%|█         | 1087/10016 [3:53:25<31:57:28, 12.88s/it, avr_loss=0.0257]
steps:  11%|█         | 1088/10016 [3:53:38<31:57:15, 12.88s/it, avr_loss=0.0257]
steps:  11%|█         | 1088/10016 [3:53:38<31:57:15, 12.88s/it, avr_loss=0.0257]
steps:  11%|█         | 1089/10016 [3:53:51<31:57:02, 12.88s/it, avr_loss=0.0257]
steps:  11%|█         | 1089/10016 [3:53:51<31:57:02, 12.88s/it, avr_loss=0.0257]
steps:  11%|█         | 1090/10016 [3:54:04<31:56:49, 12.88s/it, avr_loss=0.0257]
steps:  11%|█         | 1090/10016 [3:54:04<31:56:49, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1091/10016 [3:54:17<31:56:37, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1091/10016 [3:54:17<31:56:37, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1092/10016 [3:54:30<31:56:24, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1092/10016 [3:54:30<31:56:24, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1093/10016 [3:54:43<31:56:11, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1093/10016 [3:54:43<31:56:11, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1094/10016 [3:54:56<31:55:58, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1094/10016 [3:54:56<31:55:58, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1095/10016 [3:55:08<31:55:45, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1095/10016 [3:55:08<31:55:45, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1096/10016 [3:55:21<31:55:33, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1096/10016 [3:55:21<31:55:33, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1097/10016 [3:55:34<31:55:20, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1097/10016 [3:55:34<31:55:20, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1098/10016 [3:55:47<31:55:07, 12.88s/it, avr_loss=0.0258]
steps:  11%|█         | 1098/10016 [3:55:47<31:55:07, 12.88s/it, avr_loss=0.0259]
steps:  11%|█         | 1099/10016 [3:56:00<31:54:54, 12.88s/it, avr_loss=0.0259]
steps:  11%|█         | 1099/10016 [3:56:00<31:54:54, 12.88s/it, avr_loss=0.0259]
steps:  11%|█         | 1100/10016 [3:56:13<31:54:43, 12.89s/it, avr_loss=0.0259]
steps:  11%|█         | 1100/10016 [3:56:13<31:54:43, 12.89s/it, avr_loss=0.0259]
steps:  11%|█         | 1101/10016 [3:56:26<31:54:30, 12.89s/it, avr_loss=0.0259]
steps:  11%|█         | 1101/10016 [3:56:26<31:54:30, 12.89s/it, avr_loss=0.0259]
steps:  11%|█         | 1102/10016 [3:56:39<31:54:17, 12.89s/it, avr_loss=0.0259]
steps:  11%|█         | 1102/10016 [3:56:39<31:54:17, 12.89s/it, avr_loss=0.0259]
steps:  11%|█         | 1103/10016 [3:56:52<31:54:04, 12.89s/it, avr_loss=0.0259]
steps:  11%|█         | 1103/10016 [3:56:52<31:54:04, 12.89s/it, avr_loss=0.0259]
steps:  11%|█         | 1104/10016 [3:57:05<31:53:51, 12.89s/it, avr_loss=0.0259]
steps:  11%|█         | 1104/10016 [3:57:05<31:53:51, 12.89s/it, avr_loss=0.026] 
steps:  11%|█         | 1105/10016 [3:57:17<31:53:38, 12.89s/it, avr_loss=0.026]
steps:  11%|█         | 1105/10016 [3:57:17<31:53:38, 12.89s/it, avr_loss=0.026]
steps:  11%|█         | 1106/10016 [3:57:30<31:53:25, 12.89s/it, avr_loss=0.026]
steps:  11%|█         | 1106/10016 [3:57:30<31:53:25, 12.89s/it, avr_loss=0.0259]
steps:  11%|█         | 1107/10016 [3:57:43<31:53:12, 12.88s/it, avr_loss=0.0259]
steps:  11%|█         | 1107/10016 [3:57:43<31:53:12, 12.88s/it, avr_loss=0.026] 
steps:  11%|█         | 1108/10016 [3:57:56<31:52:59, 12.88s/it, avr_loss=0.026]
steps:  11%|█         | 1108/10016 [3:57:56<31:52:59, 12.88s/it, avr_loss=0.0261]
steps:  11%|█         | 1109/10016 [3:58:09<31:52:46, 12.88s/it, avr_loss=0.0261]
steps:  11%|█         | 1109/10016 [3:58:09<31:52:46, 12.88s/it, avr_loss=0.0261]
steps:  11%|█         | 1110/10016 [3:58:22<31:52:33, 12.88s/it, avr_loss=0.0261]
steps:  11%|█         | 1110/10016 [3:58:22<31:52:33, 12.88s/it, avr_loss=0.0261]
steps:  11%|█         | 1111/10016 [3:58:35<31:52:20, 12.88s/it, avr_loss=0.0261]
steps:  11%|█         | 1111/10016 [3:58:35<31:52:20, 12.88s/it, avr_loss=0.0261]
steps:  11%|█         | 1112/10016 [3:58:48<31:52:07, 12.88s/it, avr_loss=0.0261]
steps:  11%|█         | 1112/10016 [3:58:48<31:52:07, 12.88s/it, avr_loss=0.0261]
steps:  11%|█         | 1113/10016 [3:59:00<31:51:54, 12.88s/it, avr_loss=0.0261]
steps:  11%|█         | 1113/10016 [3:59:00<31:51:54, 12.88s/it, avr_loss=0.0261]
steps:  11%|█         | 1114/10016 [3:59:13<31:51:41, 12.88s/it, avr_loss=0.0261]
steps:  11%|█         | 1114/10016 [3:59:13<31:51:41, 12.88s/it, avr_loss=0.0261]
steps:  11%|█         | 1115/10016 [3:59:26<31:51:28, 12.88s/it, avr_loss=0.0261]
steps:  11%|█         | 1115/10016 [3:59:26<31:51:28, 12.88s/it, avr_loss=0.026] 
steps:  11%|█         | 1116/10016 [3:59:39<31:51:15, 12.88s/it, avr_loss=0.026]
steps:  11%|█         | 1116/10016 [3:59:39<31:51:15, 12.88s/it, avr_loss=0.0261]
steps:  11%|█         | 1117/10016 [3:59:52<31:51:02, 12.88s/it, avr_loss=0.0261]
steps:  11%|█         | 1117/10016 [3:59:52<31:51:02, 12.88s/it, avr_loss=0.0261]
steps:  11%|█         | 1118/10016 [4:00:05<31:50:49, 12.88s/it, avr_loss=0.0261]
steps:  11%|█         | 1118/10016 [4:00:05<31:50:49, 12.88s/it, avr_loss=0.0261]
steps:  11%|█         | 1119/10016 [4:00:18<31:50:36, 12.88s/it, avr_loss=0.0261]
steps:  11%|█         | 1119/10016 [4:00:18<31:50:36, 12.88s/it, avr_loss=0.0262]
steps:  11%|█         | 1120/10016 [4:00:31<31:50:23, 12.88s/it, avr_loss=0.0262]
steps:  11%|█         | 1120/10016 [4:00:31<31:50:23, 12.88s/it, avr_loss=0.0262]
steps:  11%|█         | 1121/10016 [4:00:43<31:50:10, 12.88s/it, avr_loss=0.0262]
steps:  11%|█         | 1121/10016 [4:00:43<31:50:10, 12.88s/it, avr_loss=0.0262]
steps:  11%|█         | 1122/10016 [4:00:56<31:49:57, 12.88s/it, avr_loss=0.0262]
steps:  11%|█         | 1122/10016 [4:00:56<31:49:57, 12.88s/it, avr_loss=0.0262]
steps:  11%|█         | 1123/10016 [4:01:09<31:49:44, 12.88s/it, avr_loss=0.0262]
steps:  11%|█         | 1123/10016 [4:01:09<31:49:44, 12.88s/it, avr_loss=0.0262]
steps:  11%|█         | 1124/10016 [4:01:22<31:49:32, 12.88s/it, avr_loss=0.0262]
steps:  11%|█         | 1124/10016 [4:01:22<31:49:32, 12.88s/it, avr_loss=0.0262]
steps:  11%|█         | 1125/10016 [4:01:35<31:49:19, 12.88s/it, avr_loss=0.0262]
steps:  11%|█         | 1125/10016 [4:01:35<31:49:19, 12.88s/it, avr_loss=0.0262]
steps:  11%|█         | 1126/10016 [4:01:48<31:49:06, 12.88s/it, avr_loss=0.0262]
steps:  11%|█         | 1126/10016 [4:01:48<31:49:06, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1127/10016 [4:02:01<31:48:53, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1127/10016 [4:02:01<31:48:53, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1128/10016 [4:02:14<31:48:40, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1128/10016 [4:02:14<31:48:40, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1129/10016 [4:02:26<31:48:27, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1129/10016 [4:02:26<31:48:27, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1130/10016 [4:02:39<31:48:14, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1130/10016 [4:02:39<31:48:14, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1131/10016 [4:02:52<31:48:01, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1131/10016 [4:02:52<31:48:01, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1132/10016 [4:03:05<31:47:48, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1132/10016 [4:03:05<31:47:48, 12.88s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1133/10016 [4:03:18<31:47:35, 12.88s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1133/10016 [4:03:18<31:47:35, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1134/10016 [4:03:31<31:47:22, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1134/10016 [4:03:31<31:47:22, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1135/10016 [4:03:44<31:47:09, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1135/10016 [4:03:44<31:47:09, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1136/10016 [4:03:57<31:46:56, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1136/10016 [4:03:57<31:46:56, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1137/10016 [4:04:09<31:46:43, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1137/10016 [4:04:09<31:46:43, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1138/10016 [4:04:22<31:46:30, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1138/10016 [4:04:22<31:46:30, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1139/10016 [4:04:35<31:46:17, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1139/10016 [4:04:35<31:46:17, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1140/10016 [4:04:48<31:46:04, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1140/10016 [4:04:48<31:46:04, 12.88s/it, avr_loss=0.0261]
steps:  11%|█▏        | 1141/10016 [4:05:01<31:45:51, 12.88s/it, avr_loss=0.0261]
steps:  11%|█▏        | 1141/10016 [4:05:01<31:45:51, 12.88s/it, avr_loss=0.0261]
steps:  11%|█▏        | 1142/10016 [4:05:14<31:45:38, 12.88s/it, avr_loss=0.0261]
steps:  11%|█▏        | 1142/10016 [4:05:14<31:45:38, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1143/10016 [4:05:27<31:45:25, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1143/10016 [4:05:27<31:45:25, 12.88s/it, avr_loss=0.0261]
steps:  11%|█▏        | 1144/10016 [4:05:40<31:45:12, 12.88s/it, avr_loss=0.0261]
steps:  11%|█▏        | 1144/10016 [4:05:40<31:45:12, 12.88s/it, avr_loss=0.0261]
steps:  11%|█▏        | 1145/10016 [4:05:52<31:44:59, 12.88s/it, avr_loss=0.0261]
steps:  11%|█▏        | 1145/10016 [4:05:52<31:44:59, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1146/10016 [4:06:05<31:44:46, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1146/10016 [4:06:05<31:44:46, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1147/10016 [4:06:18<31:44:33, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1147/10016 [4:06:18<31:44:33, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1148/10016 [4:06:31<31:44:20, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1148/10016 [4:06:31<31:44:20, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1149/10016 [4:06:44<31:44:08, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1149/10016 [4:06:44<31:44:08, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1150/10016 [4:06:57<31:43:55, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1150/10016 [4:06:57<31:43:55, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1151/10016 [4:07:10<31:43:42, 12.88s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1151/10016 [4:07:10<31:43:42, 12.88s/it, avr_loss=0.0262]
steps:  12%|█▏        | 1152/10016 [4:07:23<31:43:29, 12.88s/it, avr_loss=0.0262]
steps:  12%|█▏        | 1152/10016 [4:07:23<31:43:29, 12.88s/it, avr_loss=0.0262]
steps:  12%|█▏        | 1153/10016 [4:07:35<31:43:16, 12.88s/it, avr_loss=0.0262]
steps:  12%|█▏        | 1153/10016 [4:07:35<31:43:16, 12.88s/it, avr_loss=0.0262]
steps:  12%|█▏        | 1154/10016 [4:07:48<31:43:03, 12.88s/it, avr_loss=0.0262]
steps:  12%|█▏        | 1154/10016 [4:07:48<31:43:03, 12.88s/it, avr_loss=0.0262]
steps:  12%|█▏        | 1155/10016 [4:08:01<31:42:50, 12.88s/it, avr_loss=0.0262]
steps:  12%|█▏        | 1155/10016 [4:08:01<31:42:50, 12.88s/it, avr_loss=0.0262]
steps:  12%|█▏        | 1156/10016 [4:08:14<31:42:37, 12.88s/it, avr_loss=0.0262]
steps:  12%|█▏        | 1156/10016 [4:08:14<31:42:37, 12.88s/it, avr_loss=0.0262]
steps:  12%|█▏        | 1157/10016 [4:08:27<31:42:24, 12.88s/it, avr_loss=0.0262]
steps:  12%|█▏        | 1157/10016 [4:08:27<31:42:24, 12.88s/it, avr_loss=0.0262]
steps:  12%|█▏        | 1158/10016 [4:08:40<31:42:11, 12.88s/it, avr_loss=0.0262]
steps:  12%|█▏        | 1158/10016 [4:08:40<31:42:11, 12.88s/it, avr_loss=0.0262]
steps:  12%|█▏        | 1159/10016 [4:08:53<31:41:58, 12.88s/it, avr_loss=0.0262]
steps:  12%|█▏        | 1159/10016 [4:08:53<31:41:58, 12.88s/it, avr_loss=0.0262]
steps:  12%|█▏        | 1160/10016 [4:09:06<31:41:45, 12.88s/it, avr_loss=0.0262]
steps:  12%|█▏        | 1160/10016 [4:09:06<31:41:45, 12.88s/it, avr_loss=0.0262]
steps:  12%|█▏        | 1161/10016 [4:09:18<31:41:32, 12.88s/it, avr_loss=0.0262]
steps:  12%|█▏        | 1161/10016 [4:09:18<31:41:32, 12.88s/it, avr_loss=0.0262]
steps:  12%|█▏        | 1162/10016 [4:09:31<31:41:19, 12.88s/it, avr_loss=0.0262]
steps:  12%|█▏        | 1162/10016 [4:09:31<31:41:19, 12.88s/it, avr_loss=0.0262]
steps:  12%|█▏        | 1163/10016 [4:09:44<31:41:06, 12.88s/it, avr_loss=0.0262]
steps:  12%|█▏        | 1163/10016 [4:09:44<31:41:06, 12.88s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1164/10016 [4:09:57<31:40:53, 12.88s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1164/10016 [4:09:57<31:40:53, 12.88s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1165/10016 [4:10:10<31:40:40, 12.88s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1165/10016 [4:10:10<31:40:40, 12.88s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1166/10016 [4:10:23<31:40:27, 12.88s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1166/10016 [4:10:23<31:40:27, 12.88s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1167/10016 [4:10:36<31:40:14, 12.88s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1167/10016 [4:10:36<31:40:14, 12.88s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1168/10016 [4:10:49<31:40:01, 12.88s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1168/10016 [4:10:49<31:40:01, 12.88s/it, avr_loss=0.026] 
steps:  12%|█▏        | 1169/10016 [4:11:01<31:39:48, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1169/10016 [4:11:01<31:39:48, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1170/10016 [4:11:14<31:39:36, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1170/10016 [4:11:14<31:39:36, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1171/10016 [4:11:27<31:39:23, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1171/10016 [4:11:27<31:39:23, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1172/10016 [4:11:40<31:39:10, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1172/10016 [4:11:40<31:39:10, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1173/10016 [4:11:53<31:38:57, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1173/10016 [4:11:53<31:38:57, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1174/10016 [4:12:06<31:38:44, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1174/10016 [4:12:06<31:38:44, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1175/10016 [4:12:19<31:38:31, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1175/10016 [4:12:19<31:38:31, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1176/10016 [4:12:32<31:38:18, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1176/10016 [4:12:32<31:38:18, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1177/10016 [4:12:44<31:38:05, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1177/10016 [4:12:44<31:38:05, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1178/10016 [4:12:57<31:37:52, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1178/10016 [4:12:57<31:37:52, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1179/10016 [4:13:10<31:37:39, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1179/10016 [4:13:10<31:37:39, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1180/10016 [4:13:23<31:37:26, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1180/10016 [4:13:23<31:37:26, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1181/10016 [4:13:36<31:37:13, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1181/10016 [4:13:36<31:37:13, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1182/10016 [4:13:49<31:37:00, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1182/10016 [4:13:49<31:37:00, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1183/10016 [4:14:02<31:36:47, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1183/10016 [4:14:02<31:36:47, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1184/10016 [4:14:15<31:36:34, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1184/10016 [4:14:15<31:36:34, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1185/10016 [4:14:27<31:36:21, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1185/10016 [4:14:27<31:36:21, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1186/10016 [4:14:40<31:36:08, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1186/10016 [4:14:40<31:36:08, 12.88s/it, avr_loss=0.026] 
steps:  12%|█▏        | 1187/10016 [4:14:53<31:35:55, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1187/10016 [4:14:53<31:35:55, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1188/10016 [4:15:06<31:35:42, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1188/10016 [4:15:06<31:35:42, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1189/10016 [4:15:19<31:35:29, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1189/10016 [4:15:19<31:35:29, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1190/10016 [4:15:32<31:35:16, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1190/10016 [4:15:32<31:35:16, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1191/10016 [4:15:45<31:35:03, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1191/10016 [4:15:45<31:35:03, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1192/10016 [4:15:58<31:34:50, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1192/10016 [4:15:58<31:34:50, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1193/10016 [4:16:10<31:34:38, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1193/10016 [4:16:10<31:34:38, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1194/10016 [4:16:23<31:34:25, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1194/10016 [4:16:23<31:34:25, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1195/10016 [4:16:36<31:34:12, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1195/10016 [4:16:36<31:34:12, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1196/10016 [4:16:49<31:33:59, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1196/10016 [4:16:49<31:33:59, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1197/10016 [4:17:02<31:33:46, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1197/10016 [4:17:02<31:33:46, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1198/10016 [4:17:15<31:33:33, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1198/10016 [4:17:15<31:33:33, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1199/10016 [4:17:28<31:33:20, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1199/10016 [4:17:28<31:33:20, 12.88s/it, avr_loss=0.026] 
steps:  12%|█▏        | 1200/10016 [4:17:41<31:33:07, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1200/10016 [4:17:41<31:33:07, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1201/10016 [4:17:53<31:32:54, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1201/10016 [4:17:53<31:32:54, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1202/10016 [4:18:06<31:32:41, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1202/10016 [4:18:06<31:32:41, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1203/10016 [4:18:19<31:32:28, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1203/10016 [4:18:19<31:32:28, 12.88s/it, avr_loss=0.026] 
steps:  12%|█▏        | 1204/10016 [4:18:32<31:32:15, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1204/10016 [4:18:32<31:32:15, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1205/10016 [4:18:45<31:32:02, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1205/10016 [4:18:45<31:32:02, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1206/10016 [4:18:58<31:31:49, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1206/10016 [4:18:58<31:31:49, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1207/10016 [4:19:11<31:31:36, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1207/10016 [4:19:11<31:31:36, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1208/10016 [4:19:24<31:31:23, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1208/10016 [4:19:24<31:31:23, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1209/10016 [4:19:37<31:31:11, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1209/10016 [4:19:37<31:31:11, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1210/10016 [4:19:49<31:30:59, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1210/10016 [4:19:49<31:30:59, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1211/10016 [4:20:02<31:30:46, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1211/10016 [4:20:02<31:30:46, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1212/10016 [4:20:15<31:30:33, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1212/10016 [4:20:15<31:30:33, 12.88s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1213/10016 [4:20:28<31:30:20, 12.88s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1213/10016 [4:20:28<31:30:20, 12.88s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1214/10016 [4:20:41<31:30:07, 12.88s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1214/10016 [4:20:41<31:30:07, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1215/10016 [4:20:54<31:29:54, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1215/10016 [4:20:54<31:29:54, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1216/10016 [4:21:07<31:29:42, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1216/10016 [4:21:07<31:29:42, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1217/10016 [4:21:20<31:29:29, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1217/10016 [4:21:20<31:29:29, 12.88s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1218/10016 [4:21:33<31:29:16, 12.88s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1218/10016 [4:21:33<31:29:16, 12.88s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1219/10016 [4:21:46<31:29:03, 12.88s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1219/10016 [4:21:46<31:29:03, 12.88s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1220/10016 [4:21:58<31:28:50, 12.88s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1220/10016 [4:21:58<31:28:50, 12.88s/it, avr_loss=0.0257]
steps:  12%|█▏        | 1221/10016 [4:22:11<31:28:38, 12.88s/it, avr_loss=0.0257]
steps:  12%|█▏        | 1221/10016 [4:22:11<31:28:38, 12.88s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1222/10016 [4:22:24<31:28:25, 12.88s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1222/10016 [4:22:24<31:28:25, 12.88s/it, avr_loss=0.0257]
steps:  12%|█▏        | 1223/10016 [4:22:37<31:28:12, 12.88s/it, avr_loss=0.0257]
steps:  12%|█▏        | 1223/10016 [4:22:37<31:28:12, 12.88s/it, avr_loss=0.0257]
steps:  12%|█▏        | 1224/10016 [4:22:50<31:27:59, 12.88s/it, avr_loss=0.0257]
steps:  12%|█▏        | 1224/10016 [4:22:50<31:27:59, 12.88s/it, avr_loss=0.0257]
steps:  12%|█▏        | 1225/10016 [4:23:03<31:27:47, 12.88s/it, avr_loss=0.0257]
steps:  12%|█▏        | 1225/10016 [4:23:03<31:27:47, 12.88s/it, avr_loss=0.0257]
steps:  12%|█▏        | 1226/10016 [4:23:16<31:27:34, 12.88s/it, avr_loss=0.0257]
steps:  12%|█▏        | 1226/10016 [4:23:16<31:27:34, 12.88s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1227/10016 [4:23:29<31:27:21, 12.88s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1227/10016 [4:23:29<31:27:21, 12.88s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1228/10016 [4:23:42<31:27:08, 12.88s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1228/10016 [4:23:42<31:27:08, 12.88s/it, avr_loss=0.0257]
steps:  12%|█▏        | 1229/10016 [4:23:54<31:26:55, 12.88s/it, avr_loss=0.0257]
steps:  12%|█▏        | 1229/10016 [4:23:54<31:26:55, 12.88s/it, avr_loss=0.0257]
steps:  12%|█▏        | 1230/10016 [4:24:07<31:26:42, 12.88s/it, avr_loss=0.0257]
steps:  12%|█▏        | 1230/10016 [4:24:07<31:26:42, 12.88s/it, avr_loss=0.0257]
steps:  12%|█▏        | 1231/10016 [4:24:20<31:26:29, 12.88s/it, avr_loss=0.0257]
steps:  12%|█▏        | 1231/10016 [4:24:20<31:26:29, 12.88s/it, avr_loss=0.0257]
steps:  12%|█▏        | 1232/10016 [4:24:33<31:26:16, 12.88s/it, avr_loss=0.0257]
steps:  12%|█▏        | 1232/10016 [4:24:33<31:26:16, 12.88s/it, avr_loss=0.0257]
steps:  12%|█▏        | 1233/10016 [4:24:46<31:26:04, 12.88s/it, avr_loss=0.0257]
steps:  12%|█▏        | 1233/10016 [4:24:46<31:26:04, 12.88s/it, avr_loss=0.0257]
steps:  12%|█▏        | 1234/10016 [4:24:59<31:25:51, 12.88s/it, avr_loss=0.0257]
steps:  12%|█▏        | 1234/10016 [4:24:59<31:25:51, 12.88s/it, avr_loss=0.0257]
steps:  12%|█▏        | 1235/10016 [4:25:12<31:25:38, 12.88s/it, avr_loss=0.0257]
steps:  12%|█▏        | 1235/10016 [4:25:12<31:25:38, 12.88s/it, avr_loss=0.0257]
steps:  12%|█▏        | 1236/10016 [4:25:25<31:25:25, 12.88s/it, avr_loss=0.0257]
steps:  12%|█▏        | 1236/10016 [4:25:25<31:25:25, 12.88s/it, avr_loss=0.026] 
steps:  12%|█▏        | 1237/10016 [4:25:38<31:25:12, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1237/10016 [4:25:38<31:25:12, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1238/10016 [4:25:50<31:24:59, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1238/10016 [4:25:50<31:24:59, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1239/10016 [4:26:03<31:24:46, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1239/10016 [4:26:03<31:24:46, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1240/10016 [4:26:16<31:24:34, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1240/10016 [4:26:16<31:24:34, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1241/10016 [4:26:29<31:24:21, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1241/10016 [4:26:29<31:24:21, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1242/10016 [4:26:42<31:24:08, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1242/10016 [4:26:42<31:24:08, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1243/10016 [4:26:55<31:23:55, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1243/10016 [4:26:55<31:23:55, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1244/10016 [4:27:08<31:23:42, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1244/10016 [4:27:08<31:23:42, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1245/10016 [4:27:21<31:23:29, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1245/10016 [4:27:21<31:23:29, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1246/10016 [4:27:34<31:23:16, 12.88s/it, avr_loss=0.026]
steps:  12%|█▏        | 1246/10016 [4:27:34<31:23:16, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1247/10016 [4:27:46<31:23:04, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1247/10016 [4:27:46<31:23:04, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1248/10016 [4:27:59<31:22:51, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1248/10016 [4:27:59<31:22:51, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1249/10016 [4:28:12<31:22:38, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1249/10016 [4:28:12<31:22:38, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1250/10016 [4:28:25<31:22:25, 12.88s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1250/10016 [4:28:25<31:22:25, 12.88s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1251/10016 [4:28:38<31:22:12, 12.88s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1251/10016 [4:28:38<31:22:12, 12.88s/it, avr_loss=0.0258]
steps:  12%|█▎        | 1252/10016 [4:28:51<31:21:59, 12.88s/it, avr_loss=0.0258]
steps:  12%|█▎        | 1252/10016 [4:28:51<31:21:59, 12.88s/it, avr_loss=0.0258]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/loss-curve-retrain/train_lora-000002.safetensors

epoch 3/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3

steps:  13%|█▎        | 1253/10016 [4:29:06<31:22:00, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1253/10016 [4:29:06<31:22:00, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1254/10016 [4:29:19<31:21:47, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1254/10016 [4:29:19<31:21:47, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1255/10016 [4:29:31<31:21:34, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1255/10016 [4:29:31<31:21:34, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1256/10016 [4:29:44<31:21:21, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1256/10016 [4:29:44<31:21:21, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1257/10016 [4:29:57<31:21:08, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1257/10016 [4:29:57<31:21:08, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1258/10016 [4:30:10<31:20:55, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1258/10016 [4:30:10<31:20:55, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1259/10016 [4:30:23<31:20:43, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1259/10016 [4:30:23<31:20:43, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1260/10016 [4:30:36<31:20:30, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1260/10016 [4:30:36<31:20:30, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1261/10016 [4:30:49<31:20:17, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1261/10016 [4:30:49<31:20:17, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1262/10016 [4:31:02<31:20:04, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1262/10016 [4:31:02<31:20:04, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1263/10016 [4:31:15<31:19:51, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1263/10016 [4:31:15<31:19:51, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1264/10016 [4:31:28<31:19:39, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1264/10016 [4:31:28<31:19:39, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1265/10016 [4:31:40<31:19:26, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1265/10016 [4:31:40<31:19:26, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1266/10016 [4:31:53<31:19:13, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1266/10016 [4:31:53<31:19:13, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1267/10016 [4:32:06<31:19:00, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1267/10016 [4:32:06<31:19:00, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1268/10016 [4:32:19<31:18:48, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1268/10016 [4:32:19<31:18:48, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1269/10016 [4:32:32<31:18:35, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1269/10016 [4:32:32<31:18:35, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1270/10016 [4:32:45<31:18:22, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1270/10016 [4:32:45<31:18:22, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1271/10016 [4:32:58<31:18:10, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1271/10016 [4:32:58<31:18:10, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1272/10016 [4:33:11<31:17:57, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1272/10016 [4:33:11<31:17:57, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1273/10016 [4:33:24<31:17:45, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1273/10016 [4:33:24<31:17:45, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1274/10016 [4:33:37<31:17:32, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1274/10016 [4:33:37<31:17:32, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1275/10016 [4:33:50<31:17:19, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1275/10016 [4:33:50<31:17:19, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1276/10016 [4:34:03<31:17:07, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1276/10016 [4:34:03<31:17:07, 12.89s/it, avr_loss=0.0259]
steps:  13%|█▎        | 1277/10016 [4:34:15<31:16:54, 12.89s/it, avr_loss=0.0259]
steps:  13%|█▎        | 1277/10016 [4:34:15<31:16:54, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1278/10016 [4:34:28<31:16:41, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1278/10016 [4:34:28<31:16:41, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1279/10016 [4:34:41<31:16:29, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1279/10016 [4:34:41<31:16:29, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1280/10016 [4:34:54<31:16:16, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1280/10016 [4:34:54<31:16:16, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1281/10016 [4:35:07<31:16:03, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1281/10016 [4:35:07<31:16:03, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1282/10016 [4:35:20<31:15:50, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1282/10016 [4:35:20<31:15:50, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1283/10016 [4:35:33<31:15:37, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1283/10016 [4:35:33<31:15:37, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1284/10016 [4:35:46<31:15:25, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1284/10016 [4:35:46<31:15:25, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1285/10016 [4:35:59<31:15:12, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1285/10016 [4:35:59<31:15:12, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1286/10016 [4:36:12<31:14:59, 12.89s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1286/10016 [4:36:12<31:14:59, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1287/10016 [4:36:25<31:14:46, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1287/10016 [4:36:25<31:14:46, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1288/10016 [4:36:37<31:14:34, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1288/10016 [4:36:37<31:14:34, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1289/10016 [4:36:50<31:14:21, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1289/10016 [4:36:50<31:14:21, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1290/10016 [4:37:03<31:14:08, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1290/10016 [4:37:03<31:14:08, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1291/10016 [4:37:16<31:13:55, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1291/10016 [4:37:16<31:13:55, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1292/10016 [4:37:29<31:13:43, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1292/10016 [4:37:29<31:13:43, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1293/10016 [4:37:42<31:13:30, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1293/10016 [4:37:42<31:13:30, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1294/10016 [4:37:55<31:13:17, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1294/10016 [4:37:55<31:13:17, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1295/10016 [4:38:08<31:13:04, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1295/10016 [4:38:08<31:13:04, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1296/10016 [4:38:21<31:12:51, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1296/10016 [4:38:21<31:12:51, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1297/10016 [4:38:34<31:12:38, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1297/10016 [4:38:34<31:12:38, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1298/10016 [4:38:46<31:12:26, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1298/10016 [4:38:46<31:12:26, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1299/10016 [4:38:59<31:12:13, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1299/10016 [4:38:59<31:12:13, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1300/10016 [4:39:12<31:12:00, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1300/10016 [4:39:12<31:12:00, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1301/10016 [4:39:25<31:11:47, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1301/10016 [4:39:25<31:11:47, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1302/10016 [4:39:38<31:11:34, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1302/10016 [4:39:38<31:11:34, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1303/10016 [4:39:51<31:11:22, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1303/10016 [4:39:51<31:11:22, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1304/10016 [4:40:04<31:11:09, 12.89s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1304/10016 [4:40:04<31:11:09, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1305/10016 [4:40:17<31:10:56, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1305/10016 [4:40:17<31:10:56, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1306/10016 [4:40:30<31:10:43, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1306/10016 [4:40:30<31:10:43, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1307/10016 [4:40:43<31:10:30, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1307/10016 [4:40:43<31:10:30, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1308/10016 [4:40:55<31:10:18, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1308/10016 [4:40:55<31:10:18, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1309/10016 [4:41:08<31:10:05, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1309/10016 [4:41:08<31:10:05, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1310/10016 [4:41:21<31:09:52, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1310/10016 [4:41:21<31:09:52, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1311/10016 [4:41:34<31:09:39, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1311/10016 [4:41:34<31:09:39, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1312/10016 [4:41:47<31:09:26, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1312/10016 [4:41:47<31:09:26, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1313/10016 [4:42:00<31:09:14, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1313/10016 [4:42:00<31:09:14, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1314/10016 [4:42:13<31:09:01, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1314/10016 [4:42:13<31:09:01, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1315/10016 [4:42:26<31:08:48, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1315/10016 [4:42:26<31:08:48, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1316/10016 [4:42:39<31:08:35, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1316/10016 [4:42:39<31:08:35, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1317/10016 [4:42:52<31:08:22, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1317/10016 [4:42:52<31:08:22, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1318/10016 [4:43:05<31:08:11, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1318/10016 [4:43:05<31:08:11, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1319/10016 [4:43:17<31:07:58, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1319/10016 [4:43:17<31:07:58, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1320/10016 [4:43:30<31:07:45, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1320/10016 [4:43:30<31:07:45, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1321/10016 [4:43:43<31:07:32, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1321/10016 [4:43:43<31:07:32, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1322/10016 [4:43:56<31:07:19, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1322/10016 [4:43:56<31:07:19, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1323/10016 [4:44:09<31:07:06, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1323/10016 [4:44:09<31:07:06, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1324/10016 [4:44:22<31:06:53, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1324/10016 [4:44:22<31:06:53, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1325/10016 [4:44:35<31:06:40, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1325/10016 [4:44:35<31:06:40, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1326/10016 [4:44:48<31:06:27, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1326/10016 [4:44:48<31:06:27, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1327/10016 [4:45:00<31:06:14, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1327/10016 [4:45:00<31:06:14, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1328/10016 [4:45:13<31:06:01, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1328/10016 [4:45:13<31:06:01, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1329/10016 [4:45:26<31:05:48, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1329/10016 [4:45:26<31:05:48, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1330/10016 [4:45:39<31:05:35, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1330/10016 [4:45:39<31:05:35, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1331/10016 [4:45:52<31:05:23, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1331/10016 [4:45:52<31:05:23, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1332/10016 [4:46:05<31:05:10, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1332/10016 [4:46:05<31:05:10, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1333/10016 [4:46:18<31:04:57, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1333/10016 [4:46:18<31:04:57, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1334/10016 [4:46:31<31:04:44, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1334/10016 [4:46:31<31:04:44, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1335/10016 [4:46:44<31:04:31, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1335/10016 [4:46:44<31:04:31, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1336/10016 [4:46:56<31:04:18, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1336/10016 [4:46:56<31:04:18, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1337/10016 [4:47:09<31:04:05, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1337/10016 [4:47:09<31:04:05, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1338/10016 [4:47:22<31:03:52, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1338/10016 [4:47:22<31:03:52, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1339/10016 [4:47:35<31:03:39, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1339/10016 [4:47:35<31:03:39, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1340/10016 [4:47:48<31:03:26, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1340/10016 [4:47:48<31:03:26, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1341/10016 [4:48:01<31:03:14, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1341/10016 [4:48:01<31:03:14, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1342/10016 [4:48:14<31:03:01, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1342/10016 [4:48:14<31:03:01, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1343/10016 [4:48:27<31:02:48, 12.89s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1343/10016 [4:48:27<31:02:48, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1344/10016 [4:48:39<31:02:35, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1344/10016 [4:48:39<31:02:35, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1345/10016 [4:48:52<31:02:22, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1345/10016 [4:48:52<31:02:22, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1346/10016 [4:49:05<31:02:09, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1346/10016 [4:49:05<31:02:09, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1347/10016 [4:49:18<31:01:56, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1347/10016 [4:49:18<31:01:56, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1348/10016 [4:49:31<31:01:43, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1348/10016 [4:49:31<31:01:43, 12.89s/it, avr_loss=0.0254]
steps:  13%|█▎        | 1349/10016 [4:49:44<31:01:30, 12.89s/it, avr_loss=0.0254]
steps:  13%|█▎        | 1349/10016 [4:49:44<31:01:30, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1350/10016 [4:49:57<31:01:17, 12.89s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1350/10016 [4:49:57<31:01:17, 12.89s/it, avr_loss=0.0254]
steps:  13%|█▎        | 1351/10016 [4:50:10<31:01:04, 12.89s/it, avr_loss=0.0254]
steps:  13%|█▎        | 1351/10016 [4:50:10<31:01:04, 12.89s/it, avr_loss=0.0254]
steps:  13%|█▎        | 1352/10016 [4:50:23<31:00:51, 12.89s/it, avr_loss=0.0254]
steps:  13%|█▎        | 1352/10016 [4:50:23<31:00:51, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1353/10016 [4:50:35<31:00:38, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1353/10016 [4:50:35<31:00:38, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1354/10016 [4:50:48<31:00:26, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1354/10016 [4:50:48<31:00:26, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1355/10016 [4:51:01<31:00:13, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1355/10016 [4:51:01<31:00:13, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1356/10016 [4:51:14<31:00:00, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1356/10016 [4:51:14<31:00:00, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1357/10016 [4:51:27<30:59:47, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1357/10016 [4:51:27<30:59:47, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1358/10016 [4:51:40<30:59:34, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1358/10016 [4:51:40<30:59:34, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1359/10016 [4:51:53<30:59:21, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1359/10016 [4:51:53<30:59:21, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1360/10016 [4:52:06<30:59:08, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1360/10016 [4:52:06<30:59:08, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1361/10016 [4:52:19<30:58:55, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1361/10016 [4:52:19<30:58:55, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1362/10016 [4:52:31<30:58:42, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1362/10016 [4:52:31<30:58:42, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1363/10016 [4:52:44<30:58:29, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1363/10016 [4:52:44<30:58:29, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1364/10016 [4:52:57<30:58:16, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1364/10016 [4:52:57<30:58:17, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1365/10016 [4:53:10<30:58:04, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1365/10016 [4:53:10<30:58:04, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1366/10016 [4:53:23<30:57:51, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1366/10016 [4:53:23<30:57:51, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1367/10016 [4:53:36<30:57:38, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1367/10016 [4:53:36<30:57:38, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1368/10016 [4:53:49<30:57:25, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1368/10016 [4:53:49<30:57:25, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1369/10016 [4:54:02<30:57:12, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1369/10016 [4:54:02<30:57:12, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1370/10016 [4:54:14<30:56:59, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1370/10016 [4:54:14<30:56:59, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1371/10016 [4:54:27<30:56:46, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1371/10016 [4:54:27<30:56:46, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1372/10016 [4:54:40<30:56:33, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1372/10016 [4:54:40<30:56:33, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1373/10016 [4:54:53<30:56:20, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1373/10016 [4:54:53<30:56:20, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1374/10016 [4:55:06<30:56:07, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1374/10016 [4:55:06<30:56:07, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▎        | 1375/10016 [4:55:19<30:55:54, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▎        | 1375/10016 [4:55:19<30:55:54, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▎        | 1376/10016 [4:55:32<30:55:42, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▎        | 1376/10016 [4:55:32<30:55:42, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▎        | 1377/10016 [4:55:45<30:55:29, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▎        | 1377/10016 [4:55:45<30:55:29, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1378/10016 [4:55:58<30:55:16, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1378/10016 [4:55:58<30:55:16, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1379/10016 [4:56:10<30:55:03, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1379/10016 [4:56:10<30:55:03, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1380/10016 [4:56:23<30:54:50, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1380/10016 [4:56:23<30:54:50, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1381/10016 [4:56:36<30:54:37, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1381/10016 [4:56:36<30:54:37, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1382/10016 [4:56:49<30:54:24, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1382/10016 [4:56:49<30:54:24, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1383/10016 [4:57:02<30:54:11, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1383/10016 [4:57:02<30:54:11, 12.89s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1384/10016 [4:57:15<30:53:58, 12.89s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1384/10016 [4:57:15<30:53:58, 12.89s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1385/10016 [4:57:28<30:53:45, 12.89s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1385/10016 [4:57:28<30:53:45, 12.89s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1386/10016 [4:57:41<30:53:32, 12.89s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1386/10016 [4:57:41<30:53:32, 12.89s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1387/10016 [4:57:53<30:53:20, 12.89s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1387/10016 [4:57:53<30:53:20, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1388/10016 [4:58:06<30:53:07, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1388/10016 [4:58:06<30:53:07, 12.89s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1389/10016 [4:58:19<30:52:54, 12.89s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1389/10016 [4:58:19<30:52:54, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1390/10016 [4:58:32<30:52:41, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1390/10016 [4:58:32<30:52:41, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1391/10016 [4:58:45<30:52:28, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1391/10016 [4:58:45<30:52:28, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1392/10016 [4:58:58<30:52:15, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1392/10016 [4:58:58<30:52:15, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1393/10016 [4:59:11<30:52:02, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1393/10016 [4:59:11<30:52:02, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1394/10016 [4:59:24<30:51:49, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1394/10016 [4:59:24<30:51:49, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1395/10016 [4:59:37<30:51:36, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1395/10016 [4:59:37<30:51:36, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1396/10016 [4:59:49<30:51:23, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1396/10016 [4:59:49<30:51:23, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1397/10016 [5:00:02<30:51:10, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1397/10016 [5:00:02<30:51:10, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1398/10016 [5:00:15<30:50:57, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1398/10016 [5:00:15<30:50:57, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1399/10016 [5:00:28<30:50:45, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1399/10016 [5:00:28<30:50:45, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1400/10016 [5:00:41<30:50:32, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1400/10016 [5:00:41<30:50:32, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1401/10016 [5:00:54<30:50:19, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1401/10016 [5:00:54<30:50:19, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1402/10016 [5:01:07<30:50:06, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1402/10016 [5:01:07<30:50:06, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1403/10016 [5:01:20<30:49:53, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1403/10016 [5:01:20<30:49:53, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1404/10016 [5:01:32<30:49:40, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1404/10016 [5:01:32<30:49:40, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1405/10016 [5:01:45<30:49:27, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1405/10016 [5:01:45<30:49:27, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1406/10016 [5:01:58<30:49:14, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1406/10016 [5:01:58<30:49:14, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1407/10016 [5:02:11<30:49:01, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1407/10016 [5:02:11<30:49:01, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1408/10016 [5:02:24<30:48:48, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1408/10016 [5:02:24<30:48:48, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1409/10016 [5:02:37<30:48:35, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1409/10016 [5:02:37<30:48:35, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1410/10016 [5:02:50<30:48:22, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1410/10016 [5:02:50<30:48:22, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1411/10016 [5:03:03<30:48:09, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1411/10016 [5:03:03<30:48:09, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1412/10016 [5:03:15<30:47:56, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1412/10016 [5:03:15<30:47:56, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1413/10016 [5:03:28<30:47:44, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1413/10016 [5:03:28<30:47:44, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1414/10016 [5:03:41<30:47:31, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1414/10016 [5:03:41<30:47:31, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1415/10016 [5:03:54<30:47:18, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1415/10016 [5:03:54<30:47:18, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1416/10016 [5:04:07<30:47:05, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1416/10016 [5:04:07<30:47:05, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1417/10016 [5:04:20<30:46:52, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1417/10016 [5:04:20<30:46:52, 12.89s/it, avr_loss=0.0251]
steps:  14%|█▍        | 1418/10016 [5:04:33<30:46:39, 12.89s/it, avr_loss=0.0251]
steps:  14%|█▍        | 1418/10016 [5:04:33<30:46:39, 12.89s/it, avr_loss=0.0251]
steps:  14%|█▍        | 1419/10016 [5:04:46<30:46:26, 12.89s/it, avr_loss=0.0251]
steps:  14%|█▍        | 1419/10016 [5:04:46<30:46:26, 12.89s/it, avr_loss=0.0251]
steps:  14%|█▍        | 1420/10016 [5:04:59<30:46:13, 12.89s/it, avr_loss=0.0251]
steps:  14%|█▍        | 1420/10016 [5:04:59<30:46:13, 12.89s/it, avr_loss=0.025] 
steps:  14%|█▍        | 1421/10016 [5:05:11<30:46:00, 12.89s/it, avr_loss=0.025]
steps:  14%|█▍        | 1421/10016 [5:05:11<30:46:00, 12.89s/it, avr_loss=0.025]
steps:  14%|█▍        | 1422/10016 [5:05:24<30:45:47, 12.89s/it, avr_loss=0.025]
steps:  14%|█▍        | 1422/10016 [5:05:24<30:45:47, 12.89s/it, avr_loss=0.025]
steps:  14%|█▍        | 1423/10016 [5:05:37<30:45:34, 12.89s/it, avr_loss=0.025]
steps:  14%|█▍        | 1423/10016 [5:05:37<30:45:34, 12.89s/it, avr_loss=0.025]
steps:  14%|█▍        | 1424/10016 [5:05:50<30:45:22, 12.89s/it, avr_loss=0.025]
steps:  14%|█▍        | 1424/10016 [5:05:50<30:45:22, 12.89s/it, avr_loss=0.025]
steps:  14%|█▍        | 1425/10016 [5:06:03<30:45:09, 12.89s/it, avr_loss=0.025]
steps:  14%|█▍        | 1425/10016 [5:06:03<30:45:09, 12.89s/it, avr_loss=0.025]
steps:  14%|█▍        | 1426/10016 [5:06:16<30:44:56, 12.89s/it, avr_loss=0.025]
steps:  14%|█▍        | 1426/10016 [5:06:16<30:44:56, 12.89s/it, avr_loss=0.025]
steps:  14%|█▍        | 1427/10016 [5:06:29<30:44:43, 12.89s/it, avr_loss=0.025]
steps:  14%|█▍        | 1427/10016 [5:06:29<30:44:43, 12.89s/it, avr_loss=0.0251]
steps:  14%|█▍        | 1428/10016 [5:06:42<30:44:31, 12.89s/it, avr_loss=0.0251]
steps:  14%|█▍        | 1428/10016 [5:06:42<30:44:31, 12.89s/it, avr_loss=0.0251]
steps:  14%|█▍        | 1429/10016 [5:06:55<30:44:18, 12.89s/it, avr_loss=0.0251]
steps:  14%|█▍        | 1429/10016 [5:06:55<30:44:18, 12.89s/it, avr_loss=0.0251]
steps:  14%|█▍        | 1430/10016 [5:07:08<30:44:05, 12.89s/it, avr_loss=0.0251]
steps:  14%|█▍        | 1430/10016 [5:07:08<30:44:05, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1431/10016 [5:07:20<30:43:52, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1431/10016 [5:07:20<30:43:52, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1432/10016 [5:07:33<30:43:39, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1432/10016 [5:07:33<30:43:39, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1433/10016 [5:07:46<30:43:27, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1433/10016 [5:07:46<30:43:27, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1434/10016 [5:07:59<30:43:14, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1434/10016 [5:07:59<30:43:14, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1435/10016 [5:08:12<30:43:01, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1435/10016 [5:08:12<30:43:01, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1436/10016 [5:08:25<30:42:48, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1436/10016 [5:08:25<30:42:48, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1437/10016 [5:08:38<30:42:35, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1437/10016 [5:08:38<30:42:35, 12.89s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1438/10016 [5:08:51<30:42:22, 12.89s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1438/10016 [5:08:51<30:42:22, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1439/10016 [5:09:04<30:42:10, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1439/10016 [5:09:04<30:42:10, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1440/10016 [5:09:17<30:41:57, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1440/10016 [5:09:17<30:41:57, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1441/10016 [5:09:29<30:41:44, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1441/10016 [5:09:29<30:41:44, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1442/10016 [5:09:42<30:41:31, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1442/10016 [5:09:42<30:41:31, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1443/10016 [5:09:55<30:41:19, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1443/10016 [5:09:55<30:41:19, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1444/10016 [5:10:08<30:41:06, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1444/10016 [5:10:08<30:41:06, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1445/10016 [5:10:21<30:40:53, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1445/10016 [5:10:21<30:40:53, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1446/10016 [5:10:34<30:40:40, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1446/10016 [5:10:34<30:40:40, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1447/10016 [5:10:47<30:40:27, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1447/10016 [5:10:47<30:40:27, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1448/10016 [5:11:00<30:40:14, 12.89s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1448/10016 [5:11:00<30:40:14, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1449/10016 [5:11:13<30:40:02, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1449/10016 [5:11:13<30:40:02, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1450/10016 [5:11:26<30:39:49, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1450/10016 [5:11:26<30:39:49, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1451/10016 [5:11:38<30:39:36, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1451/10016 [5:11:38<30:39:36, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1452/10016 [5:11:51<30:39:23, 12.89s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1452/10016 [5:11:51<30:39:23, 12.89s/it, avr_loss=0.0251]
steps:  15%|█▍        | 1453/10016 [5:12:04<30:39:11, 12.89s/it, avr_loss=0.0251]
steps:  15%|█▍        | 1453/10016 [5:12:04<30:39:11, 12.89s/it, avr_loss=0.0252]
steps:  15%|█▍        | 1454/10016 [5:12:17<30:38:58, 12.89s/it, avr_loss=0.0252]
steps:  15%|█▍        | 1454/10016 [5:12:17<30:38:58, 12.89s/it, avr_loss=0.0252]
steps:  15%|█▍        | 1455/10016 [5:12:30<30:38:45, 12.89s/it, avr_loss=0.0252]
steps:  15%|█▍        | 1455/10016 [5:12:30<30:38:45, 12.89s/it, avr_loss=0.0252]
steps:  15%|█▍        | 1456/10016 [5:12:43<30:38:32, 12.89s/it, avr_loss=0.0252]
steps:  15%|█▍        | 1456/10016 [5:12:43<30:38:32, 12.89s/it, avr_loss=0.0252]
steps:  15%|█▍        | 1457/10016 [5:12:56<30:38:20, 12.89s/it, avr_loss=0.0252]
steps:  15%|█▍        | 1457/10016 [5:12:56<30:38:20, 12.89s/it, avr_loss=0.0252]
steps:  15%|█▍        | 1458/10016 [5:13:09<30:38:07, 12.89s/it, avr_loss=0.0252]
steps:  15%|█▍        | 1458/10016 [5:13:09<30:38:07, 12.89s/it, avr_loss=0.0252]
steps:  15%|█▍        | 1459/10016 [5:13:22<30:37:54, 12.89s/it, avr_loss=0.0252]
steps:  15%|█▍        | 1459/10016 [5:13:22<30:37:54, 12.89s/it, avr_loss=0.0252]
steps:  15%|█▍        | 1460/10016 [5:13:35<30:37:41, 12.89s/it, avr_loss=0.0252]
steps:  15%|█▍        | 1460/10016 [5:13:35<30:37:41, 12.89s/it, avr_loss=0.0252]
steps:  15%|█▍        | 1461/10016 [5:13:47<30:37:28, 12.89s/it, avr_loss=0.0252]
steps:  15%|█▍        | 1461/10016 [5:13:47<30:37:28, 12.89s/it, avr_loss=0.0252]
steps:  15%|█▍        | 1462/10016 [5:14:00<30:37:16, 12.89s/it, avr_loss=0.0252]
steps:  15%|█▍        | 1462/10016 [5:14:00<30:37:16, 12.89s/it, avr_loss=0.0252]
steps:  15%|█▍        | 1463/10016 [5:14:13<30:37:03, 12.89s/it, avr_loss=0.0252]
steps:  15%|█▍        | 1463/10016 [5:14:13<30:37:03, 12.89s/it, avr_loss=0.0251]
steps:  15%|█▍        | 1464/10016 [5:14:26<30:36:50, 12.89s/it, avr_loss=0.0251]
steps:  15%|█▍        | 1464/10016 [5:14:26<30:36:50, 12.89s/it, avr_loss=0.0251]
steps:  15%|█▍        | 1465/10016 [5:14:39<30:36:37, 12.89s/it, avr_loss=0.0251]
steps:  15%|█▍        | 1465/10016 [5:14:39<30:36:37, 12.89s/it, avr_loss=0.0252]
steps:  15%|█▍        | 1466/10016 [5:14:52<30:36:24, 12.89s/it, avr_loss=0.0252]
steps:  15%|█▍        | 1466/10016 [5:14:52<30:36:24, 12.89s/it, avr_loss=0.0252]
steps:  15%|█▍        | 1467/10016 [5:15:05<30:36:11, 12.89s/it, avr_loss=0.0252]
steps:  15%|█▍        | 1467/10016 [5:15:05<30:36:11, 12.89s/it, avr_loss=0.0251]
steps:  15%|█▍        | 1468/10016 [5:15:18<30:35:59, 12.89s/it, avr_loss=0.0251]
steps:  15%|█▍        | 1468/10016 [5:15:18<30:35:59, 12.89s/it, avr_loss=0.0251]
steps:  15%|█▍        | 1469/10016 [5:15:31<30:35:46, 12.89s/it, avr_loss=0.0251]
steps:  15%|█▍        | 1469/10016 [5:15:31<30:35:46, 12.89s/it, avr_loss=0.0252]
steps:  15%|█▍        | 1470/10016 [5:15:44<30:35:33, 12.89s/it, avr_loss=0.0252]
steps:  15%|█▍        | 1470/10016 [5:15:44<30:35:33, 12.89s/it, avr_loss=0.0251]
steps:  15%|█▍        | 1471/10016 [5:15:56<30:35:20, 12.89s/it, avr_loss=0.0251]
steps:  15%|█▍        | 1471/10016 [5:15:56<30:35:20, 12.89s/it, avr_loss=0.0251]
steps:  15%|█▍        | 1472/10016 [5:16:09<30:35:07, 12.89s/it, avr_loss=0.0251]
steps:  15%|█▍        | 1472/10016 [5:16:09<30:35:07, 12.89s/it, avr_loss=0.0251]
steps:  15%|█▍        | 1473/10016 [5:16:22<30:34:55, 12.89s/it, avr_loss=0.0251]
steps:  15%|█▍        | 1473/10016 [5:16:22<30:34:55, 12.89s/it, avr_loss=0.0251]
steps:  15%|█▍        | 1474/10016 [5:16:35<30:34:42, 12.89s/it, avr_loss=0.0251]
steps:  15%|█▍        | 1474/10016 [5:16:35<30:34:42, 12.89s/it, avr_loss=0.0251]
steps:  15%|█▍        | 1475/10016 [5:16:48<30:34:29, 12.89s/it, avr_loss=0.0251]
steps:  15%|█▍        | 1475/10016 [5:16:48<30:34:29, 12.89s/it, avr_loss=0.0249]
steps:  15%|█▍        | 1476/10016 [5:17:01<30:34:16, 12.89s/it, avr_loss=0.0249]
steps:  15%|█▍        | 1476/10016 [5:17:01<30:34:16, 12.89s/it, avr_loss=0.0249]
steps:  15%|█▍        | 1477/10016 [5:17:14<30:34:03, 12.89s/it, avr_loss=0.0249]
steps:  15%|█▍        | 1477/10016 [5:17:14<30:34:03, 12.89s/it, avr_loss=0.0249]
steps:  15%|█▍        | 1478/10016 [5:17:27<30:33:51, 12.89s/it, avr_loss=0.0249]
steps:  15%|█▍        | 1478/10016 [5:17:27<30:33:51, 12.89s/it, avr_loss=0.0249]
steps:  15%|█▍        | 1479/10016 [5:17:40<30:33:38, 12.89s/it, avr_loss=0.0249]
steps:  15%|█▍        | 1479/10016 [5:17:40<30:33:38, 12.89s/it, avr_loss=0.0249]
steps:  15%|█▍        | 1480/10016 [5:17:53<30:33:25, 12.89s/it, avr_loss=0.0249]
steps:  15%|█▍        | 1480/10016 [5:17:53<30:33:25, 12.89s/it, avr_loss=0.0249]
steps:  15%|█▍        | 1481/10016 [5:18:06<30:33:12, 12.89s/it, avr_loss=0.0249]
steps:  15%|█▍        | 1481/10016 [5:18:06<30:33:12, 12.89s/it, avr_loss=0.0249]
steps:  15%|█▍        | 1482/10016 [5:18:18<30:32:59, 12.89s/it, avr_loss=0.0249]
steps:  15%|█▍        | 1482/10016 [5:18:18<30:32:59, 12.89s/it, avr_loss=0.0248]
steps:  15%|█▍        | 1483/10016 [5:18:31<30:32:46, 12.89s/it, avr_loss=0.0248]
steps:  15%|█▍        | 1483/10016 [5:18:31<30:32:46, 12.89s/it, avr_loss=0.0248]
steps:  15%|█▍        | 1484/10016 [5:18:44<30:32:34, 12.89s/it, avr_loss=0.0248]
steps:  15%|█▍        | 1484/10016 [5:18:44<30:32:34, 12.89s/it, avr_loss=0.0248]
steps:  15%|█▍        | 1485/10016 [5:18:57<30:32:21, 12.89s/it, avr_loss=0.0248]
steps:  15%|█▍        | 1485/10016 [5:18:57<30:32:21, 12.89s/it, avr_loss=0.0249]
steps:  15%|█▍        | 1486/10016 [5:19:10<30:32:08, 12.89s/it, avr_loss=0.0249]
steps:  15%|█▍        | 1486/10016 [5:19:10<30:32:08, 12.89s/it, avr_loss=0.0249]
steps:  15%|█▍        | 1487/10016 [5:19:23<30:31:55, 12.89s/it, avr_loss=0.0249]
steps:  15%|█▍        | 1487/10016 [5:19:23<30:31:55, 12.89s/it, avr_loss=0.0249]
steps:  15%|█▍        | 1488/10016 [5:19:36<30:31:42, 12.89s/it, avr_loss=0.0249]
steps:  15%|█▍        | 1488/10016 [5:19:36<30:31:42, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▍        | 1489/10016 [5:19:49<30:31:29, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▍        | 1489/10016 [5:19:49<30:31:29, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▍        | 1490/10016 [5:20:02<30:31:16, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▍        | 1490/10016 [5:20:02<30:31:16, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▍        | 1491/10016 [5:20:14<30:31:04, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▍        | 1491/10016 [5:20:14<30:31:04, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▍        | 1492/10016 [5:20:27<30:30:51, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▍        | 1492/10016 [5:20:27<30:30:51, 12.89s/it, avr_loss=0.0248]
steps:  15%|█▍        | 1493/10016 [5:20:40<30:30:38, 12.89s/it, avr_loss=0.0248]
steps:  15%|█▍        | 1493/10016 [5:20:40<30:30:38, 12.89s/it, avr_loss=0.0248]
steps:  15%|█▍        | 1494/10016 [5:20:53<30:30:25, 12.89s/it, avr_loss=0.0248]
steps:  15%|█▍        | 1494/10016 [5:20:53<30:30:25, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▍        | 1495/10016 [5:21:06<30:30:12, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▍        | 1495/10016 [5:21:06<30:30:12, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▍        | 1496/10016 [5:21:19<30:30:00, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▍        | 1496/10016 [5:21:19<30:30:00, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▍        | 1497/10016 [5:21:32<30:29:47, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▍        | 1497/10016 [5:21:32<30:29:47, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▍        | 1498/10016 [5:21:45<30:29:34, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▍        | 1498/10016 [5:21:45<30:29:34, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▍        | 1499/10016 [5:21:58<30:29:21, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▍        | 1499/10016 [5:21:58<30:29:21, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▍        | 1500/10016 [5:22:11<30:29:08, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▍        | 1500/10016 [5:22:11<30:29:08, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▍        | 1501/10016 [5:22:23<30:28:56, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▍        | 1501/10016 [5:22:23<30:28:56, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▍        | 1502/10016 [5:22:36<30:28:43, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▍        | 1502/10016 [5:22:36<30:28:43, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1503/10016 [5:22:49<30:28:30, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1503/10016 [5:22:49<30:28:30, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1504/10016 [5:23:02<30:28:17, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1504/10016 [5:23:02<30:28:17, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1505/10016 [5:23:15<30:28:04, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1505/10016 [5:23:15<30:28:04, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1506/10016 [5:23:28<30:27:52, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1506/10016 [5:23:28<30:27:52, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1507/10016 [5:23:41<30:27:39, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1507/10016 [5:23:41<30:27:39, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1508/10016 [5:23:54<30:27:26, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1508/10016 [5:23:54<30:27:26, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1509/10016 [5:24:07<30:27:13, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1509/10016 [5:24:07<30:27:13, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1510/10016 [5:24:20<30:27:01, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1510/10016 [5:24:20<30:27:01, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1511/10016 [5:24:33<30:26:48, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1511/10016 [5:24:33<30:26:48, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1512/10016 [5:24:45<30:26:35, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1512/10016 [5:24:45<30:26:35, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1513/10016 [5:24:58<30:26:22, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1513/10016 [5:24:58<30:26:22, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1514/10016 [5:25:11<30:26:09, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1514/10016 [5:25:11<30:26:09, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1515/10016 [5:25:24<30:25:56, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1515/10016 [5:25:24<30:25:56, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1516/10016 [5:25:37<30:25:43, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1516/10016 [5:25:37<30:25:43, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1517/10016 [5:25:50<30:25:31, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1517/10016 [5:25:50<30:25:31, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1518/10016 [5:26:03<30:25:18, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1518/10016 [5:26:03<30:25:18, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1519/10016 [5:26:16<30:25:05, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1519/10016 [5:26:16<30:25:05, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1520/10016 [5:26:29<30:24:52, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1520/10016 [5:26:29<30:24:52, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1521/10016 [5:26:42<30:24:40, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1521/10016 [5:26:42<30:24:40, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1522/10016 [5:26:54<30:24:27, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1522/10016 [5:26:54<30:24:27, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1523/10016 [5:27:07<30:24:14, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1523/10016 [5:27:07<30:24:14, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1524/10016 [5:27:20<30:24:01, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1524/10016 [5:27:20<30:24:01, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1525/10016 [5:27:33<30:23:48, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1525/10016 [5:27:33<30:23:48, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1526/10016 [5:27:46<30:23:36, 12.89s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1526/10016 [5:27:46<30:23:36, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1527/10016 [5:27:59<30:23:23, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1527/10016 [5:27:59<30:23:23, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1528/10016 [5:28:12<30:23:10, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1528/10016 [5:28:12<30:23:10, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1529/10016 [5:28:25<30:22:57, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1529/10016 [5:28:25<30:22:57, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1530/10016 [5:28:38<30:22:44, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1530/10016 [5:28:38<30:22:44, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1531/10016 [5:28:51<30:22:32, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1531/10016 [5:28:51<30:22:32, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1532/10016 [5:29:03<30:22:19, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1532/10016 [5:29:03<30:22:19, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1533/10016 [5:29:16<30:22:06, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1533/10016 [5:29:16<30:22:06, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1534/10016 [5:29:29<30:21:53, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1534/10016 [5:29:29<30:21:53, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1535/10016 [5:29:42<30:21:40, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1535/10016 [5:29:42<30:21:40, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1536/10016 [5:29:55<30:21:28, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1536/10016 [5:29:55<30:21:28, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1537/10016 [5:30:08<30:21:15, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1537/10016 [5:30:08<30:21:15, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1538/10016 [5:30:21<30:21:02, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1538/10016 [5:30:21<30:21:02, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1539/10016 [5:30:34<30:20:50, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1539/10016 [5:30:34<30:20:50, 12.89s/it, avr_loss=0.0245]
steps:  15%|█▌        | 1540/10016 [5:30:47<30:20:37, 12.89s/it, avr_loss=0.0245]
steps:  15%|█▌        | 1540/10016 [5:30:47<30:20:37, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1541/10016 [5:31:00<30:20:24, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1541/10016 [5:31:00<30:20:24, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1542/10016 [5:31:13<30:20:11, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1542/10016 [5:31:13<30:20:11, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1543/10016 [5:31:25<30:19:58, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1543/10016 [5:31:25<30:19:58, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1544/10016 [5:31:38<30:19:45, 12.89s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1544/10016 [5:31:38<30:19:45, 12.89s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1545/10016 [5:31:51<30:19:32, 12.89s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1545/10016 [5:31:51<30:19:32, 12.89s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1546/10016 [5:32:04<30:19:19, 12.89s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1546/10016 [5:32:04<30:19:19, 12.89s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1547/10016 [5:32:17<30:19:06, 12.89s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1547/10016 [5:32:17<30:19:06, 12.89s/it, avr_loss=0.0244]
steps:  15%|█▌        | 1548/10016 [5:32:30<30:18:53, 12.89s/it, avr_loss=0.0244]
steps:  15%|█▌        | 1548/10016 [5:32:30<30:18:53, 12.89s/it, avr_loss=0.0244]
steps:  15%|█▌        | 1549/10016 [5:32:43<30:18:40, 12.89s/it, avr_loss=0.0244]
steps:  15%|█▌        | 1549/10016 [5:32:43<30:18:40, 12.89s/it, avr_loss=0.0244]
steps:  15%|█▌        | 1550/10016 [5:32:56<30:18:27, 12.89s/it, avr_loss=0.0244]
steps:  15%|█▌        | 1550/10016 [5:32:56<30:18:27, 12.89s/it, avr_loss=0.0245]
steps:  15%|█▌        | 1551/10016 [5:33:08<30:18:14, 12.89s/it, avr_loss=0.0245]
steps:  15%|█▌        | 1551/10016 [5:33:08<30:18:14, 12.89s/it, avr_loss=0.0245]
steps:  15%|█▌        | 1552/10016 [5:33:21<30:18:01, 12.89s/it, avr_loss=0.0245]
steps:  15%|█▌        | 1552/10016 [5:33:21<30:18:01, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1553/10016 [5:33:34<30:17:48, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1553/10016 [5:33:34<30:17:48, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1554/10016 [5:33:47<30:17:36, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1554/10016 [5:33:47<30:17:36, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1555/10016 [5:34:00<30:17:23, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1555/10016 [5:34:00<30:17:23, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1556/10016 [5:34:13<30:17:10, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1556/10016 [5:34:13<30:17:10, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1557/10016 [5:34:26<30:16:57, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1557/10016 [5:34:26<30:16:57, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1558/10016 [5:34:39<30:16:44, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1558/10016 [5:34:39<30:16:44, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1559/10016 [5:34:51<30:16:31, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1559/10016 [5:34:51<30:16:31, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1560/10016 [5:35:04<30:16:18, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1560/10016 [5:35:04<30:16:18, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1561/10016 [5:35:17<30:16:05, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1561/10016 [5:35:17<30:16:05, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1562/10016 [5:35:30<30:15:52, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1562/10016 [5:35:30<30:15:52, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1563/10016 [5:35:43<30:15:39, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1563/10016 [5:35:43<30:15:39, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1564/10016 [5:35:56<30:15:26, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1564/10016 [5:35:56<30:15:26, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1565/10016 [5:36:09<30:15:13, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1565/10016 [5:36:09<30:15:13, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1566/10016 [5:36:22<30:15:01, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1566/10016 [5:36:22<30:15:01, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1567/10016 [5:36:35<30:14:48, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1567/10016 [5:36:35<30:14:48, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1568/10016 [5:36:47<30:14:35, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1568/10016 [5:36:47<30:14:35, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1569/10016 [5:37:00<30:14:22, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1569/10016 [5:37:00<30:14:22, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1570/10016 [5:37:13<30:14:09, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1570/10016 [5:37:13<30:14:09, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1571/10016 [5:37:26<30:13:56, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1571/10016 [5:37:26<30:13:56, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1572/10016 [5:37:39<30:13:43, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1572/10016 [5:37:39<30:13:43, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1573/10016 [5:37:52<30:13:30, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1573/10016 [5:37:52<30:13:30, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1574/10016 [5:38:05<30:13:17, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1574/10016 [5:38:05<30:13:17, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1575/10016 [5:38:18<30:13:04, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1575/10016 [5:38:18<30:13:04, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1576/10016 [5:38:30<30:12:51, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1576/10016 [5:38:30<30:12:51, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1577/10016 [5:38:43<30:12:39, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1577/10016 [5:38:43<30:12:39, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1578/10016 [5:38:56<30:12:26, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1578/10016 [5:38:56<30:12:26, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1579/10016 [5:39:09<30:12:13, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1579/10016 [5:39:09<30:12:13, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1580/10016 [5:39:22<30:12:00, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1580/10016 [5:39:22<30:12:00, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1581/10016 [5:39:35<30:11:47, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1581/10016 [5:39:35<30:11:47, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1582/10016 [5:39:48<30:11:34, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1582/10016 [5:39:48<30:11:34, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1583/10016 [5:40:01<30:11:21, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1583/10016 [5:40:01<30:11:21, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1584/10016 [5:40:14<30:11:08, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1584/10016 [5:40:14<30:11:08, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1585/10016 [5:40:26<30:10:55, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1585/10016 [5:40:26<30:10:55, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1586/10016 [5:40:39<30:10:42, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1586/10016 [5:40:39<30:10:42, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1587/10016 [5:40:52<30:10:29, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1587/10016 [5:40:52<30:10:29, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1588/10016 [5:41:05<30:10:16, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1588/10016 [5:41:05<30:10:16, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1589/10016 [5:41:18<30:10:04, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1589/10016 [5:41:18<30:10:04, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1590/10016 [5:41:31<30:09:51, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1590/10016 [5:41:31<30:09:51, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1591/10016 [5:41:44<30:09:38, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1591/10016 [5:41:44<30:09:38, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1592/10016 [5:41:57<30:09:25, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1592/10016 [5:41:57<30:09:25, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1593/10016 [5:42:09<30:09:12, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1593/10016 [5:42:09<30:09:12, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1594/10016 [5:42:22<30:08:59, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1594/10016 [5:42:22<30:08:59, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1595/10016 [5:42:35<30:08:46, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1595/10016 [5:42:35<30:08:46, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1596/10016 [5:42:48<30:08:33, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1596/10016 [5:42:48<30:08:33, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1597/10016 [5:43:01<30:08:20, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1597/10016 [5:43:01<30:08:20, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1598/10016 [5:43:14<30:08:07, 12.89s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1598/10016 [5:43:14<30:08:07, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1599/10016 [5:43:27<30:07:54, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1599/10016 [5:43:27<30:07:54, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1600/10016 [5:43:40<30:07:42, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1600/10016 [5:43:40<30:07:42, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1601/10016 [5:43:53<30:07:29, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1601/10016 [5:43:53<30:07:29, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1602/10016 [5:44:05<30:07:16, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1602/10016 [5:44:05<30:07:16, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1603/10016 [5:44:18<30:07:03, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1603/10016 [5:44:18<30:07:03, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1604/10016 [5:44:31<30:06:50, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1604/10016 [5:44:31<30:06:50, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1605/10016 [5:44:44<30:06:37, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1605/10016 [5:44:44<30:06:37, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1606/10016 [5:44:57<30:06:24, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1606/10016 [5:44:57<30:06:24, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1607/10016 [5:45:10<30:06:11, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1607/10016 [5:45:10<30:06:11, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1608/10016 [5:45:23<30:05:58, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1608/10016 [5:45:23<30:05:58, 12.89s/it, avr_loss=0.0242]
steps:  16%|█▌        | 1609/10016 [5:45:36<30:05:45, 12.89s/it, avr_loss=0.0242]
steps:  16%|█▌        | 1609/10016 [5:45:36<30:05:45, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1610/10016 [5:45:48<30:05:32, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1610/10016 [5:45:48<30:05:32, 12.89s/it, avr_loss=0.0242]
steps:  16%|█▌        | 1611/10016 [5:46:01<30:05:19, 12.89s/it, avr_loss=0.0242]
steps:  16%|█▌        | 1611/10016 [5:46:01<30:05:19, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1612/10016 [5:46:14<30:05:06, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1612/10016 [5:46:14<30:05:06, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1613/10016 [5:46:27<30:04:54, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1613/10016 [5:46:27<30:04:54, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1614/10016 [5:46:40<30:04:41, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1614/10016 [5:46:40<30:04:41, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1615/10016 [5:46:53<30:04:28, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1615/10016 [5:46:53<30:04:28, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1616/10016 [5:47:06<30:04:15, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1616/10016 [5:47:06<30:04:15, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1617/10016 [5:47:19<30:04:02, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1617/10016 [5:47:19<30:04:02, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1618/10016 [5:47:32<30:03:49, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1618/10016 [5:47:32<30:03:49, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1619/10016 [5:47:44<30:03:36, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1619/10016 [5:47:44<30:03:36, 12.89s/it, avr_loss=0.0242]
steps:  16%|█▌        | 1620/10016 [5:47:57<30:03:23, 12.89s/it, avr_loss=0.0242]
steps:  16%|█▌        | 1620/10016 [5:47:57<30:03:23, 12.89s/it, avr_loss=0.0242]
steps:  16%|█▌        | 1621/10016 [5:48:10<30:03:10, 12.89s/it, avr_loss=0.0242]
steps:  16%|█▌        | 1621/10016 [5:48:10<30:03:10, 12.89s/it, avr_loss=0.0242]
steps:  16%|█▌        | 1622/10016 [5:48:23<30:02:57, 12.89s/it, avr_loss=0.0242]
steps:  16%|█▌        | 1622/10016 [5:48:23<30:02:57, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1623/10016 [5:48:36<30:02:44, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1623/10016 [5:48:36<30:02:44, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1624/10016 [5:48:49<30:02:31, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1624/10016 [5:48:49<30:02:31, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1625/10016 [5:49:02<30:02:18, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▌        | 1625/10016 [5:49:02<30:02:18, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1626/10016 [5:49:15<30:02:05, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▌        | 1626/10016 [5:49:15<30:02:05, 12.89s/it, avr_loss=0.0242]
steps:  16%|█▌        | 1627/10016 [5:49:27<30:01:53, 12.89s/it, avr_loss=0.0242]
steps:  16%|█▌        | 1627/10016 [5:49:27<30:01:53, 12.89s/it, avr_loss=0.0242]
steps:  16%|█▋        | 1628/10016 [5:49:40<30:01:40, 12.89s/it, avr_loss=0.0242]
steps:  16%|█▋        | 1628/10016 [5:49:40<30:01:40, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▋        | 1629/10016 [5:49:53<30:01:27, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▋        | 1629/10016 [5:49:53<30:01:27, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▋        | 1630/10016 [5:50:06<30:01:14, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▋        | 1630/10016 [5:50:06<30:01:14, 12.89s/it, avr_loss=0.0242]
steps:  16%|█▋        | 1631/10016 [5:50:19<30:01:01, 12.89s/it, avr_loss=0.0242]
steps:  16%|█▋        | 1631/10016 [5:50:19<30:01:01, 12.89s/it, avr_loss=0.0242]
steps:  16%|█▋        | 1632/10016 [5:50:32<30:00:48, 12.89s/it, avr_loss=0.0242]
steps:  16%|█▋        | 1632/10016 [5:50:32<30:00:48, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▋        | 1633/10016 [5:50:45<30:00:35, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▋        | 1633/10016 [5:50:45<30:00:35, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▋        | 1634/10016 [5:50:58<30:00:22, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▋        | 1634/10016 [5:50:58<30:00:22, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▋        | 1635/10016 [5:51:10<30:00:09, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▋        | 1635/10016 [5:51:10<30:00:09, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▋        | 1636/10016 [5:51:23<29:59:56, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▋        | 1636/10016 [5:51:23<29:59:56, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▋        | 1637/10016 [5:51:36<29:59:43, 12.89s/it, avr_loss=0.0244]
steps:  16%|█▋        | 1637/10016 [5:51:36<29:59:43, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▋        | 1638/10016 [5:51:49<29:59:30, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▋        | 1638/10016 [5:51:49<29:59:30, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▋        | 1639/10016 [5:52:02<29:59:17, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▋        | 1639/10016 [5:52:02<29:59:17, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▋        | 1640/10016 [5:52:15<29:59:05, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▋        | 1640/10016 [5:52:15<29:59:05, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▋        | 1641/10016 [5:52:28<29:58:52, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▋        | 1641/10016 [5:52:28<29:58:52, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▋        | 1642/10016 [5:52:41<29:58:39, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▋        | 1642/10016 [5:52:41<29:58:39, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▋        | 1643/10016 [5:52:54<29:58:26, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▋        | 1643/10016 [5:52:54<29:58:26, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▋        | 1644/10016 [5:53:06<29:58:13, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▋        | 1644/10016 [5:53:06<29:58:13, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▋        | 1645/10016 [5:53:19<29:58:01, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▋        | 1645/10016 [5:53:19<29:58:01, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▋        | 1646/10016 [5:53:32<29:57:48, 12.89s/it, avr_loss=0.0243]
steps:  16%|█▋        | 1646/10016 [5:53:32<29:57:48, 12.89s/it, avr_loss=0.0242]
steps:  16%|█▋        | 1647/10016 [5:53:45<29:57:35, 12.89s/it, avr_loss=0.0242]
steps:  16%|█▋        | 1647/10016 [5:53:45<29:57:35, 12.89s/it, avr_loss=0.0242]
steps:  16%|█▋        | 1648/10016 [5:53:58<29:57:22, 12.89s/it, avr_loss=0.0242]
steps:  16%|█▋        | 1648/10016 [5:53:58<29:57:22, 12.89s/it, avr_loss=0.0242]
steps:  16%|█▋        | 1649/10016 [5:54:11<29:57:09, 12.89s/it, avr_loss=0.0242]
steps:  16%|█▋        | 1649/10016 [5:54:11<29:57:09, 12.89s/it, avr_loss=0.0241]
steps:  16%|█▋        | 1650/10016 [5:54:24<29:56:56, 12.89s/it, avr_loss=0.0241]
steps:  16%|█▋        | 1650/10016 [5:54:24<29:56:56, 12.89s/it, avr_loss=0.0241]
steps:  16%|█▋        | 1651/10016 [5:54:37<29:56:43, 12.89s/it, avr_loss=0.0241]
steps:  16%|█▋        | 1651/10016 [5:54:37<29:56:43, 12.89s/it, avr_loss=0.0242]
steps:  16%|█▋        | 1652/10016 [5:54:50<29:56:30, 12.89s/it, avr_loss=0.0242]
steps:  16%|█▋        | 1652/10016 [5:54:50<29:56:30, 12.89s/it, avr_loss=0.0242]
steps:  17%|█▋        | 1653/10016 [5:55:02<29:56:17, 12.89s/it, avr_loss=0.0242]
steps:  17%|█▋        | 1653/10016 [5:55:02<29:56:17, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1654/10016 [5:55:15<29:56:04, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1654/10016 [5:55:15<29:56:04, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1655/10016 [5:55:28<29:55:51, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1655/10016 [5:55:28<29:55:51, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1656/10016 [5:55:41<29:55:38, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1656/10016 [5:55:41<29:55:38, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1657/10016 [5:55:54<29:55:25, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1657/10016 [5:55:54<29:55:25, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1658/10016 [5:56:07<29:55:12, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1658/10016 [5:56:07<29:55:12, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1659/10016 [5:56:20<29:55:00, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1659/10016 [5:56:20<29:55:00, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1660/10016 [5:56:33<29:54:47, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1660/10016 [5:56:33<29:54:47, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1661/10016 [5:56:45<29:54:34, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1661/10016 [5:56:45<29:54:34, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1662/10016 [5:56:58<29:54:21, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1662/10016 [5:56:58<29:54:21, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1663/10016 [5:57:11<29:54:08, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1663/10016 [5:57:11<29:54:08, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1664/10016 [5:57:24<29:53:55, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1664/10016 [5:57:24<29:53:55, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1665/10016 [5:57:37<29:53:42, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1665/10016 [5:57:37<29:53:42, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1666/10016 [5:57:50<29:53:29, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1666/10016 [5:57:50<29:53:29, 12.89s/it, avr_loss=0.0242]
steps:  17%|█▋        | 1667/10016 [5:58:03<29:53:16, 12.89s/it, avr_loss=0.0242]
steps:  17%|█▋        | 1667/10016 [5:58:03<29:53:16, 12.89s/it, avr_loss=0.0242]
steps:  17%|█▋        | 1668/10016 [5:58:16<29:53:03, 12.89s/it, avr_loss=0.0242]
steps:  17%|█▋        | 1668/10016 [5:58:16<29:53:03, 12.89s/it, avr_loss=0.0242]
steps:  17%|█▋        | 1669/10016 [5:58:28<29:52:50, 12.89s/it, avr_loss=0.0242]
steps:  17%|█▋        | 1669/10016 [5:58:28<29:52:50, 12.89s/it, avr_loss=0.0242]
steps:  17%|█▋        | 1670/10016 [5:58:41<29:52:37, 12.89s/it, avr_loss=0.0242]
steps:  17%|█▋        | 1670/10016 [5:58:41<29:52:37, 12.89s/it, avr_loss=0.0242]
steps:  17%|█▋        | 1671/10016 [5:58:54<29:52:24, 12.89s/it, avr_loss=0.0242]
steps:  17%|█▋        | 1671/10016 [5:58:54<29:52:24, 12.89s/it, avr_loss=0.0243]
steps:  17%|█▋        | 1672/10016 [5:59:07<29:52:11, 12.89s/it, avr_loss=0.0243]
steps:  17%|█▋        | 1672/10016 [5:59:07<29:52:11, 12.89s/it, avr_loss=0.0242]
steps:  17%|█▋        | 1673/10016 [5:59:20<29:51:58, 12.89s/it, avr_loss=0.0242]
steps:  17%|█▋        | 1673/10016 [5:59:20<29:51:58, 12.89s/it, avr_loss=0.0242]
steps:  17%|█▋        | 1674/10016 [5:59:33<29:51:45, 12.89s/it, avr_loss=0.0242]
steps:  17%|█▋        | 1674/10016 [5:59:33<29:51:45, 12.89s/it, avr_loss=0.0242]
steps:  17%|█▋        | 1675/10016 [5:59:46<29:51:32, 12.89s/it, avr_loss=0.0242]
steps:  17%|█▋        | 1675/10016 [5:59:46<29:51:32, 12.89s/it, avr_loss=0.0242]
steps:  17%|█▋        | 1676/10016 [5:59:59<29:51:19, 12.89s/it, avr_loss=0.0242]
steps:  17%|█▋        | 1676/10016 [5:59:59<29:51:19, 12.89s/it, avr_loss=0.0242]
steps:  17%|█▋        | 1677/10016 [6:00:11<29:51:07, 12.89s/it, avr_loss=0.0242]
steps:  17%|█▋        | 1677/10016 [6:00:11<29:51:07, 12.89s/it, avr_loss=0.0242]
steps:  17%|█▋        | 1678/10016 [6:00:24<29:50:54, 12.89s/it, avr_loss=0.0242]
steps:  17%|█▋        | 1678/10016 [6:00:24<29:50:54, 12.89s/it, avr_loss=0.0242]
steps:  17%|█▋        | 1679/10016 [6:00:37<29:50:41, 12.89s/it, avr_loss=0.0242]
steps:  17%|█▋        | 1679/10016 [6:00:37<29:50:41, 12.89s/it, avr_loss=0.0242]
steps:  17%|█▋        | 1680/10016 [6:00:50<29:50:28, 12.89s/it, avr_loss=0.0242]
steps:  17%|█▋        | 1680/10016 [6:00:50<29:50:28, 12.89s/it, avr_loss=0.0242]
steps:  17%|█▋        | 1681/10016 [6:01:03<29:50:15, 12.89s/it, avr_loss=0.0242]
steps:  17%|█▋        | 1681/10016 [6:01:03<29:50:15, 12.89s/it, avr_loss=0.0242]
steps:  17%|█▋        | 1682/10016 [6:01:16<29:50:02, 12.89s/it, avr_loss=0.0242]
steps:  17%|█▋        | 1682/10016 [6:01:16<29:50:02, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1683/10016 [6:01:29<29:49:49, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1683/10016 [6:01:29<29:49:49, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1684/10016 [6:01:42<29:49:36, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1684/10016 [6:01:42<29:49:36, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1685/10016 [6:01:54<29:49:23, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1685/10016 [6:01:54<29:49:23, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1686/10016 [6:02:07<29:49:10, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1686/10016 [6:02:07<29:49:10, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1687/10016 [6:02:20<29:48:57, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1687/10016 [6:02:20<29:48:57, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1688/10016 [6:02:33<29:48:44, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1688/10016 [6:02:33<29:48:44, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1689/10016 [6:02:46<29:48:31, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1689/10016 [6:02:46<29:48:31, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1690/10016 [6:02:59<29:48:18, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1690/10016 [6:02:59<29:48:18, 12.89s/it, avr_loss=0.024] 
steps:  17%|█▋        | 1691/10016 [6:03:12<29:48:05, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1691/10016 [6:03:12<29:48:05, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1692/10016 [6:03:25<29:47:52, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1692/10016 [6:03:25<29:47:52, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1693/10016 [6:03:38<29:47:40, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1693/10016 [6:03:38<29:47:40, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1694/10016 [6:03:50<29:47:27, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1694/10016 [6:03:50<29:47:27, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1695/10016 [6:04:03<29:47:14, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1695/10016 [6:04:03<29:47:14, 12.89s/it, avr_loss=0.024] 
steps:  17%|█▋        | 1696/10016 [6:04:16<29:47:01, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1696/10016 [6:04:16<29:47:01, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1697/10016 [6:04:29<29:46:48, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1697/10016 [6:04:29<29:46:48, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1698/10016 [6:04:42<29:46:35, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1698/10016 [6:04:42<29:46:35, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1699/10016 [6:04:55<29:46:22, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1699/10016 [6:04:55<29:46:22, 12.89s/it, avr_loss=0.024] 
steps:  17%|█▋        | 1700/10016 [6:05:08<29:46:09, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1700/10016 [6:05:08<29:46:09, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1701/10016 [6:05:21<29:45:56, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1701/10016 [6:05:21<29:45:56, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1702/10016 [6:05:33<29:45:43, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1702/10016 [6:05:33<29:45:43, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1703/10016 [6:05:46<29:45:30, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1703/10016 [6:05:46<29:45:30, 12.89s/it, avr_loss=0.024] 
steps:  17%|█▋        | 1704/10016 [6:05:59<29:45:17, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1704/10016 [6:05:59<29:45:17, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1705/10016 [6:06:12<29:45:04, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1705/10016 [6:06:12<29:45:04, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1706/10016 [6:06:25<29:44:51, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1706/10016 [6:06:25<29:44:51, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1707/10016 [6:06:38<29:44:38, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1707/10016 [6:06:38<29:44:38, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1708/10016 [6:06:51<29:44:25, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1708/10016 [6:06:51<29:44:25, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1709/10016 [6:07:03<29:44:12, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1709/10016 [6:07:03<29:44:12, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1710/10016 [6:07:16<29:43:59, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1710/10016 [6:07:16<29:43:59, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1711/10016 [6:07:29<29:43:46, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1711/10016 [6:07:29<29:43:46, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1712/10016 [6:07:42<29:43:33, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1712/10016 [6:07:42<29:43:33, 12.89s/it, avr_loss=0.024] 
steps:  17%|█▋        | 1713/10016 [6:07:55<29:43:21, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1713/10016 [6:07:55<29:43:21, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1714/10016 [6:08:08<29:43:08, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1714/10016 [6:08:08<29:43:08, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1715/10016 [6:08:21<29:42:55, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1715/10016 [6:08:21<29:42:55, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1716/10016 [6:08:34<29:42:42, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1716/10016 [6:08:34<29:42:42, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1717/10016 [6:08:46<29:42:29, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1717/10016 [6:08:46<29:42:29, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1718/10016 [6:08:59<29:42:16, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1718/10016 [6:08:59<29:42:16, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1719/10016 [6:09:12<29:42:03, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1719/10016 [6:09:12<29:42:03, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1720/10016 [6:09:25<29:41:50, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1720/10016 [6:09:25<29:41:50, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1721/10016 [6:09:38<29:41:37, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1721/10016 [6:09:38<29:41:37, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1722/10016 [6:09:51<29:41:24, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1722/10016 [6:09:51<29:41:24, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1723/10016 [6:10:04<29:41:11, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1723/10016 [6:10:04<29:41:11, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1724/10016 [6:10:17<29:40:58, 12.89s/it, avr_loss=0.0241]
steps:  17%|█▋        | 1724/10016 [6:10:17<29:40:58, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1725/10016 [6:10:30<29:40:48, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1725/10016 [6:10:30<29:40:48, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1726/10016 [6:10:43<29:40:35, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1726/10016 [6:10:43<29:40:35, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1727/10016 [6:10:56<29:40:22, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1727/10016 [6:10:56<29:40:22, 12.89s/it, avr_loss=0.024] 
steps:  17%|█▋        | 1728/10016 [6:11:09<29:40:09, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1728/10016 [6:11:09<29:40:09, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1729/10016 [6:11:22<29:40:00, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1729/10016 [6:11:22<29:40:00, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1730/10016 [6:11:37<29:39:54, 12.89s/it, avr_loss=0.024]
steps:  17%|█▋        | 1730/10016 [6:11:37<29:39:54, 12.89s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1731/10016 [6:12:06<29:40:58, 12.90s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1731/10016 [6:12:06<29:40:58, 12.90s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1732/10016 [6:12:36<29:42:09, 12.91s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1732/10016 [6:12:36<29:42:09, 12.91s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1733/10016 [6:13:07<29:43:20, 12.92s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1733/10016 [6:13:07<29:43:20, 12.92s/it, avr_loss=0.0238]
steps:  17%|█▋        | 1734/10016 [6:13:29<29:43:50, 12.92s/it, avr_loss=0.0238]
steps:  17%|█▋        | 1734/10016 [6:13:29<29:43:50, 12.92s/it, avr_loss=0.0238]
steps:  17%|█▋        | 1735/10016 [6:13:41<29:43:37, 12.92s/it, avr_loss=0.0238]
steps:  17%|█▋        | 1735/10016 [6:13:41<29:43:37, 12.92s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1736/10016 [6:13:54<29:43:23, 12.92s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1736/10016 [6:13:54<29:43:23, 12.92s/it, avr_loss=0.0238]
steps:  17%|█▋        | 1737/10016 [6:14:07<29:43:10, 12.92s/it, avr_loss=0.0238]
steps:  17%|█▋        | 1737/10016 [6:14:07<29:43:10, 12.92s/it, avr_loss=0.0238]
steps:  17%|█▋        | 1738/10016 [6:14:20<29:42:57, 12.92s/it, avr_loss=0.0238]
steps:  17%|█▋        | 1738/10016 [6:14:20<29:42:57, 12.92s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1739/10016 [6:14:33<29:42:44, 12.92s/it, avr_loss=0.0239]
steps:  17%|█▋        | 1739/10016 [6:14:33<29:42:44, 12.92s/it, avr_loss=0.0238]
steps:  17%|█▋        | 1740/10016 [6:14:46<29:42:32, 12.92s/it, avr_loss=0.0238]
steps:  17%|█▋        | 1740/10016 [6:14:46<29:42:32, 12.92s/it, avr_loss=0.0238]
steps:  17%|█▋        | 1741/10016 [6:14:59<29:42:19, 12.92s/it, avr_loss=0.0238]
steps:  17%|█▋        | 1741/10016 [6:14:59<29:42:19, 12.92s/it, avr_loss=0.0238]
steps:  17%|█▋        | 1742/10016 [6:15:12<29:42:06, 12.92s/it, avr_loss=0.0238]
steps:  17%|█▋        | 1742/10016 [6:15:12<29:42:06, 12.92s/it, avr_loss=0.0238]
steps:  17%|█▋        | 1743/10016 [6:15:25<29:41:53, 12.92s/it, avr_loss=0.0238]
steps:  17%|█▋        | 1743/10016 [6:15:25<29:41:53, 12.92s/it, avr_loss=0.0238]
steps:  17%|█▋        | 1744/10016 [6:15:37<29:41:40, 12.92s/it, avr_loss=0.0238]
steps:  17%|█▋        | 1744/10016 [6:15:37<29:41:40, 12.92s/it, avr_loss=0.0238]
steps:  17%|█▋        | 1745/10016 [6:15:50<29:41:27, 12.92s/it, avr_loss=0.0238]
steps:  17%|█▋        | 1745/10016 [6:15:50<29:41:27, 12.92s/it, avr_loss=0.0237]
steps:  17%|█▋        | 1746/10016 [6:16:03<29:41:14, 12.92s/it, avr_loss=0.0237]
steps:  17%|█▋        | 1746/10016 [6:16:03<29:41:14, 12.92s/it, avr_loss=0.0237]
steps:  17%|█▋        | 1747/10016 [6:16:16<29:41:00, 12.92s/it, avr_loss=0.0237]
steps:  17%|█▋        | 1747/10016 [6:16:16<29:41:00, 12.92s/it, avr_loss=0.0237]
steps:  17%|█▋        | 1748/10016 [6:16:29<29:40:47, 12.92s/it, avr_loss=0.0237]
steps:  17%|█▋        | 1748/10016 [6:16:29<29:40:47, 12.92s/it, avr_loss=0.0237]
steps:  17%|█▋        | 1749/10016 [6:16:42<29:40:34, 12.92s/it, avr_loss=0.0237]
steps:  17%|█▋        | 1749/10016 [6:16:42<29:40:34, 12.92s/it, avr_loss=0.0237]
steps:  17%|█▋        | 1750/10016 [6:16:55<29:40:21, 12.92s/it, avr_loss=0.0237]
steps:  17%|█▋        | 1750/10016 [6:16:55<29:40:21, 12.92s/it, avr_loss=0.0237]
steps:  17%|█▋        | 1751/10016 [6:17:08<29:40:08, 12.92s/it, avr_loss=0.0237]
steps:  17%|█▋        | 1751/10016 [6:17:08<29:40:08, 12.92s/it, avr_loss=0.0237]
steps:  17%|█▋        | 1752/10016 [6:17:21<29:39:55, 12.92s/it, avr_loss=0.0237]
steps:  17%|█▋        | 1752/10016 [6:17:21<29:39:55, 12.92s/it, avr_loss=0.0237]
steps:  18%|█▊        | 1753/10016 [6:17:34<29:39:42, 12.92s/it, avr_loss=0.0237]
steps:  18%|█▊        | 1753/10016 [6:17:34<29:39:42, 12.92s/it, avr_loss=0.0237]
steps:  18%|█▊        | 1754/10016 [6:17:47<29:39:30, 12.92s/it, avr_loss=0.0237]
steps:  18%|█▊        | 1754/10016 [6:17:47<29:39:30, 12.92s/it, avr_loss=0.0237]
steps:  18%|█▊        | 1755/10016 [6:17:59<29:39:17, 12.92s/it, avr_loss=0.0237]
steps:  18%|█▊        | 1755/10016 [6:17:59<29:39:17, 12.92s/it, avr_loss=0.0237]
steps:  18%|█▊        | 1756/10016 [6:18:12<29:39:04, 12.92s/it, avr_loss=0.0237]
steps:  18%|█▊        | 1756/10016 [6:18:12<29:39:04, 12.92s/it, avr_loss=0.0237]
steps:  18%|█▊        | 1757/10016 [6:18:25<29:38:51, 12.92s/it, avr_loss=0.0237]
steps:  18%|█▊        | 1757/10016 [6:18:25<29:38:51, 12.92s/it, avr_loss=0.0237]
steps:  18%|█▊        | 1758/10016 [6:18:38<29:38:37, 12.92s/it, avr_loss=0.0237]
steps:  18%|█▊        | 1758/10016 [6:18:38<29:38:37, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1759/10016 [6:18:51<29:38:24, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1759/10016 [6:18:51<29:38:24, 12.92s/it, avr_loss=0.0235]
steps:  18%|█▊        | 1760/10016 [6:19:04<29:38:11, 12.92s/it, avr_loss=0.0235]
steps:  18%|█▊        | 1760/10016 [6:19:04<29:38:11, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1761/10016 [6:19:17<29:37:58, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1761/10016 [6:19:17<29:37:58, 12.92s/it, avr_loss=0.0235]
steps:  18%|█▊        | 1762/10016 [6:19:30<29:37:45, 12.92s/it, avr_loss=0.0235]
steps:  18%|█▊        | 1762/10016 [6:19:30<29:37:45, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1763/10016 [6:19:43<29:37:32, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1763/10016 [6:19:43<29:37:32, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1764/10016 [6:19:55<29:37:19, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1764/10016 [6:19:55<29:37:19, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1765/10016 [6:20:08<29:37:06, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1765/10016 [6:20:08<29:37:06, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1766/10016 [6:20:21<29:36:53, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1766/10016 [6:20:21<29:36:53, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1767/10016 [6:20:34<29:36:40, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1767/10016 [6:20:34<29:36:40, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1768/10016 [6:20:47<29:36:27, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1768/10016 [6:20:47<29:36:27, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1769/10016 [6:21:00<29:36:14, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1769/10016 [6:21:00<29:36:14, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1770/10016 [6:21:13<29:36:01, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1770/10016 [6:21:13<29:36:01, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1771/10016 [6:21:26<29:35:48, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1771/10016 [6:21:26<29:35:48, 12.92s/it, avr_loss=0.0235]
steps:  18%|█▊        | 1772/10016 [6:21:39<29:35:35, 12.92s/it, avr_loss=0.0235]
steps:  18%|█▊        | 1772/10016 [6:21:39<29:35:35, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1773/10016 [6:21:51<29:35:21, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1773/10016 [6:21:51<29:35:21, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1774/10016 [6:22:04<29:35:08, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1774/10016 [6:22:04<29:35:08, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1775/10016 [6:22:17<29:34:55, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1775/10016 [6:22:17<29:34:55, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1776/10016 [6:22:30<29:34:42, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1776/10016 [6:22:30<29:34:42, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1777/10016 [6:22:43<29:34:29, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1777/10016 [6:22:43<29:34:29, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1778/10016 [6:22:56<29:34:16, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1778/10016 [6:22:56<29:34:16, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1779/10016 [6:23:09<29:34:03, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1779/10016 [6:23:09<29:34:03, 12.92s/it, avr_loss=0.0237]
steps:  18%|█▊        | 1780/10016 [6:23:22<29:33:50, 12.92s/it, avr_loss=0.0237]
steps:  18%|█▊        | 1780/10016 [6:23:22<29:33:50, 12.92s/it, avr_loss=0.0237]
steps:  18%|█▊        | 1781/10016 [6:23:35<29:33:37, 12.92s/it, avr_loss=0.0237]
steps:  18%|█▊        | 1781/10016 [6:23:35<29:33:37, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1782/10016 [6:23:48<29:33:24, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1782/10016 [6:23:48<29:33:24, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1783/10016 [6:24:00<29:33:11, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1783/10016 [6:24:00<29:33:11, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1784/10016 [6:24:13<29:32:58, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1784/10016 [6:24:13<29:32:58, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1785/10016 [6:24:26<29:32:45, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1785/10016 [6:24:26<29:32:45, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1786/10016 [6:24:39<29:32:33, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1786/10016 [6:24:39<29:32:33, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1787/10016 [6:24:52<29:32:20, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1787/10016 [6:24:52<29:32:20, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1788/10016 [6:25:05<29:32:07, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1788/10016 [6:25:05<29:32:07, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1789/10016 [6:25:18<29:31:54, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1789/10016 [6:25:18<29:31:54, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1790/10016 [6:25:31<29:31:41, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1790/10016 [6:25:31<29:31:41, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1791/10016 [6:25:44<29:31:28, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1791/10016 [6:25:44<29:31:28, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1792/10016 [6:25:57<29:31:15, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1792/10016 [6:25:57<29:31:15, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1793/10016 [6:26:10<29:31:02, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1793/10016 [6:26:10<29:31:02, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1794/10016 [6:26:23<29:30:49, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1794/10016 [6:26:23<29:30:49, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1795/10016 [6:26:35<29:30:36, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1795/10016 [6:26:35<29:30:36, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1796/10016 [6:26:48<29:30:23, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1796/10016 [6:26:48<29:30:23, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1797/10016 [6:27:01<29:30:10, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1797/10016 [6:27:01<29:30:10, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1798/10016 [6:27:14<29:29:57, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1798/10016 [6:27:14<29:29:57, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1799/10016 [6:27:27<29:29:44, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1799/10016 [6:27:27<29:29:44, 12.92s/it, avr_loss=0.0235]
steps:  18%|█▊        | 1800/10016 [6:27:40<29:29:30, 12.92s/it, avr_loss=0.0235]
steps:  18%|█▊        | 1800/10016 [6:27:40<29:29:30, 12.92s/it, avr_loss=0.0235]
steps:  18%|█▊        | 1801/10016 [6:27:53<29:29:17, 12.92s/it, avr_loss=0.0235]
steps:  18%|█▊        | 1801/10016 [6:27:53<29:29:17, 12.92s/it, avr_loss=0.0237]
steps:  18%|█▊        | 1802/10016 [6:28:06<29:29:04, 12.92s/it, avr_loss=0.0237]
steps:  18%|█▊        | 1802/10016 [6:28:06<29:29:04, 12.92s/it, avr_loss=0.0237]
steps:  18%|█▊        | 1803/10016 [6:28:19<29:28:51, 12.92s/it, avr_loss=0.0237]
steps:  18%|█▊        | 1803/10016 [6:28:19<29:28:51, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1804/10016 [6:28:32<29:28:38, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1804/10016 [6:28:32<29:28:38, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1805/10016 [6:28:44<29:28:25, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1805/10016 [6:28:44<29:28:25, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1806/10016 [6:28:57<29:28:12, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1806/10016 [6:28:57<29:28:12, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1807/10016 [6:29:10<29:27:59, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1807/10016 [6:29:10<29:27:59, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1808/10016 [6:29:23<29:27:46, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1808/10016 [6:29:23<29:27:46, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1809/10016 [6:29:36<29:27:33, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1809/10016 [6:29:36<29:27:33, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1810/10016 [6:29:49<29:27:20, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1810/10016 [6:29:49<29:27:20, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1811/10016 [6:30:02<29:27:07, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1811/10016 [6:30:02<29:27:07, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1812/10016 [6:30:15<29:26:54, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1812/10016 [6:30:15<29:26:54, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1813/10016 [6:30:28<29:26:41, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1813/10016 [6:30:28<29:26:41, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1814/10016 [6:30:41<29:26:28, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1814/10016 [6:30:41<29:26:28, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1815/10016 [6:30:53<29:26:15, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1815/10016 [6:30:53<29:26:15, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1816/10016 [6:31:06<29:26:02, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1816/10016 [6:31:06<29:26:02, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1817/10016 [6:31:19<29:25:49, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1817/10016 [6:31:19<29:25:49, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1818/10016 [6:31:32<29:25:36, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1818/10016 [6:31:32<29:25:36, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1819/10016 [6:31:45<29:25:23, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1819/10016 [6:31:45<29:25:23, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1820/10016 [6:31:58<29:25:10, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1820/10016 [6:31:58<29:25:10, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1821/10016 [6:32:11<29:24:57, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1821/10016 [6:32:11<29:24:57, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1822/10016 [6:32:24<29:24:44, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1822/10016 [6:32:24<29:24:44, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1823/10016 [6:32:37<29:24:31, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1823/10016 [6:32:37<29:24:31, 12.92s/it, avr_loss=0.0237]
steps:  18%|█▊        | 1824/10016 [6:32:50<29:24:18, 12.92s/it, avr_loss=0.0237]
steps:  18%|█▊        | 1824/10016 [6:32:50<29:24:18, 12.92s/it, avr_loss=0.0237]
steps:  18%|█▊        | 1825/10016 [6:33:02<29:24:05, 12.92s/it, avr_loss=0.0237]
steps:  18%|█▊        | 1825/10016 [6:33:02<29:24:05, 12.92s/it, avr_loss=0.0237]
steps:  18%|█▊        | 1826/10016 [6:33:15<29:23:52, 12.92s/it, avr_loss=0.0237]
steps:  18%|█▊        | 1826/10016 [6:33:15<29:23:52, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1827/10016 [6:33:28<29:23:39, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1827/10016 [6:33:28<29:23:39, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1828/10016 [6:33:41<29:23:26, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1828/10016 [6:33:41<29:23:26, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1829/10016 [6:33:54<29:23:13, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1829/10016 [6:33:54<29:23:13, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1830/10016 [6:34:07<29:23:00, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1830/10016 [6:34:07<29:23:00, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1831/10016 [6:34:20<29:22:47, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1831/10016 [6:34:20<29:22:47, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1832/10016 [6:34:33<29:22:34, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1832/10016 [6:34:33<29:22:34, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1833/10016 [6:34:46<29:22:21, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1833/10016 [6:34:46<29:22:21, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1834/10016 [6:34:59<29:22:08, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1834/10016 [6:34:59<29:22:08, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1835/10016 [6:35:11<29:21:55, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1835/10016 [6:35:11<29:21:55, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1836/10016 [6:35:24<29:21:42, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1836/10016 [6:35:24<29:21:42, 12.92s/it, avr_loss=0.0237]
steps:  18%|█▊        | 1837/10016 [6:35:37<29:21:29, 12.92s/it, avr_loss=0.0237]
steps:  18%|█▊        | 1837/10016 [6:35:37<29:21:29, 12.92s/it, avr_loss=0.0237]
steps:  18%|█▊        | 1838/10016 [6:35:50<29:21:16, 12.92s/it, avr_loss=0.0237]
steps:  18%|█▊        | 1838/10016 [6:35:50<29:21:16, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1839/10016 [6:36:03<29:21:03, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1839/10016 [6:36:03<29:21:03, 12.92s/it, avr_loss=0.0237]
steps:  18%|█▊        | 1840/10016 [6:36:16<29:20:50, 12.92s/it, avr_loss=0.0237]
steps:  18%|█▊        | 1840/10016 [6:36:16<29:20:50, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1841/10016 [6:36:29<29:20:37, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1841/10016 [6:36:29<29:20:37, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1842/10016 [6:36:42<29:20:23, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1842/10016 [6:36:42<29:20:23, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1843/10016 [6:36:55<29:20:10, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1843/10016 [6:36:55<29:20:10, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1844/10016 [6:37:07<29:19:57, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1844/10016 [6:37:07<29:19:57, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1845/10016 [6:37:20<29:19:44, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1845/10016 [6:37:20<29:19:44, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1846/10016 [6:37:33<29:19:31, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1846/10016 [6:37:33<29:19:31, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1847/10016 [6:37:46<29:19:18, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1847/10016 [6:37:46<29:19:18, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1848/10016 [6:37:59<29:19:05, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1848/10016 [6:37:59<29:19:05, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1849/10016 [6:38:12<29:18:51, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1849/10016 [6:38:12<29:18:51, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1850/10016 [6:38:25<29:18:38, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1850/10016 [6:38:25<29:18:38, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1851/10016 [6:38:38<29:18:25, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1851/10016 [6:38:38<29:18:25, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1852/10016 [6:38:50<29:18:12, 12.92s/it, avr_loss=0.0236]
steps:  18%|█▊        | 1852/10016 [6:38:50<29:18:12, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▊        | 1853/10016 [6:39:03<29:17:59, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▊        | 1853/10016 [6:39:03<29:17:59, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▊        | 1854/10016 [6:39:16<29:17:46, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▊        | 1854/10016 [6:39:16<29:17:46, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▊        | 1855/10016 [6:39:29<29:17:33, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▊        | 1855/10016 [6:39:29<29:17:33, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▊        | 1856/10016 [6:39:42<29:17:20, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▊        | 1856/10016 [6:39:42<29:17:20, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▊        | 1857/10016 [6:39:55<29:17:07, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▊        | 1857/10016 [6:39:55<29:17:07, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▊        | 1858/10016 [6:40:08<29:16:53, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▊        | 1858/10016 [6:40:08<29:16:53, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▊        | 1859/10016 [6:40:21<29:16:40, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▊        | 1859/10016 [6:40:21<29:16:40, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▊        | 1860/10016 [6:40:33<29:16:27, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▊        | 1860/10016 [6:40:33<29:16:27, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▊        | 1861/10016 [6:40:46<29:16:14, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▊        | 1861/10016 [6:40:46<29:16:14, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▊        | 1862/10016 [6:40:59<29:16:01, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▊        | 1862/10016 [6:40:59<29:16:01, 12.92s/it, avr_loss=0.0233]
steps:  19%|█▊        | 1863/10016 [6:41:12<29:15:48, 12.92s/it, avr_loss=0.0233]
steps:  19%|█▊        | 1863/10016 [6:41:12<29:15:48, 12.92s/it, avr_loss=0.0233]
steps:  19%|█▊        | 1864/10016 [6:41:25<29:15:35, 12.92s/it, avr_loss=0.0233]
steps:  19%|█▊        | 1864/10016 [6:41:25<29:15:35, 12.92s/it, avr_loss=0.0232]
steps:  19%|█▊        | 1865/10016 [6:41:38<29:15:22, 12.92s/it, avr_loss=0.0232]
steps:  19%|█▊        | 1865/10016 [6:41:38<29:15:22, 12.92s/it, avr_loss=0.0232]
steps:  19%|█▊        | 1866/10016 [6:41:51<29:15:09, 12.92s/it, avr_loss=0.0232]
steps:  19%|█▊        | 1866/10016 [6:41:51<29:15:09, 12.92s/it, avr_loss=0.0232]
steps:  19%|█▊        | 1867/10016 [6:42:04<29:14:56, 12.92s/it, avr_loss=0.0232]
steps:  19%|█▊        | 1867/10016 [6:42:04<29:14:56, 12.92s/it, avr_loss=0.0232]
steps:  19%|█▊        | 1868/10016 [6:42:17<29:14:43, 12.92s/it, avr_loss=0.0232]
steps:  19%|█▊        | 1868/10016 [6:42:17<29:14:43, 12.92s/it, avr_loss=0.0232]
steps:  19%|█▊        | 1869/10016 [6:42:30<29:14:30, 12.92s/it, avr_loss=0.0232]
steps:  19%|█▊        | 1869/10016 [6:42:30<29:14:30, 12.92s/it, avr_loss=0.0233]
steps:  19%|█▊        | 1870/10016 [6:42:42<29:14:17, 12.92s/it, avr_loss=0.0233]
steps:  19%|█▊        | 1870/10016 [6:42:42<29:14:17, 12.92s/it, avr_loss=0.0233]
steps:  19%|█▊        | 1871/10016 [6:42:55<29:14:04, 12.92s/it, avr_loss=0.0233]
steps:  19%|█▊        | 1871/10016 [6:42:55<29:14:04, 12.92s/it, avr_loss=0.0233]
steps:  19%|█▊        | 1872/10016 [6:43:08<29:13:51, 12.92s/it, avr_loss=0.0233]
steps:  19%|█▊        | 1872/10016 [6:43:08<29:13:51, 12.92s/it, avr_loss=0.0233]
steps:  19%|█▊        | 1873/10016 [6:43:21<29:13:38, 12.92s/it, avr_loss=0.0233]
steps:  19%|█▊        | 1873/10016 [6:43:21<29:13:38, 12.92s/it, avr_loss=0.0233]
steps:  19%|█▊        | 1874/10016 [6:43:34<29:13:25, 12.92s/it, avr_loss=0.0233]
steps:  19%|█▊        | 1874/10016 [6:43:34<29:13:25, 12.92s/it, avr_loss=0.0234]
steps:  19%|█▊        | 1875/10016 [6:43:47<29:13:12, 12.92s/it, avr_loss=0.0234]
steps:  19%|█▊        | 1875/10016 [6:43:47<29:13:12, 12.92s/it, avr_loss=0.0234]
steps:  19%|█▊        | 1876/10016 [6:44:00<29:12:58, 12.92s/it, avr_loss=0.0234]
steps:  19%|█▊        | 1876/10016 [6:44:00<29:12:58, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▊        | 1877/10016 [6:44:13<29:12:45, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▊        | 1877/10016 [6:44:13<29:12:45, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1878/10016 [6:44:26<29:12:32, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1878/10016 [6:44:26<29:12:32, 12.92s/it, avr_loss=0.0235]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/loss-curve-retrain/train_lora-000003.safetensors

epoch 4/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4

steps:  19%|█▉        | 1879/10016 [6:44:40<29:12:27, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1879/10016 [6:44:40<29:12:27, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1880/10016 [6:44:53<29:12:14, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1880/10016 [6:44:53<29:12:14, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1881/10016 [6:45:06<29:12:01, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1881/10016 [6:45:06<29:12:01, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1882/10016 [6:45:19<29:11:48, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1882/10016 [6:45:19<29:11:48, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1883/10016 [6:45:32<29:11:35, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1883/10016 [6:45:32<29:11:35, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1884/10016 [6:45:45<29:11:22, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1884/10016 [6:45:45<29:11:22, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1885/10016 [6:45:58<29:11:09, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1885/10016 [6:45:58<29:11:09, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1886/10016 [6:46:11<29:10:56, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1886/10016 [6:46:11<29:10:56, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1887/10016 [6:46:23<29:10:43, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1887/10016 [6:46:23<29:10:43, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1888/10016 [6:46:36<29:10:30, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1888/10016 [6:46:36<29:10:30, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1889/10016 [6:46:49<29:10:17, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1889/10016 [6:46:49<29:10:17, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1890/10016 [6:47:02<29:10:04, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1890/10016 [6:47:02<29:10:04, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1891/10016 [6:47:15<29:09:51, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1891/10016 [6:47:15<29:09:51, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1892/10016 [6:47:28<29:09:38, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1892/10016 [6:47:28<29:09:38, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1893/10016 [6:47:41<29:09:24, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1893/10016 [6:47:41<29:09:24, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1894/10016 [6:47:54<29:09:11, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1894/10016 [6:47:54<29:09:11, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1895/10016 [6:48:07<29:08:58, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1895/10016 [6:48:07<29:08:58, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1896/10016 [6:48:19<29:08:45, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1896/10016 [6:48:19<29:08:45, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1897/10016 [6:48:32<29:08:32, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1897/10016 [6:48:32<29:08:32, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1898/10016 [6:48:45<29:08:19, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1898/10016 [6:48:45<29:08:19, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1899/10016 [6:48:58<29:08:06, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1899/10016 [6:48:58<29:08:06, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1900/10016 [6:49:11<29:07:53, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1900/10016 [6:49:11<29:07:53, 12.92s/it, avr_loss=0.0234]
steps:  19%|█▉        | 1901/10016 [6:49:24<29:07:39, 12.92s/it, avr_loss=0.0234]
steps:  19%|█▉        | 1901/10016 [6:49:24<29:07:39, 12.92s/it, avr_loss=0.0234]
steps:  19%|█▉        | 1902/10016 [6:49:37<29:07:26, 12.92s/it, avr_loss=0.0234]
steps:  19%|█▉        | 1902/10016 [6:49:37<29:07:26, 12.92s/it, avr_loss=0.0234]
steps:  19%|█▉        | 1903/10016 [6:49:49<29:07:13, 12.92s/it, avr_loss=0.0234]
steps:  19%|█▉        | 1903/10016 [6:49:49<29:07:13, 12.92s/it, avr_loss=0.0234]
steps:  19%|█▉        | 1904/10016 [6:50:02<29:07:00, 12.92s/it, avr_loss=0.0234]
steps:  19%|█▉        | 1904/10016 [6:50:02<29:07:00, 12.92s/it, avr_loss=0.0234]
steps:  19%|█▉        | 1905/10016 [6:50:15<29:06:47, 12.92s/it, avr_loss=0.0234]
steps:  19%|█▉        | 1905/10016 [6:50:15<29:06:47, 12.92s/it, avr_loss=0.0234]
steps:  19%|█▉        | 1906/10016 [6:50:28<29:06:34, 12.92s/it, avr_loss=0.0234]
steps:  19%|█▉        | 1906/10016 [6:50:28<29:06:34, 12.92s/it, avr_loss=0.0234]
steps:  19%|█▉        | 1907/10016 [6:50:41<29:06:21, 12.92s/it, avr_loss=0.0234]
steps:  19%|█▉        | 1907/10016 [6:50:41<29:06:21, 12.92s/it, avr_loss=0.0234]
steps:  19%|█▉        | 1908/10016 [6:50:54<29:06:08, 12.92s/it, avr_loss=0.0234]
steps:  19%|█▉        | 1908/10016 [6:50:54<29:06:08, 12.92s/it, avr_loss=0.0234]
steps:  19%|█▉        | 1909/10016 [6:51:07<29:05:55, 12.92s/it, avr_loss=0.0234]
steps:  19%|█▉        | 1909/10016 [6:51:07<29:05:55, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1910/10016 [6:51:20<29:05:42, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1910/10016 [6:51:20<29:05:42, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1911/10016 [6:51:33<29:05:28, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1911/10016 [6:51:33<29:05:28, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1912/10016 [6:51:45<29:05:15, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1912/10016 [6:51:45<29:05:15, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1913/10016 [6:51:58<29:05:02, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1913/10016 [6:51:58<29:05:02, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1914/10016 [6:52:11<29:04:49, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1914/10016 [6:52:11<29:04:49, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1915/10016 [6:52:24<29:04:36, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1915/10016 [6:52:24<29:04:36, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1916/10016 [6:52:37<29:04:23, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1916/10016 [6:52:37<29:04:23, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1917/10016 [6:52:50<29:04:10, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1917/10016 [6:52:50<29:04:10, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1918/10016 [6:53:03<29:03:57, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1918/10016 [6:53:03<29:03:57, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1919/10016 [6:53:16<29:03:44, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1919/10016 [6:53:16<29:03:44, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1920/10016 [6:53:28<29:03:31, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1920/10016 [6:53:28<29:03:31, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1921/10016 [6:53:41<29:03:17, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1921/10016 [6:53:41<29:03:17, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1922/10016 [6:53:54<29:03:04, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1922/10016 [6:53:54<29:03:04, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1923/10016 [6:54:07<29:02:51, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1923/10016 [6:54:07<29:02:51, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1924/10016 [6:54:20<29:02:38, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1924/10016 [6:54:20<29:02:38, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1925/10016 [6:54:33<29:02:25, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1925/10016 [6:54:33<29:02:25, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1926/10016 [6:54:46<29:02:12, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1926/10016 [6:54:46<29:02:12, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1927/10016 [6:54:59<29:01:59, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1927/10016 [6:54:59<29:01:59, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1928/10016 [6:55:11<29:01:46, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1928/10016 [6:55:11<29:01:46, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1929/10016 [6:55:24<29:01:33, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1929/10016 [6:55:24<29:01:33, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1930/10016 [6:55:37<29:01:20, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1930/10016 [6:55:37<29:01:20, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1931/10016 [6:55:50<29:01:07, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1931/10016 [6:55:50<29:01:07, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1932/10016 [6:56:03<29:00:54, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1932/10016 [6:56:03<29:00:54, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1933/10016 [6:56:16<29:00:40, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1933/10016 [6:56:16<29:00:40, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1934/10016 [6:56:29<29:00:27, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1934/10016 [6:56:29<29:00:27, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1935/10016 [6:56:42<29:00:14, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1935/10016 [6:56:42<29:00:14, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1936/10016 [6:56:55<29:00:01, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1936/10016 [6:56:55<29:00:01, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1937/10016 [6:57:07<28:59:48, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1937/10016 [6:57:07<28:59:48, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1938/10016 [6:57:20<28:59:35, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1938/10016 [6:57:20<28:59:35, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1939/10016 [6:57:33<28:59:22, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1939/10016 [6:57:33<28:59:22, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1940/10016 [6:57:46<28:59:09, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1940/10016 [6:57:46<28:59:09, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1941/10016 [6:57:59<28:58:56, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1941/10016 [6:57:59<28:58:56, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1942/10016 [6:58:12<28:58:43, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1942/10016 [6:58:12<28:58:43, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1943/10016 [6:58:25<28:58:30, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1943/10016 [6:58:25<28:58:30, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1944/10016 [6:58:38<28:58:17, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1944/10016 [6:58:38<28:58:17, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1945/10016 [6:58:51<28:58:04, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1945/10016 [6:58:51<28:58:04, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1946/10016 [6:59:03<28:57:51, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1946/10016 [6:59:03<28:57:51, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1947/10016 [6:59:16<28:57:37, 12.92s/it, avr_loss=0.0235]
steps:  19%|█▉        | 1947/10016 [6:59:16<28:57:37, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1948/10016 [6:59:29<28:57:24, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1948/10016 [6:59:29<28:57:24, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1949/10016 [6:59:42<28:57:11, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1949/10016 [6:59:42<28:57:11, 12.92s/it, avr_loss=0.0237]
steps:  19%|█▉        | 1950/10016 [6:59:55<28:56:58, 12.92s/it, avr_loss=0.0237]
steps:  19%|█▉        | 1950/10016 [6:59:55<28:56:58, 12.92s/it, avr_loss=0.0237]
steps:  19%|█▉        | 1951/10016 [7:00:08<28:56:45, 12.92s/it, avr_loss=0.0237]
steps:  19%|█▉        | 1951/10016 [7:00:08<28:56:45, 12.92s/it, avr_loss=0.0237]
steps:  19%|█▉        | 1952/10016 [7:00:21<28:56:32, 12.92s/it, avr_loss=0.0237]
steps:  19%|█▉        | 1952/10016 [7:00:21<28:56:32, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1953/10016 [7:00:34<28:56:19, 12.92s/it, avr_loss=0.0236]
steps:  19%|█▉        | 1953/10016 [7:00:34<28:56:19, 12.92s/it, avr_loss=0.0236]
steps:  20%|█▉        | 1954/10016 [7:00:46<28:56:06, 12.92s/it, avr_loss=0.0236]
steps:  20%|█▉        | 1954/10016 [7:00:46<28:56:06, 12.92s/it, avr_loss=0.0236]
steps:  20%|█▉        | 1955/10016 [7:00:59<28:55:53, 12.92s/it, avr_loss=0.0236]
steps:  20%|█▉        | 1955/10016 [7:00:59<28:55:53, 12.92s/it, avr_loss=0.0236]
steps:  20%|█▉        | 1956/10016 [7:01:12<28:55:40, 12.92s/it, avr_loss=0.0236]
steps:  20%|█▉        | 1956/10016 [7:01:12<28:55:40, 12.92s/it, avr_loss=0.0236]
steps:  20%|█▉        | 1957/10016 [7:01:25<28:55:26, 12.92s/it, avr_loss=0.0236]
steps:  20%|█▉        | 1957/10016 [7:01:25<28:55:26, 12.92s/it, avr_loss=0.0237]
steps:  20%|█▉        | 1958/10016 [7:01:38<28:55:13, 12.92s/it, avr_loss=0.0237]
steps:  20%|█▉        | 1958/10016 [7:01:38<28:55:13, 12.92s/it, avr_loss=0.0237]
steps:  20%|█▉        | 1959/10016 [7:01:51<28:55:00, 12.92s/it, avr_loss=0.0237]
steps:  20%|█▉        | 1959/10016 [7:01:51<28:55:00, 12.92s/it, avr_loss=0.0237]
steps:  20%|█▉        | 1960/10016 [7:02:04<28:54:47, 12.92s/it, avr_loss=0.0237]
steps:  20%|█▉        | 1960/10016 [7:02:04<28:54:47, 12.92s/it, avr_loss=0.0237]
steps:  20%|█▉        | 1961/10016 [7:02:17<28:54:34, 12.92s/it, avr_loss=0.0237]
steps:  20%|█▉        | 1961/10016 [7:02:17<28:54:34, 12.92s/it, avr_loss=0.0237]
steps:  20%|█▉        | 1962/10016 [7:02:29<28:54:21, 12.92s/it, avr_loss=0.0237]
steps:  20%|█▉        | 1962/10016 [7:02:29<28:54:21, 12.92s/it, avr_loss=0.0237]
steps:  20%|█▉        | 1963/10016 [7:02:42<28:54:08, 12.92s/it, avr_loss=0.0237]
steps:  20%|█▉        | 1963/10016 [7:02:42<28:54:08, 12.92s/it, avr_loss=0.0237]
steps:  20%|█▉        | 1964/10016 [7:02:55<28:53:55, 12.92s/it, avr_loss=0.0237]
steps:  20%|█▉        | 1964/10016 [7:02:55<28:53:55, 12.92s/it, avr_loss=0.0237]
steps:  20%|█▉        | 1965/10016 [7:03:08<28:53:42, 12.92s/it, avr_loss=0.0237]
steps:  20%|█▉        | 1965/10016 [7:03:08<28:53:42, 12.92s/it, avr_loss=0.0237]
steps:  20%|█▉        | 1966/10016 [7:03:21<28:53:29, 12.92s/it, avr_loss=0.0237]
steps:  20%|█▉        | 1966/10016 [7:03:21<28:53:29, 12.92s/it, avr_loss=0.0237]
steps:  20%|█▉        | 1967/10016 [7:03:34<28:53:16, 12.92s/it, avr_loss=0.0237]
steps:  20%|█▉        | 1967/10016 [7:03:34<28:53:16, 12.92s/it, avr_loss=0.0237]
steps:  20%|█▉        | 1968/10016 [7:03:47<28:53:03, 12.92s/it, avr_loss=0.0237]
steps:  20%|█▉        | 1968/10016 [7:03:47<28:53:03, 12.92s/it, avr_loss=0.0237]
steps:  20%|█▉        | 1969/10016 [7:04:00<28:52:50, 12.92s/it, avr_loss=0.0237]
steps:  20%|█▉        | 1969/10016 [7:04:00<28:52:50, 12.92s/it, avr_loss=0.0237]
steps:  20%|█▉        | 1970/10016 [7:04:13<28:52:37, 12.92s/it, avr_loss=0.0237]
steps:  20%|█▉        | 1970/10016 [7:04:13<28:52:37, 12.92s/it, avr_loss=0.0237]
steps:  20%|█▉        | 1971/10016 [7:04:26<28:52:24, 12.92s/it, avr_loss=0.0237]
steps:  20%|█▉        | 1971/10016 [7:04:26<28:52:24, 12.92s/it, avr_loss=0.0239]
steps:  20%|█▉        | 1972/10016 [7:04:39<28:52:11, 12.92s/it, avr_loss=0.0239]
steps:  20%|█▉        | 1972/10016 [7:04:39<28:52:11, 12.92s/it, avr_loss=0.0239]
steps:  20%|█▉        | 1973/10016 [7:04:51<28:51:58, 12.92s/it, avr_loss=0.0239]
steps:  20%|█▉        | 1973/10016 [7:04:51<28:51:58, 12.92s/it, avr_loss=0.024] 
steps:  20%|█▉        | 1974/10016 [7:05:04<28:51:45, 12.92s/it, avr_loss=0.024]
steps:  20%|█▉        | 1974/10016 [7:05:04<28:51:45, 12.92s/it, avr_loss=0.024]
steps:  20%|█▉        | 1975/10016 [7:05:17<28:51:32, 12.92s/it, avr_loss=0.024]
steps:  20%|█▉        | 1975/10016 [7:05:17<28:51:32, 12.92s/it, avr_loss=0.024]
steps:  20%|█▉        | 1976/10016 [7:05:30<28:51:19, 12.92s/it, avr_loss=0.024]
steps:  20%|█▉        | 1976/10016 [7:05:30<28:51:19, 12.92s/it, avr_loss=0.0239]
steps:  20%|█▉        | 1977/10016 [7:05:43<28:51:06, 12.92s/it, avr_loss=0.0239]
steps:  20%|█▉        | 1977/10016 [7:05:43<28:51:06, 12.92s/it, avr_loss=0.024] 
steps:  20%|█▉        | 1978/10016 [7:05:56<28:50:52, 12.92s/it, avr_loss=0.024]
steps:  20%|█▉        | 1978/10016 [7:05:56<28:50:52, 12.92s/it, avr_loss=0.0239]
steps:  20%|█▉        | 1979/10016 [7:06:09<28:50:39, 12.92s/it, avr_loss=0.0239]
steps:  20%|█▉        | 1979/10016 [7:06:09<28:50:39, 12.92s/it, avr_loss=0.0239]
steps:  20%|█▉        | 1980/10016 [7:06:21<28:50:26, 12.92s/it, avr_loss=0.0239]
steps:  20%|█▉        | 1980/10016 [7:06:21<28:50:26, 12.92s/it, avr_loss=0.0239]
steps:  20%|█▉        | 1981/10016 [7:06:34<28:50:13, 12.92s/it, avr_loss=0.0239]
steps:  20%|█▉        | 1981/10016 [7:06:34<28:50:13, 12.92s/it, avr_loss=0.024] 
steps:  20%|█▉        | 1982/10016 [7:06:47<28:50:00, 12.92s/it, avr_loss=0.024]
steps:  20%|█▉        | 1982/10016 [7:06:47<28:50:00, 12.92s/it, avr_loss=0.024]
steps:  20%|█▉        | 1983/10016 [7:07:00<28:49:47, 12.92s/it, avr_loss=0.024]
steps:  20%|█▉        | 1983/10016 [7:07:00<28:49:47, 12.92s/it, avr_loss=0.024]
steps:  20%|█▉        | 1984/10016 [7:07:13<28:49:34, 12.92s/it, avr_loss=0.024]
steps:  20%|█▉        | 1984/10016 [7:07:13<28:49:34, 12.92s/it, avr_loss=0.024]
steps:  20%|█▉        | 1985/10016 [7:07:26<28:49:21, 12.92s/it, avr_loss=0.024]
steps:  20%|█▉        | 1985/10016 [7:07:26<28:49:21, 12.92s/it, avr_loss=0.024]
steps:  20%|█▉        | 1986/10016 [7:07:39<28:49:07, 12.92s/it, avr_loss=0.024]
steps:  20%|█▉        | 1986/10016 [7:07:39<28:49:07, 12.92s/it, avr_loss=0.0239]
steps:  20%|█▉        | 1987/10016 [7:07:52<28:48:54, 12.92s/it, avr_loss=0.0239]
steps:  20%|█▉        | 1987/10016 [7:07:52<28:48:54, 12.92s/it, avr_loss=0.0239]
steps:  20%|█▉        | 1988/10016 [7:08:04<28:48:41, 12.92s/it, avr_loss=0.0239]
steps:  20%|█▉        | 1988/10016 [7:08:04<28:48:41, 12.92s/it, avr_loss=0.0239]
steps:  20%|█▉        | 1989/10016 [7:08:17<28:48:28, 12.92s/it, avr_loss=0.0239]
steps:  20%|█▉        | 1989/10016 [7:08:17<28:48:28, 12.92s/it, avr_loss=0.0239]
steps:  20%|█▉        | 1990/10016 [7:08:30<28:48:15, 12.92s/it, avr_loss=0.0239]
steps:  20%|█▉        | 1990/10016 [7:08:30<28:48:15, 12.92s/it, avr_loss=0.0239]
steps:  20%|█▉        | 1991/10016 [7:08:43<28:48:02, 12.92s/it, avr_loss=0.0239]
steps:  20%|█▉        | 1991/10016 [7:08:43<28:48:02, 12.92s/it, avr_loss=0.0239]
steps:  20%|█▉        | 1992/10016 [7:08:56<28:47:49, 12.92s/it, avr_loss=0.0239]
steps:  20%|█▉        | 1992/10016 [7:08:56<28:47:49, 12.92s/it, avr_loss=0.0239]
steps:  20%|█▉        | 1993/10016 [7:09:09<28:47:36, 12.92s/it, avr_loss=0.0239]
steps:  20%|█▉        | 1993/10016 [7:09:09<28:47:36, 12.92s/it, avr_loss=0.0241]
steps:  20%|█▉        | 1994/10016 [7:09:22<28:47:23, 12.92s/it, avr_loss=0.0241]
steps:  20%|█▉        | 1994/10016 [7:09:22<28:47:23, 12.92s/it, avr_loss=0.0242]
steps:  20%|█▉        | 1995/10016 [7:09:35<28:47:09, 12.92s/it, avr_loss=0.0242]
steps:  20%|█▉        | 1995/10016 [7:09:35<28:47:09, 12.92s/it, avr_loss=0.0242]
steps:  20%|█▉        | 1996/10016 [7:09:47<28:46:56, 12.92s/it, avr_loss=0.0242]
steps:  20%|█▉        | 1996/10016 [7:09:47<28:46:56, 12.92s/it, avr_loss=0.0242]
steps:  20%|█▉        | 1997/10016 [7:10:00<28:46:43, 12.92s/it, avr_loss=0.0242]
steps:  20%|█▉        | 1997/10016 [7:10:00<28:46:43, 12.92s/it, avr_loss=0.0242]
steps:  20%|█▉        | 1998/10016 [7:10:13<28:46:30, 12.92s/it, avr_loss=0.0242]
steps:  20%|█▉        | 1998/10016 [7:10:13<28:46:30, 12.92s/it, avr_loss=0.0241]
steps:  20%|█▉        | 1999/10016 [7:10:26<28:46:17, 12.92s/it, avr_loss=0.0241]
steps:  20%|█▉        | 1999/10016 [7:10:26<28:46:17, 12.92s/it, avr_loss=0.0243]
steps:  20%|█▉        | 2000/10016 [7:10:39<28:46:04, 12.92s/it, avr_loss=0.0243]
steps:  20%|█▉        | 2000/10016 [7:10:39<28:46:04, 12.92s/it, avr_loss=0.0243]
steps:  20%|█▉        | 2001/10016 [7:10:52<28:45:51, 12.92s/it, avr_loss=0.0243]
steps:  20%|█▉        | 2001/10016 [7:10:52<28:45:51, 12.92s/it, avr_loss=0.0243]
steps:  20%|█▉        | 2002/10016 [7:11:05<28:45:38, 12.92s/it, avr_loss=0.0243]
steps:  20%|█▉        | 2002/10016 [7:11:05<28:45:38, 12.92s/it, avr_loss=0.0243]
steps:  20%|█▉        | 2003/10016 [7:11:18<28:45:25, 12.92s/it, avr_loss=0.0243]
steps:  20%|█▉        | 2003/10016 [7:11:18<28:45:25, 12.92s/it, avr_loss=0.0243]
steps:  20%|██        | 2004/10016 [7:11:30<28:45:12, 12.92s/it, avr_loss=0.0243]
steps:  20%|██        | 2004/10016 [7:11:30<28:45:12, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2005/10016 [7:11:43<28:44:59, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2005/10016 [7:11:43<28:44:59, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2006/10016 [7:11:56<28:44:46, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2006/10016 [7:11:56<28:44:46, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2007/10016 [7:12:09<28:44:33, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2007/10016 [7:12:09<28:44:33, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2008/10016 [7:12:22<28:44:20, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2008/10016 [7:12:22<28:44:20, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2009/10016 [7:12:35<28:44:06, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2009/10016 [7:12:35<28:44:06, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2010/10016 [7:12:48<28:43:53, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2010/10016 [7:12:48<28:43:53, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2011/10016 [7:13:01<28:43:40, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2011/10016 [7:13:01<28:43:40, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2012/10016 [7:13:14<28:43:27, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2012/10016 [7:13:14<28:43:27, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2013/10016 [7:13:26<28:43:14, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2013/10016 [7:13:26<28:43:14, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2014/10016 [7:13:39<28:43:01, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2014/10016 [7:13:39<28:43:01, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2015/10016 [7:13:52<28:42:48, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2015/10016 [7:13:52<28:42:48, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2016/10016 [7:14:05<28:42:35, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2016/10016 [7:14:05<28:42:35, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2017/10016 [7:14:18<28:42:22, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2017/10016 [7:14:18<28:42:22, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2018/10016 [7:14:31<28:42:09, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2018/10016 [7:14:31<28:42:09, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2019/10016 [7:14:44<28:41:56, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2019/10016 [7:14:44<28:41:56, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2020/10016 [7:14:57<28:41:43, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2020/10016 [7:14:57<28:41:43, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2021/10016 [7:15:09<28:41:30, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2021/10016 [7:15:09<28:41:30, 12.92s/it, avr_loss=0.0241]
steps:  20%|██        | 2022/10016 [7:15:22<28:41:17, 12.92s/it, avr_loss=0.0241]
steps:  20%|██        | 2022/10016 [7:15:22<28:41:17, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2023/10016 [7:15:35<28:41:03, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2023/10016 [7:15:35<28:41:03, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2024/10016 [7:15:48<28:40:50, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2024/10016 [7:15:48<28:40:50, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2025/10016 [7:16:01<28:40:37, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2025/10016 [7:16:01<28:40:37, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2026/10016 [7:16:14<28:40:24, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2026/10016 [7:16:14<28:40:24, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2027/10016 [7:16:27<28:40:11, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2027/10016 [7:16:27<28:40:11, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2028/10016 [7:16:40<28:39:58, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2028/10016 [7:16:40<28:39:58, 12.92s/it, avr_loss=0.0241]
steps:  20%|██        | 2029/10016 [7:16:53<28:39:45, 12.92s/it, avr_loss=0.0241]
steps:  20%|██        | 2029/10016 [7:16:53<28:39:45, 12.92s/it, avr_loss=0.0241]
steps:  20%|██        | 2030/10016 [7:17:05<28:39:32, 12.92s/it, avr_loss=0.0241]
steps:  20%|██        | 2030/10016 [7:17:05<28:39:32, 12.92s/it, avr_loss=0.0241]
steps:  20%|██        | 2031/10016 [7:17:18<28:39:19, 12.92s/it, avr_loss=0.0241]
steps:  20%|██        | 2031/10016 [7:17:18<28:39:19, 12.92s/it, avr_loss=0.0241]
steps:  20%|██        | 2032/10016 [7:17:31<28:39:06, 12.92s/it, avr_loss=0.0241]
steps:  20%|██        | 2032/10016 [7:17:31<28:39:06, 12.92s/it, avr_loss=0.0241]
steps:  20%|██        | 2033/10016 [7:17:44<28:38:53, 12.92s/it, avr_loss=0.0241]
steps:  20%|██        | 2033/10016 [7:17:44<28:38:53, 12.92s/it, avr_loss=0.0241]
steps:  20%|██        | 2034/10016 [7:17:57<28:38:40, 12.92s/it, avr_loss=0.0241]
steps:  20%|██        | 2034/10016 [7:17:57<28:38:40, 12.92s/it, avr_loss=0.0241]
steps:  20%|██        | 2035/10016 [7:18:10<28:38:27, 12.92s/it, avr_loss=0.0241]
steps:  20%|██        | 2035/10016 [7:18:10<28:38:27, 12.92s/it, avr_loss=0.0241]
steps:  20%|██        | 2036/10016 [7:18:23<28:38:14, 12.92s/it, avr_loss=0.0241]
steps:  20%|██        | 2036/10016 [7:18:23<28:38:14, 12.92s/it, avr_loss=0.0241]
steps:  20%|██        | 2037/10016 [7:18:36<28:38:01, 12.92s/it, avr_loss=0.0241]
steps:  20%|██        | 2037/10016 [7:18:36<28:38:01, 12.92s/it, avr_loss=0.024] 
steps:  20%|██        | 2038/10016 [7:18:48<28:37:47, 12.92s/it, avr_loss=0.024]
steps:  20%|██        | 2038/10016 [7:18:48<28:37:47, 12.92s/it, avr_loss=0.0241]
steps:  20%|██        | 2039/10016 [7:19:01<28:37:34, 12.92s/it, avr_loss=0.0241]
steps:  20%|██        | 2039/10016 [7:19:01<28:37:34, 12.92s/it, avr_loss=0.0241]
steps:  20%|██        | 2040/10016 [7:19:14<28:37:21, 12.92s/it, avr_loss=0.0241]
steps:  20%|██        | 2040/10016 [7:19:14<28:37:21, 12.92s/it, avr_loss=0.024] 
steps:  20%|██        | 2041/10016 [7:19:27<28:37:08, 12.92s/it, avr_loss=0.024]
steps:  20%|██        | 2041/10016 [7:19:27<28:37:08, 12.92s/it, avr_loss=0.024]
steps:  20%|██        | 2042/10016 [7:19:40<28:36:55, 12.92s/it, avr_loss=0.024]
steps:  20%|██        | 2042/10016 [7:19:40<28:36:55, 12.92s/it, avr_loss=0.024]
steps:  20%|██        | 2043/10016 [7:19:53<28:36:42, 12.92s/it, avr_loss=0.024]
steps:  20%|██        | 2043/10016 [7:19:53<28:36:42, 12.92s/it, avr_loss=0.024]
steps:  20%|██        | 2044/10016 [7:20:06<28:36:29, 12.92s/it, avr_loss=0.024]
steps:  20%|██        | 2044/10016 [7:20:06<28:36:29, 12.92s/it, avr_loss=0.024]
steps:  20%|██        | 2045/10016 [7:20:19<28:36:16, 12.92s/it, avr_loss=0.024]
steps:  20%|██        | 2045/10016 [7:20:19<28:36:16, 12.92s/it, avr_loss=0.0241]
steps:  20%|██        | 2046/10016 [7:20:32<28:36:03, 12.92s/it, avr_loss=0.0241]
steps:  20%|██        | 2046/10016 [7:20:32<28:36:03, 12.92s/it, avr_loss=0.0241]
steps:  20%|██        | 2047/10016 [7:20:44<28:35:50, 12.92s/it, avr_loss=0.0241]
steps:  20%|██        | 2047/10016 [7:20:44<28:35:50, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2048/10016 [7:20:57<28:35:37, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2048/10016 [7:20:57<28:35:37, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2049/10016 [7:21:10<28:35:24, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2049/10016 [7:21:10<28:35:24, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2050/10016 [7:21:23<28:35:11, 12.92s/it, avr_loss=0.0242]
steps:  20%|██        | 2050/10016 [7:21:23<28:35:11, 12.92s/it, avr_loss=0.0243]
steps:  20%|██        | 2051/10016 [7:21:36<28:34:58, 12.92s/it, avr_loss=0.0243]
steps:  20%|██        | 2051/10016 [7:21:36<28:34:58, 12.92s/it, avr_loss=0.0243]
steps:  20%|██        | 2052/10016 [7:21:49<28:34:45, 12.92s/it, avr_loss=0.0243]
steps:  20%|██        | 2052/10016 [7:21:49<28:34:45, 12.92s/it, avr_loss=0.0243]
steps:  20%|██        | 2053/10016 [7:22:02<28:34:32, 12.92s/it, avr_loss=0.0243]
steps:  20%|██        | 2053/10016 [7:22:02<28:34:32, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2054/10016 [7:22:15<28:34:18, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2054/10016 [7:22:15<28:34:18, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2055/10016 [7:22:27<28:34:05, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2055/10016 [7:22:27<28:34:05, 12.92s/it, avr_loss=0.0244]
steps:  21%|██        | 2056/10016 [7:22:40<28:33:52, 12.92s/it, avr_loss=0.0244]
steps:  21%|██        | 2056/10016 [7:22:40<28:33:52, 12.92s/it, avr_loss=0.0243]
steps:  21%|██        | 2057/10016 [7:22:53<28:33:39, 12.92s/it, avr_loss=0.0243]
steps:  21%|██        | 2057/10016 [7:22:53<28:33:39, 12.92s/it, avr_loss=0.0243]
steps:  21%|██        | 2058/10016 [7:23:06<28:33:26, 12.92s/it, avr_loss=0.0243]
steps:  21%|██        | 2058/10016 [7:23:06<28:33:26, 12.92s/it, avr_loss=0.0243]
steps:  21%|██        | 2059/10016 [7:23:19<28:33:13, 12.92s/it, avr_loss=0.0243]
steps:  21%|██        | 2059/10016 [7:23:19<28:33:13, 12.92s/it, avr_loss=0.0243]
steps:  21%|██        | 2060/10016 [7:23:32<28:33:00, 12.92s/it, avr_loss=0.0243]
steps:  21%|██        | 2060/10016 [7:23:32<28:33:00, 12.92s/it, avr_loss=0.0243]
steps:  21%|██        | 2061/10016 [7:23:45<28:32:47, 12.92s/it, avr_loss=0.0243]
steps:  21%|██        | 2061/10016 [7:23:45<28:32:47, 12.92s/it, avr_loss=0.0243]
steps:  21%|██        | 2062/10016 [7:23:58<28:32:34, 12.92s/it, avr_loss=0.0243]
steps:  21%|██        | 2062/10016 [7:23:58<28:32:34, 12.92s/it, avr_loss=0.0243]
steps:  21%|██        | 2063/10016 [7:24:11<28:32:21, 12.92s/it, avr_loss=0.0243]
steps:  21%|██        | 2063/10016 [7:24:11<28:32:21, 12.92s/it, avr_loss=0.0243]
steps:  21%|██        | 2064/10016 [7:24:23<28:32:08, 12.92s/it, avr_loss=0.0243]
steps:  21%|██        | 2064/10016 [7:24:23<28:32:08, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2065/10016 [7:24:36<28:31:55, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2065/10016 [7:24:36<28:31:55, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2066/10016 [7:24:49<28:31:42, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2066/10016 [7:24:49<28:31:42, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2067/10016 [7:25:02<28:31:29, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2067/10016 [7:25:02<28:31:29, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2068/10016 [7:25:15<28:31:16, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2068/10016 [7:25:15<28:31:16, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2069/10016 [7:25:28<28:31:03, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2069/10016 [7:25:28<28:31:03, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2070/10016 [7:25:41<28:30:50, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2070/10016 [7:25:41<28:30:50, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2071/10016 [7:25:54<28:30:37, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2071/10016 [7:25:54<28:30:37, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2072/10016 [7:26:06<28:30:24, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2072/10016 [7:26:06<28:30:24, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2073/10016 [7:26:19<28:30:10, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2073/10016 [7:26:19<28:30:10, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2074/10016 [7:26:32<28:29:57, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2074/10016 [7:26:32<28:29:57, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2075/10016 [7:26:45<28:29:44, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2075/10016 [7:26:45<28:29:44, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2076/10016 [7:26:58<28:29:31, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2076/10016 [7:26:58<28:29:31, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2077/10016 [7:27:11<28:29:18, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2077/10016 [7:27:11<28:29:18, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2078/10016 [7:27:24<28:29:05, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2078/10016 [7:27:24<28:29:05, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2079/10016 [7:27:37<28:28:52, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2079/10016 [7:27:37<28:28:52, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2080/10016 [7:27:50<28:28:39, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2080/10016 [7:27:50<28:28:39, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2081/10016 [7:28:03<28:28:27, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2081/10016 [7:28:03<28:28:27, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2082/10016 [7:28:15<28:28:13, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2082/10016 [7:28:15<28:28:13, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2083/10016 [7:28:28<28:28:00, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2083/10016 [7:28:28<28:28:00, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2084/10016 [7:28:41<28:27:47, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2084/10016 [7:28:41<28:27:47, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2085/10016 [7:28:54<28:27:34, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2085/10016 [7:28:54<28:27:34, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2086/10016 [7:29:07<28:27:21, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2086/10016 [7:29:07<28:27:21, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2087/10016 [7:29:20<28:27:08, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2087/10016 [7:29:20<28:27:08, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2088/10016 [7:29:33<28:26:55, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2088/10016 [7:29:33<28:26:55, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2089/10016 [7:29:46<28:26:42, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2089/10016 [7:29:46<28:26:42, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2090/10016 [7:29:58<28:26:29, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2090/10016 [7:29:58<28:26:29, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2091/10016 [7:30:11<28:26:16, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2091/10016 [7:30:11<28:26:16, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2092/10016 [7:30:24<28:26:03, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2092/10016 [7:30:24<28:26:03, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2093/10016 [7:30:37<28:25:50, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2093/10016 [7:30:37<28:25:50, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2094/10016 [7:30:50<28:25:37, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2094/10016 [7:30:50<28:25:37, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2095/10016 [7:31:03<28:25:24, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2095/10016 [7:31:03<28:25:24, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2096/10016 [7:31:16<28:25:11, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2096/10016 [7:31:16<28:25:11, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2097/10016 [7:31:29<28:24:57, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2097/10016 [7:31:29<28:24:57, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2098/10016 [7:31:42<28:24:44, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2098/10016 [7:31:42<28:24:44, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2099/10016 [7:31:54<28:24:31, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2099/10016 [7:31:54<28:24:31, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2100/10016 [7:32:07<28:24:18, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2100/10016 [7:32:07<28:24:18, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2101/10016 [7:32:20<28:24:05, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2101/10016 [7:32:20<28:24:05, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2102/10016 [7:32:33<28:23:52, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2102/10016 [7:32:33<28:23:52, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2103/10016 [7:32:46<28:23:39, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2103/10016 [7:32:46<28:23:39, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2104/10016 [7:32:59<28:23:26, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2104/10016 [7:32:59<28:23:26, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2105/10016 [7:33:12<28:23:13, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2105/10016 [7:33:12<28:23:13, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2106/10016 [7:33:25<28:23:00, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2106/10016 [7:33:25<28:23:00, 12.92s/it, avr_loss=0.0243]
steps:  21%|██        | 2107/10016 [7:33:37<28:22:47, 12.92s/it, avr_loss=0.0243]
steps:  21%|██        | 2107/10016 [7:33:37<28:22:47, 12.92s/it, avr_loss=0.0243]
steps:  21%|██        | 2108/10016 [7:33:50<28:22:34, 12.92s/it, avr_loss=0.0243]
steps:  21%|██        | 2108/10016 [7:33:50<28:22:34, 12.92s/it, avr_loss=0.0243]
steps:  21%|██        | 2109/10016 [7:34:03<28:22:21, 12.92s/it, avr_loss=0.0243]
steps:  21%|██        | 2109/10016 [7:34:03<28:22:21, 12.92s/it, avr_loss=0.0243]
steps:  21%|██        | 2110/10016 [7:34:16<28:22:08, 12.92s/it, avr_loss=0.0243]
steps:  21%|██        | 2110/10016 [7:34:16<28:22:08, 12.92s/it, avr_loss=0.0244]
steps:  21%|██        | 2111/10016 [7:34:29<28:21:55, 12.92s/it, avr_loss=0.0244]
steps:  21%|██        | 2111/10016 [7:34:29<28:21:55, 12.92s/it, avr_loss=0.0243]
steps:  21%|██        | 2112/10016 [7:34:42<28:21:41, 12.92s/it, avr_loss=0.0243]
steps:  21%|██        | 2112/10016 [7:34:42<28:21:41, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2113/10016 [7:34:55<28:21:28, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2113/10016 [7:34:55<28:21:28, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2114/10016 [7:35:08<28:21:15, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2114/10016 [7:35:08<28:21:15, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2115/10016 [7:35:20<28:21:02, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2115/10016 [7:35:20<28:21:02, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2116/10016 [7:35:33<28:20:49, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2116/10016 [7:35:33<28:20:49, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2117/10016 [7:35:46<28:20:36, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2117/10016 [7:35:46<28:20:36, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2118/10016 [7:35:59<28:20:23, 12.92s/it, avr_loss=0.0242]
steps:  21%|██        | 2118/10016 [7:35:59<28:20:23, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2119/10016 [7:36:12<28:20:10, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2119/10016 [7:36:12<28:20:10, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2120/10016 [7:36:25<28:19:57, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2120/10016 [7:36:25<28:19:57, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2121/10016 [7:36:38<28:19:44, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2121/10016 [7:36:38<28:19:44, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2122/10016 [7:36:51<28:19:31, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2122/10016 [7:36:51<28:19:31, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2123/10016 [7:37:04<28:19:18, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2123/10016 [7:37:04<28:19:18, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2124/10016 [7:37:16<28:19:05, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2124/10016 [7:37:16<28:19:05, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2125/10016 [7:37:29<28:18:52, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2125/10016 [7:37:29<28:18:52, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2126/10016 [7:37:42<28:18:39, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2126/10016 [7:37:42<28:18:39, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2127/10016 [7:37:55<28:18:26, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2127/10016 [7:37:55<28:18:26, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2128/10016 [7:38:08<28:18:13, 12.92s/it, avr_loss=0.0241]
steps:  21%|██        | 2128/10016 [7:38:08<28:18:13, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2129/10016 [7:38:21<28:18:00, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2129/10016 [7:38:21<28:18:00, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2130/10016 [7:38:34<28:17:47, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2130/10016 [7:38:34<28:17:47, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2131/10016 [7:38:47<28:17:34, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2131/10016 [7:38:47<28:17:34, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2132/10016 [7:39:00<28:17:21, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2132/10016 [7:39:00<28:17:21, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2133/10016 [7:39:12<28:17:08, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2133/10016 [7:39:12<28:17:08, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2134/10016 [7:39:25<28:16:55, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2134/10016 [7:39:25<28:16:55, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2135/10016 [7:39:38<28:16:42, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2135/10016 [7:39:38<28:16:42, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2136/10016 [7:39:51<28:16:29, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2136/10016 [7:39:51<28:16:29, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2137/10016 [7:40:04<28:16:16, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2137/10016 [7:40:04<28:16:16, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2138/10016 [7:40:17<28:16:03, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2138/10016 [7:40:17<28:16:03, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2139/10016 [7:40:30<28:15:50, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2139/10016 [7:40:30<28:15:50, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2140/10016 [7:40:43<28:15:37, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2140/10016 [7:40:43<28:15:37, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2141/10016 [7:40:55<28:15:23, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2141/10016 [7:40:55<28:15:23, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2142/10016 [7:41:08<28:15:10, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2142/10016 [7:41:08<28:15:10, 12.92s/it, avr_loss=0.024] 
steps:  21%|██▏       | 2143/10016 [7:41:21<28:14:57, 12.92s/it, avr_loss=0.024]
steps:  21%|██▏       | 2143/10016 [7:41:21<28:14:57, 12.92s/it, avr_loss=0.024]
steps:  21%|██▏       | 2144/10016 [7:41:34<28:14:44, 12.92s/it, avr_loss=0.024]
steps:  21%|██▏       | 2144/10016 [7:41:34<28:14:44, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2145/10016 [7:41:47<28:14:31, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2145/10016 [7:41:47<28:14:31, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2146/10016 [7:42:00<28:14:18, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2146/10016 [7:42:00<28:14:18, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2147/10016 [7:42:13<28:14:05, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2147/10016 [7:42:13<28:14:05, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2148/10016 [7:42:26<28:13:52, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2148/10016 [7:42:26<28:13:52, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2149/10016 [7:42:39<28:13:39, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2149/10016 [7:42:39<28:13:39, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2150/10016 [7:42:51<28:13:26, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2150/10016 [7:42:51<28:13:26, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2151/10016 [7:43:04<28:13:13, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2151/10016 [7:43:04<28:13:13, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2152/10016 [7:43:17<28:13:00, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2152/10016 [7:43:17<28:13:00, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2153/10016 [7:43:30<28:12:47, 12.92s/it, avr_loss=0.0241]
steps:  21%|██▏       | 2153/10016 [7:43:30<28:12:47, 12.92s/it, avr_loss=0.0241]
steps:  22%|██▏       | 2154/10016 [7:43:43<28:12:34, 12.92s/it, avr_loss=0.0241]
steps:  22%|██▏       | 2154/10016 [7:43:43<28:12:34, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2155/10016 [7:43:56<28:12:21, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2155/10016 [7:43:56<28:12:21, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2156/10016 [7:44:09<28:12:08, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2156/10016 [7:44:09<28:12:08, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2157/10016 [7:44:22<28:11:55, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2157/10016 [7:44:22<28:11:55, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2158/10016 [7:44:35<28:11:42, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2158/10016 [7:44:35<28:11:42, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2159/10016 [7:44:47<28:11:29, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2159/10016 [7:44:47<28:11:29, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2160/10016 [7:45:00<28:11:16, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2160/10016 [7:45:00<28:11:16, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2161/10016 [7:45:13<28:11:03, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2161/10016 [7:45:13<28:11:03, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2162/10016 [7:45:26<28:10:50, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2162/10016 [7:45:26<28:10:50, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2163/10016 [7:45:39<28:10:37, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2163/10016 [7:45:39<28:10:37, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2164/10016 [7:45:52<28:10:24, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2164/10016 [7:45:52<28:10:24, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2165/10016 [7:46:05<28:10:11, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2165/10016 [7:46:05<28:10:11, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2166/10016 [7:46:18<28:09:58, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2166/10016 [7:46:18<28:09:58, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2167/10016 [7:46:31<28:09:45, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2167/10016 [7:46:31<28:09:45, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2168/10016 [7:46:43<28:09:32, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2168/10016 [7:46:43<28:09:32, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2169/10016 [7:46:56<28:09:19, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2169/10016 [7:46:56<28:09:19, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2170/10016 [7:47:09<28:09:06, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2170/10016 [7:47:09<28:09:06, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2171/10016 [7:47:22<28:08:53, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2171/10016 [7:47:22<28:08:53, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2172/10016 [7:47:35<28:08:40, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2172/10016 [7:47:35<28:08:40, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2173/10016 [7:47:48<28:08:27, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2173/10016 [7:47:48<28:08:27, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2174/10016 [7:48:01<28:08:14, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2174/10016 [7:48:01<28:08:14, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2175/10016 [7:48:14<28:08:00, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2175/10016 [7:48:14<28:08:00, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2176/10016 [7:48:27<28:07:47, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2176/10016 [7:48:27<28:07:47, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2177/10016 [7:48:39<28:07:34, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2177/10016 [7:48:39<28:07:34, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2178/10016 [7:48:52<28:07:21, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2178/10016 [7:48:52<28:07:21, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2179/10016 [7:49:05<28:07:08, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2179/10016 [7:49:05<28:07:08, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2180/10016 [7:49:18<28:06:55, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2180/10016 [7:49:18<28:06:55, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2181/10016 [7:49:31<28:06:42, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2181/10016 [7:49:31<28:06:42, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2182/10016 [7:49:44<28:06:29, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2182/10016 [7:49:44<28:06:29, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2183/10016 [7:49:57<28:06:16, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2183/10016 [7:49:57<28:06:16, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2184/10016 [7:50:10<28:06:03, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2184/10016 [7:50:10<28:06:03, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2185/10016 [7:50:22<28:05:50, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2185/10016 [7:50:22<28:05:50, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2186/10016 [7:50:35<28:05:37, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2186/10016 [7:50:35<28:05:37, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2187/10016 [7:50:48<28:05:24, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2187/10016 [7:50:48<28:05:24, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2188/10016 [7:51:01<28:05:11, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2188/10016 [7:51:01<28:05:11, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2189/10016 [7:51:14<28:04:58, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2189/10016 [7:51:14<28:04:58, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2190/10016 [7:51:27<28:04:45, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2190/10016 [7:51:27<28:04:45, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2191/10016 [7:51:40<28:04:32, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2191/10016 [7:51:40<28:04:32, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2192/10016 [7:51:53<28:04:19, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2192/10016 [7:51:53<28:04:19, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2193/10016 [7:52:06<28:04:06, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2193/10016 [7:52:06<28:04:06, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2194/10016 [7:52:18<28:03:53, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2194/10016 [7:52:18<28:03:53, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2195/10016 [7:52:31<28:03:40, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2195/10016 [7:52:31<28:03:40, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2196/10016 [7:52:44<28:03:27, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2196/10016 [7:52:44<28:03:27, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2197/10016 [7:52:57<28:03:14, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2197/10016 [7:52:57<28:03:14, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2198/10016 [7:53:10<28:03:01, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2198/10016 [7:53:10<28:03:01, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2199/10016 [7:53:23<28:02:48, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2199/10016 [7:53:23<28:02:48, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2200/10016 [7:53:36<28:02:34, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2200/10016 [7:53:36<28:02:34, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2201/10016 [7:53:49<28:02:21, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2201/10016 [7:53:49<28:02:21, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2202/10016 [7:54:01<28:02:08, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2202/10016 [7:54:01<28:02:08, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2203/10016 [7:54:14<28:01:55, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2203/10016 [7:54:14<28:01:55, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2204/10016 [7:54:27<28:01:42, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2204/10016 [7:54:27<28:01:42, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2205/10016 [7:54:40<28:01:29, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2205/10016 [7:54:40<28:01:29, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2206/10016 [7:54:53<28:01:16, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2206/10016 [7:54:53<28:01:16, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2207/10016 [7:55:06<28:01:03, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2207/10016 [7:55:06<28:01:03, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2208/10016 [7:55:19<28:00:50, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2208/10016 [7:55:19<28:00:50, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2209/10016 [7:55:32<28:00:37, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2209/10016 [7:55:32<28:00:37, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2210/10016 [7:55:44<28:00:24, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2210/10016 [7:55:44<28:00:24, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2211/10016 [7:55:57<28:00:11, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2211/10016 [7:55:57<28:00:11, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2212/10016 [7:56:10<27:59:58, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2212/10016 [7:56:10<27:59:58, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2213/10016 [7:56:23<27:59:45, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2213/10016 [7:56:23<27:59:45, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2214/10016 [7:56:36<27:59:32, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2214/10016 [7:56:36<27:59:32, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2215/10016 [7:56:49<27:59:19, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2215/10016 [7:56:49<27:59:19, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2216/10016 [7:57:02<27:59:06, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2216/10016 [7:57:02<27:59:06, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2217/10016 [7:57:15<27:58:52, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2217/10016 [7:57:15<27:58:52, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2218/10016 [7:57:27<27:58:39, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2218/10016 [7:57:27<27:58:39, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2219/10016 [7:57:40<27:58:26, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2219/10016 [7:57:40<27:58:26, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2220/10016 [7:57:53<27:58:13, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2220/10016 [7:57:53<27:58:13, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2221/10016 [7:58:06<27:58:00, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2221/10016 [7:58:06<27:58:00, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2222/10016 [7:58:19<27:57:47, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2222/10016 [7:58:19<27:57:47, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2223/10016 [7:58:32<27:57:34, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2223/10016 [7:58:32<27:57:34, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2224/10016 [7:58:45<27:57:21, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2224/10016 [7:58:45<27:57:21, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2225/10016 [7:58:58<27:57:08, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2225/10016 [7:58:58<27:57:08, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2226/10016 [7:59:11<27:56:55, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2226/10016 [7:59:11<27:56:55, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2227/10016 [7:59:24<27:56:42, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2227/10016 [7:59:24<27:56:42, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2228/10016 [7:59:36<27:56:29, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2228/10016 [7:59:36<27:56:29, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2229/10016 [7:59:49<27:56:16, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2229/10016 [7:59:49<27:56:16, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2230/10016 [8:00:02<27:56:03, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2230/10016 [8:00:02<27:56:03, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2231/10016 [8:00:15<27:55:50, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2231/10016 [8:00:15<27:55:50, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2232/10016 [8:00:28<27:55:37, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2232/10016 [8:00:28<27:55:37, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2233/10016 [8:00:41<27:55:24, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2233/10016 [8:00:41<27:55:24, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2234/10016 [8:00:54<27:55:11, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2234/10016 [8:00:54<27:55:11, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2235/10016 [8:01:07<27:54:58, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2235/10016 [8:01:07<27:54:58, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2236/10016 [8:01:19<27:54:45, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2236/10016 [8:01:19<27:54:45, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2237/10016 [8:01:32<27:54:32, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2237/10016 [8:01:32<27:54:32, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2238/10016 [8:01:45<27:54:19, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2238/10016 [8:01:45<27:54:19, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2239/10016 [8:01:58<27:54:06, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2239/10016 [8:01:58<27:54:06, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2240/10016 [8:02:11<27:53:53, 12.92s/it, avr_loss=0.0244]
steps:  22%|██▏       | 2240/10016 [8:02:11<27:53:53, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2241/10016 [8:02:24<27:53:40, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2241/10016 [8:02:24<27:53:40, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2242/10016 [8:02:37<27:53:27, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2242/10016 [8:02:37<27:53:27, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2243/10016 [8:02:50<27:53:14, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2243/10016 [8:02:50<27:53:14, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2244/10016 [8:03:03<27:53:01, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2244/10016 [8:03:03<27:53:01, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2245/10016 [8:03:15<27:52:48, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2245/10016 [8:03:15<27:52:48, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2246/10016 [8:03:28<27:52:35, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2246/10016 [8:03:28<27:52:35, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2247/10016 [8:03:41<27:52:22, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2247/10016 [8:03:41<27:52:22, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2248/10016 [8:03:54<27:52:09, 12.92s/it, avr_loss=0.0243]
steps:  22%|██▏       | 2248/10016 [8:03:54<27:52:09, 12.92s/it, avr_loss=0.0242]
steps:  22%|██▏       | 2249/10016 [8:04:07<27:51:56, 12.92s/it, avr_loss=0.0242]
steps:  22%|██▏       | 2249/10016 [8:04:07<27:51:56, 12.92s/it, avr_loss=0.0242]
steps:  22%|██▏       | 2250/10016 [8:04:20<27:51:43, 12.92s/it, avr_loss=0.0242]
steps:  22%|██▏       | 2250/10016 [8:04:20<27:51:43, 12.92s/it, avr_loss=0.0241]
steps:  22%|██▏       | 2251/10016 [8:04:33<27:51:30, 12.92s/it, avr_loss=0.0241]
steps:  22%|██▏       | 2251/10016 [8:04:33<27:51:30, 12.92s/it, avr_loss=0.0241]
steps:  22%|██▏       | 2252/10016 [8:04:46<27:51:17, 12.92s/it, avr_loss=0.0241]
steps:  22%|██▏       | 2252/10016 [8:04:46<27:51:17, 12.92s/it, avr_loss=0.0241]
steps:  22%|██▏       | 2253/10016 [8:04:59<27:51:04, 12.92s/it, avr_loss=0.0241]
steps:  22%|██▏       | 2253/10016 [8:04:59<27:51:04, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2254/10016 [8:05:12<27:50:51, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2254/10016 [8:05:12<27:50:51, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2255/10016 [8:05:24<27:50:38, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2255/10016 [8:05:24<27:50:38, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2256/10016 [8:05:37<27:50:25, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2256/10016 [8:05:37<27:50:25, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2257/10016 [8:05:50<27:50:12, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2257/10016 [8:05:50<27:50:12, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2258/10016 [8:06:03<27:49:59, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2258/10016 [8:06:03<27:49:59, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2259/10016 [8:06:16<27:49:46, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2259/10016 [8:06:16<27:49:46, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2260/10016 [8:06:29<27:49:33, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2260/10016 [8:06:29<27:49:33, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2261/10016 [8:06:42<27:49:20, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2261/10016 [8:06:42<27:49:20, 12.92s/it, avr_loss=0.024] 
steps:  23%|██▎       | 2262/10016 [8:06:55<27:49:07, 12.92s/it, avr_loss=0.024]
steps:  23%|██▎       | 2262/10016 [8:06:55<27:49:07, 12.92s/it, avr_loss=0.024]
steps:  23%|██▎       | 2263/10016 [8:07:08<27:48:54, 12.92s/it, avr_loss=0.024]
steps:  23%|██▎       | 2263/10016 [8:07:08<27:48:54, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2264/10016 [8:07:20<27:48:41, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2264/10016 [8:07:20<27:48:41, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2265/10016 [8:07:33<27:48:28, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2265/10016 [8:07:33<27:48:28, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2266/10016 [8:07:46<27:48:15, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2266/10016 [8:07:46<27:48:15, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2267/10016 [8:07:59<27:48:02, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2267/10016 [8:07:59<27:48:02, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2268/10016 [8:08:12<27:47:49, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2268/10016 [8:08:12<27:47:49, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2269/10016 [8:08:25<27:47:36, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2269/10016 [8:08:25<27:47:36, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2270/10016 [8:08:38<27:47:23, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2270/10016 [8:08:38<27:47:23, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2271/10016 [8:08:51<27:47:10, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2271/10016 [8:08:51<27:47:10, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2272/10016 [8:09:04<27:46:57, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2272/10016 [8:09:04<27:46:57, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2273/10016 [8:09:17<27:46:44, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2273/10016 [8:09:17<27:46:44, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2274/10016 [8:09:29<27:46:32, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2274/10016 [8:09:29<27:46:32, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2275/10016 [8:09:42<27:46:19, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2275/10016 [8:09:42<27:46:19, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2276/10016 [8:09:55<27:46:06, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2276/10016 [8:09:55<27:46:06, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2277/10016 [8:10:08<27:45:53, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2277/10016 [8:10:08<27:45:53, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2278/10016 [8:10:21<27:45:40, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2278/10016 [8:10:21<27:45:40, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2279/10016 [8:10:34<27:45:27, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2279/10016 [8:10:34<27:45:27, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2280/10016 [8:10:47<27:45:14, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2280/10016 [8:10:47<27:45:14, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2281/10016 [8:11:00<27:45:01, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2281/10016 [8:11:00<27:45:01, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2282/10016 [8:11:13<27:44:48, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2282/10016 [8:11:13<27:44:48, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2283/10016 [8:11:25<27:44:35, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2283/10016 [8:11:25<27:44:35, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2284/10016 [8:11:38<27:44:21, 12.92s/it, avr_loss=0.0241]
steps:  23%|██▎       | 2284/10016 [8:11:38<27:44:21, 12.92s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2285/10016 [8:11:51<27:44:08, 12.92s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2285/10016 [8:11:51<27:44:08, 12.92s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2286/10016 [8:12:04<27:43:55, 12.92s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2286/10016 [8:12:04<27:43:55, 12.92s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2287/10016 [8:12:17<27:43:42, 12.92s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2287/10016 [8:12:17<27:43:42, 12.92s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2288/10016 [8:12:30<27:43:29, 12.92s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2288/10016 [8:12:30<27:43:29, 12.92s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2289/10016 [8:12:43<27:43:17, 12.92s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2289/10016 [8:12:43<27:43:17, 12.92s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2290/10016 [8:12:56<27:43:04, 12.92s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2290/10016 [8:12:56<27:43:04, 12.92s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2291/10016 [8:13:09<27:42:51, 12.92s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2291/10016 [8:13:09<27:42:51, 12.92s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2292/10016 [8:13:21<27:42:38, 12.92s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2292/10016 [8:13:21<27:42:38, 12.92s/it, avr_loss=0.0243]
steps:  23%|██▎       | 2293/10016 [8:13:34<27:42:25, 12.92s/it, avr_loss=0.0243]
steps:  23%|██▎       | 2293/10016 [8:13:34<27:42:25, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2294/10016 [8:13:47<27:42:12, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2294/10016 [8:13:47<27:42:12, 12.92s/it, avr_loss=0.0243]
steps:  23%|██▎       | 2295/10016 [8:14:00<27:41:59, 12.92s/it, avr_loss=0.0243]
steps:  23%|██▎       | 2295/10016 [8:14:00<27:41:59, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2296/10016 [8:14:13<27:41:46, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2296/10016 [8:14:13<27:41:46, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2297/10016 [8:14:26<27:41:33, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2297/10016 [8:14:26<27:41:33, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2298/10016 [8:14:39<27:41:20, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2298/10016 [8:14:39<27:41:20, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2299/10016 [8:14:52<27:41:07, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2299/10016 [8:14:52<27:41:07, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2300/10016 [8:15:05<27:40:54, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2300/10016 [8:15:05<27:40:54, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2301/10016 [8:15:18<27:40:41, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2301/10016 [8:15:18<27:40:41, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2302/10016 [8:15:30<27:40:28, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2302/10016 [8:15:30<27:40:28, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2303/10016 [8:15:43<27:40:15, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2303/10016 [8:15:43<27:40:15, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2304/10016 [8:15:56<27:40:02, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2304/10016 [8:15:56<27:40:02, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2305/10016 [8:16:09<27:39:49, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2305/10016 [8:16:09<27:39:49, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2306/10016 [8:16:22<27:39:36, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2306/10016 [8:16:22<27:39:36, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2307/10016 [8:16:35<27:39:23, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2307/10016 [8:16:35<27:39:23, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2308/10016 [8:16:48<27:39:09, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2308/10016 [8:16:48<27:39:09, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2309/10016 [8:17:01<27:38:56, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2309/10016 [8:17:01<27:38:56, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2310/10016 [8:17:13<27:38:43, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2310/10016 [8:17:13<27:38:43, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2311/10016 [8:17:26<27:38:30, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2311/10016 [8:17:26<27:38:30, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2312/10016 [8:17:39<27:38:17, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2312/10016 [8:17:39<27:38:17, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2313/10016 [8:17:52<27:38:04, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2313/10016 [8:17:52<27:38:04, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2314/10016 [8:18:05<27:37:51, 12.92s/it, avr_loss=0.0242]
steps:  23%|██▎       | 2314/10016 [8:18:05<27:37:51, 12.92s/it, avr_loss=0.0245]
steps:  23%|██▎       | 2315/10016 [8:18:18<27:37:38, 12.92s/it, avr_loss=0.0245]
steps:  23%|██▎       | 2315/10016 [8:18:18<27:37:38, 12.92s/it, avr_loss=0.0245]
steps:  23%|██▎       | 2316/10016 [8:18:31<27:37:25, 12.91s/it, avr_loss=0.0245]
steps:  23%|██▎       | 2316/10016 [8:18:31<27:37:25, 12.91s/it, avr_loss=0.0245]
steps:  23%|██▎       | 2317/10016 [8:18:44<27:37:12, 12.91s/it, avr_loss=0.0245]
steps:  23%|██▎       | 2317/10016 [8:18:44<27:37:12, 12.91s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2318/10016 [8:18:56<27:36:59, 12.91s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2318/10016 [8:18:56<27:36:59, 12.91s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2319/10016 [8:19:09<27:36:46, 12.91s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2319/10016 [8:19:09<27:36:46, 12.91s/it, avr_loss=0.0245]
steps:  23%|██▎       | 2320/10016 [8:19:22<27:36:33, 12.91s/it, avr_loss=0.0245]
steps:  23%|██▎       | 2320/10016 [8:19:22<27:36:33, 12.91s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2321/10016 [8:19:35<27:36:20, 12.91s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2321/10016 [8:19:35<27:36:20, 12.91s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2322/10016 [8:19:48<27:36:07, 12.91s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2322/10016 [8:19:48<27:36:07, 12.91s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2323/10016 [8:20:01<27:35:54, 12.91s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2323/10016 [8:20:01<27:35:54, 12.91s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2324/10016 [8:20:14<27:35:40, 12.91s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2324/10016 [8:20:14<27:35:40, 12.91s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2325/10016 [8:20:26<27:35:27, 12.91s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2325/10016 [8:20:26<27:35:27, 12.91s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2326/10016 [8:20:39<27:35:14, 12.91s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2326/10016 [8:20:39<27:35:14, 12.91s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2327/10016 [8:20:52<27:35:01, 12.91s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2327/10016 [8:20:52<27:35:01, 12.91s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2328/10016 [8:21:05<27:34:48, 12.91s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2328/10016 [8:21:05<27:34:48, 12.91s/it, avr_loss=0.0243]
steps:  23%|██▎       | 2329/10016 [8:21:18<27:34:35, 12.91s/it, avr_loss=0.0243]
steps:  23%|██▎       | 2329/10016 [8:21:18<27:34:35, 12.91s/it, avr_loss=0.0243]
steps:  23%|██▎       | 2330/10016 [8:21:31<27:34:22, 12.91s/it, avr_loss=0.0243]
steps:  23%|██▎       | 2330/10016 [8:21:31<27:34:22, 12.91s/it, avr_loss=0.0243]
steps:  23%|██▎       | 2331/10016 [8:21:44<27:34:09, 12.91s/it, avr_loss=0.0243]
steps:  23%|██▎       | 2331/10016 [8:21:44<27:34:09, 12.91s/it, avr_loss=0.0243]
steps:  23%|██▎       | 2332/10016 [8:21:57<27:33:56, 12.91s/it, avr_loss=0.0243]
steps:  23%|██▎       | 2332/10016 [8:21:57<27:33:56, 12.91s/it, avr_loss=0.0243]
steps:  23%|██▎       | 2333/10016 [8:22:09<27:33:43, 12.91s/it, avr_loss=0.0243]
steps:  23%|██▎       | 2333/10016 [8:22:09<27:33:43, 12.91s/it, avr_loss=0.0243]
steps:  23%|██▎       | 2334/10016 [8:22:22<27:33:30, 12.91s/it, avr_loss=0.0243]
steps:  23%|██▎       | 2334/10016 [8:22:22<27:33:30, 12.91s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2335/10016 [8:22:35<27:33:17, 12.91s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2335/10016 [8:22:35<27:33:17, 12.91s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2336/10016 [8:22:48<27:33:04, 12.91s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2336/10016 [8:22:48<27:33:04, 12.91s/it, avr_loss=0.0245]
steps:  23%|██▎       | 2337/10016 [8:23:01<27:32:51, 12.91s/it, avr_loss=0.0245]
steps:  23%|██▎       | 2337/10016 [8:23:01<27:32:51, 12.91s/it, avr_loss=0.0245]
steps:  23%|██▎       | 2338/10016 [8:23:14<27:32:38, 12.91s/it, avr_loss=0.0245]
steps:  23%|██▎       | 2338/10016 [8:23:14<27:32:38, 12.91s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2339/10016 [8:23:27<27:32:25, 12.91s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2339/10016 [8:23:27<27:32:25, 12.91s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2340/10016 [8:23:40<27:32:12, 12.91s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2340/10016 [8:23:40<27:32:12, 12.91s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2341/10016 [8:23:52<27:31:59, 12.91s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2341/10016 [8:23:52<27:31:59, 12.91s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2342/10016 [8:24:05<27:31:46, 12.91s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2342/10016 [8:24:05<27:31:46, 12.91s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2343/10016 [8:24:18<27:31:33, 12.91s/it, avr_loss=0.0244]
steps:  23%|██▎       | 2343/10016 [8:24:18<27:31:33, 12.91s/it, avr_loss=0.0243]
steps:  23%|██▎       | 2344/10016 [8:24:31<27:31:19, 12.91s/it, avr_loss=0.0243]
steps:  23%|██▎       | 2344/10016 [8:24:31<27:31:19, 12.91s/it, avr_loss=0.0243]
steps:  23%|██▎       | 2345/10016 [8:24:44<27:31:06, 12.91s/it, avr_loss=0.0243]
steps:  23%|██▎       | 2345/10016 [8:24:44<27:31:06, 12.91s/it, avr_loss=0.0243]
steps:  23%|██▎       | 2346/10016 [8:24:57<27:30:53, 12.91s/it, avr_loss=0.0243]
steps:  23%|██▎       | 2346/10016 [8:24:57<27:30:53, 12.91s/it, avr_loss=0.0243]
steps:  23%|██▎       | 2347/10016 [8:25:10<27:30:40, 12.91s/it, avr_loss=0.0243]
steps:  23%|██▎       | 2347/10016 [8:25:10<27:30:40, 12.91s/it, avr_loss=0.0243]
steps:  23%|██▎       | 2348/10016 [8:25:23<27:30:27, 12.91s/it, avr_loss=0.0243]
steps:  23%|██▎       | 2348/10016 [8:25:23<27:30:27, 12.91s/it, avr_loss=0.0246]
steps:  23%|██▎       | 2349/10016 [8:25:35<27:30:14, 12.91s/it, avr_loss=0.0246]
steps:  23%|██▎       | 2349/10016 [8:25:35<27:30:14, 12.91s/it, avr_loss=0.0246]
steps:  23%|██▎       | 2350/10016 [8:25:48<27:30:01, 12.91s/it, avr_loss=0.0246]
steps:  23%|██▎       | 2350/10016 [8:25:48<27:30:01, 12.91s/it, avr_loss=0.0246]
steps:  23%|██▎       | 2351/10016 [8:26:01<27:29:48, 12.91s/it, avr_loss=0.0246]
steps:  23%|██▎       | 2351/10016 [8:26:01<27:29:48, 12.91s/it, avr_loss=0.0246]
steps:  23%|██▎       | 2352/10016 [8:26:14<27:29:35, 12.91s/it, avr_loss=0.0246]
steps:  23%|██▎       | 2352/10016 [8:26:14<27:29:35, 12.91s/it, avr_loss=0.0246]
steps:  23%|██▎       | 2353/10016 [8:26:27<27:29:22, 12.91s/it, avr_loss=0.0246]
steps:  23%|██▎       | 2353/10016 [8:26:27<27:29:22, 12.91s/it, avr_loss=0.0246]
steps:  24%|██▎       | 2354/10016 [8:26:40<27:29:09, 12.91s/it, avr_loss=0.0246]
steps:  24%|██▎       | 2354/10016 [8:26:40<27:29:09, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2355/10016 [8:26:53<27:28:56, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2355/10016 [8:26:53<27:28:56, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2356/10016 [8:27:06<27:28:43, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2356/10016 [8:27:06<27:28:43, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2357/10016 [8:27:18<27:28:30, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2357/10016 [8:27:18<27:28:30, 12.91s/it, avr_loss=0.0246]
steps:  24%|██▎       | 2358/10016 [8:27:31<27:28:17, 12.91s/it, avr_loss=0.0246]
steps:  24%|██▎       | 2358/10016 [8:27:31<27:28:17, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2359/10016 [8:27:44<27:28:04, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2359/10016 [8:27:44<27:28:04, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2360/10016 [8:27:57<27:27:51, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2360/10016 [8:27:57<27:27:51, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2361/10016 [8:28:10<27:27:38, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2361/10016 [8:28:10<27:27:38, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2362/10016 [8:28:23<27:27:25, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2362/10016 [8:28:23<27:27:25, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2363/10016 [8:28:36<27:27:12, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2363/10016 [8:28:36<27:27:12, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2364/10016 [8:28:49<27:26:59, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2364/10016 [8:28:49<27:26:59, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2365/10016 [8:29:01<27:26:46, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2365/10016 [8:29:01<27:26:46, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2366/10016 [8:29:14<27:26:33, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2366/10016 [8:29:14<27:26:33, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2367/10016 [8:29:27<27:26:19, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2367/10016 [8:29:27<27:26:19, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2368/10016 [8:29:40<27:26:06, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2368/10016 [8:29:40<27:26:06, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▎       | 2369/10016 [8:29:53<27:25:53, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▎       | 2369/10016 [8:29:53<27:25:53, 12.91s/it, avr_loss=0.0246]
steps:  24%|██▎       | 2370/10016 [8:30:06<27:25:40, 12.91s/it, avr_loss=0.0246]
steps:  24%|██▎       | 2370/10016 [8:30:06<27:25:40, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2371/10016 [8:30:19<27:25:27, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2371/10016 [8:30:19<27:25:27, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2372/10016 [8:30:32<27:25:14, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2372/10016 [8:30:32<27:25:14, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2373/10016 [8:30:44<27:25:01, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2373/10016 [8:30:44<27:25:01, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2374/10016 [8:30:57<27:24:48, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2374/10016 [8:30:57<27:24:48, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2375/10016 [8:31:10<27:24:35, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2375/10016 [8:31:10<27:24:35, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2376/10016 [8:31:23<27:24:22, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2376/10016 [8:31:23<27:24:22, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2377/10016 [8:31:36<27:24:09, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▎       | 2377/10016 [8:31:36<27:24:09, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▎       | 2378/10016 [8:31:49<27:23:56, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▎       | 2378/10016 [8:31:49<27:23:56, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2379/10016 [8:32:02<27:23:43, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2379/10016 [8:32:02<27:23:43, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2380/10016 [8:32:15<27:23:30, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2380/10016 [8:32:15<27:23:30, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2381/10016 [8:32:27<27:23:17, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2381/10016 [8:32:27<27:23:17, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2382/10016 [8:32:40<27:23:04, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2382/10016 [8:32:40<27:23:04, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2383/10016 [8:32:53<27:22:51, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2383/10016 [8:32:53<27:22:51, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2384/10016 [8:33:06<27:22:38, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2384/10016 [8:33:06<27:22:38, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2385/10016 [8:33:19<27:22:25, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2385/10016 [8:33:19<27:22:25, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2386/10016 [8:33:32<27:22:12, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2386/10016 [8:33:32<27:22:12, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2387/10016 [8:33:45<27:21:59, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2387/10016 [8:33:45<27:21:59, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2388/10016 [8:33:58<27:21:46, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2388/10016 [8:33:58<27:21:46, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2389/10016 [8:34:10<27:21:33, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2389/10016 [8:34:10<27:21:33, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2390/10016 [8:34:23<27:21:19, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2390/10016 [8:34:23<27:21:19, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2391/10016 [8:34:36<27:21:06, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2391/10016 [8:34:36<27:21:06, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2392/10016 [8:34:49<27:20:53, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2392/10016 [8:34:49<27:20:53, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2393/10016 [8:35:02<27:20:40, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2393/10016 [8:35:02<27:20:40, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2394/10016 [8:35:15<27:20:27, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2394/10016 [8:35:15<27:20:27, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2395/10016 [8:35:28<27:20:14, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2395/10016 [8:35:28<27:20:14, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2396/10016 [8:35:40<27:20:01, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2396/10016 [8:35:40<27:20:01, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2397/10016 [8:35:53<27:19:48, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2397/10016 [8:35:53<27:19:48, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2398/10016 [8:36:06<27:19:35, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2398/10016 [8:36:06<27:19:35, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2399/10016 [8:36:19<27:19:22, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2399/10016 [8:36:19<27:19:22, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2400/10016 [8:36:32<27:19:09, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2400/10016 [8:36:32<27:19:09, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2401/10016 [8:36:45<27:18:56, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2401/10016 [8:36:45<27:18:56, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2402/10016 [8:36:58<27:18:43, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2402/10016 [8:36:58<27:18:43, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2403/10016 [8:37:11<27:18:30, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2403/10016 [8:37:11<27:18:30, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2404/10016 [8:37:24<27:18:17, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2404/10016 [8:37:24<27:18:17, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2405/10016 [8:37:36<27:18:04, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2405/10016 [8:37:36<27:18:04, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2406/10016 [8:37:49<27:17:51, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2406/10016 [8:37:49<27:17:51, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2407/10016 [8:38:02<27:17:38, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2407/10016 [8:38:02<27:17:38, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2408/10016 [8:38:15<27:17:25, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2408/10016 [8:38:15<27:17:25, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2409/10016 [8:38:28<27:17:12, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2409/10016 [8:38:28<27:17:12, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2410/10016 [8:38:41<27:16:59, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2410/10016 [8:38:41<27:16:59, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2411/10016 [8:38:54<27:16:46, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2411/10016 [8:38:54<27:16:46, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2412/10016 [8:39:07<27:16:33, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2412/10016 [8:39:07<27:16:33, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2413/10016 [8:39:20<27:16:20, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2413/10016 [8:39:20<27:16:20, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2414/10016 [8:39:32<27:16:07, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2414/10016 [8:39:32<27:16:07, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2415/10016 [8:39:45<27:15:54, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2415/10016 [8:39:45<27:15:54, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2416/10016 [8:39:58<27:15:41, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2416/10016 [8:39:58<27:15:41, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2417/10016 [8:40:11<27:15:28, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2417/10016 [8:40:11<27:15:28, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2418/10016 [8:40:24<27:15:15, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2418/10016 [8:40:24<27:15:15, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2419/10016 [8:40:37<27:15:02, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2419/10016 [8:40:37<27:15:02, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2420/10016 [8:40:50<27:14:49, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2420/10016 [8:40:50<27:14:49, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2421/10016 [8:41:03<27:14:36, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2421/10016 [8:41:03<27:14:36, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2422/10016 [8:41:15<27:14:23, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2422/10016 [8:41:15<27:14:23, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2423/10016 [8:41:28<27:14:10, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2423/10016 [8:41:28<27:14:10, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2424/10016 [8:41:41<27:13:57, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2424/10016 [8:41:41<27:13:57, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2425/10016 [8:41:54<27:13:44, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2425/10016 [8:41:54<27:13:44, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2426/10016 [8:42:07<27:13:31, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2426/10016 [8:42:07<27:13:31, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2427/10016 [8:42:20<27:13:18, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2427/10016 [8:42:20<27:13:18, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2428/10016 [8:42:33<27:13:05, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2428/10016 [8:42:33<27:13:05, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2429/10016 [8:42:46<27:12:52, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2429/10016 [8:42:46<27:12:52, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2430/10016 [8:42:58<27:12:39, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2430/10016 [8:42:58<27:12:39, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2431/10016 [8:43:11<27:12:26, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2431/10016 [8:43:11<27:12:26, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2432/10016 [8:43:24<27:12:13, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2432/10016 [8:43:24<27:12:13, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2433/10016 [8:43:37<27:12:00, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2433/10016 [8:43:37<27:12:00, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2434/10016 [8:43:50<27:11:46, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2434/10016 [8:43:50<27:11:46, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2435/10016 [8:44:03<27:11:33, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2435/10016 [8:44:03<27:11:33, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2436/10016 [8:44:16<27:11:20, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2436/10016 [8:44:16<27:11:20, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2437/10016 [8:44:29<27:11:07, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2437/10016 [8:44:29<27:11:07, 12.91s/it, avr_loss=0.0246]
steps:  24%|██▍       | 2438/10016 [8:44:41<27:10:54, 12.91s/it, avr_loss=0.0246]
steps:  24%|██▍       | 2438/10016 [8:44:41<27:10:54, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2439/10016 [8:44:54<27:10:41, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2439/10016 [8:44:54<27:10:41, 12.91s/it, avr_loss=0.0246]
steps:  24%|██▍       | 2440/10016 [8:45:07<27:10:28, 12.91s/it, avr_loss=0.0246]
steps:  24%|██▍       | 2440/10016 [8:45:07<27:10:28, 12.91s/it, avr_loss=0.0246]
steps:  24%|██▍       | 2441/10016 [8:45:20<27:10:15, 12.91s/it, avr_loss=0.0246]
steps:  24%|██▍       | 2441/10016 [8:45:20<27:10:15, 12.91s/it, avr_loss=0.0246]
steps:  24%|██▍       | 2442/10016 [8:45:33<27:10:02, 12.91s/it, avr_loss=0.0246]
steps:  24%|██▍       | 2442/10016 [8:45:33<27:10:02, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2443/10016 [8:45:46<27:09:49, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2443/10016 [8:45:46<27:09:49, 12.91s/it, avr_loss=0.0246]
steps:  24%|██▍       | 2444/10016 [8:45:59<27:09:36, 12.91s/it, avr_loss=0.0246]
steps:  24%|██▍       | 2444/10016 [8:45:59<27:09:36, 12.91s/it, avr_loss=0.0246]
steps:  24%|██▍       | 2445/10016 [8:46:12<27:09:23, 12.91s/it, avr_loss=0.0246]
steps:  24%|██▍       | 2445/10016 [8:46:12<27:09:23, 12.91s/it, avr_loss=0.0246]
steps:  24%|██▍       | 2446/10016 [8:46:24<27:09:10, 12.91s/it, avr_loss=0.0246]
steps:  24%|██▍       | 2446/10016 [8:46:24<27:09:10, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2447/10016 [8:46:37<27:08:57, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2447/10016 [8:46:37<27:08:57, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2448/10016 [8:46:50<27:08:44, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2448/10016 [8:46:50<27:08:44, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2449/10016 [8:47:03<27:08:31, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2449/10016 [8:47:03<27:08:31, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2450/10016 [8:47:16<27:08:18, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2450/10016 [8:47:16<27:08:18, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2451/10016 [8:47:29<27:08:05, 12.91s/it, avr_loss=0.0245]
steps:  24%|██▍       | 2451/10016 [8:47:29<27:08:05, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2452/10016 [8:47:42<27:07:52, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2452/10016 [8:47:42<27:07:52, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2453/10016 [8:47:55<27:07:39, 12.91s/it, avr_loss=0.0244]
steps:  24%|██▍       | 2453/10016 [8:47:55<27:07:39, 12.91s/it, avr_loss=0.0244]
steps:  25%|██▍       | 2454/10016 [8:48:07<27:07:26, 12.91s/it, avr_loss=0.0244]
steps:  25%|██▍       | 2454/10016 [8:48:07<27:07:26, 12.91s/it, avr_loss=0.0244]
steps:  25%|██▍       | 2455/10016 [8:48:20<27:07:13, 12.91s/it, avr_loss=0.0244]
steps:  25%|██▍       | 2455/10016 [8:48:20<27:07:13, 12.91s/it, avr_loss=0.0244]
steps:  25%|██▍       | 2456/10016 [8:48:33<27:07:00, 12.91s/it, avr_loss=0.0244]
steps:  25%|██▍       | 2456/10016 [8:48:33<27:07:00, 12.91s/it, avr_loss=0.0244]
steps:  25%|██▍       | 2457/10016 [8:48:46<27:06:47, 12.91s/it, avr_loss=0.0244]
steps:  25%|██▍       | 2457/10016 [8:48:46<27:06:47, 12.91s/it, avr_loss=0.0244]
steps:  25%|██▍       | 2458/10016 [8:48:59<27:06:34, 12.91s/it, avr_loss=0.0244]
steps:  25%|██▍       | 2458/10016 [8:48:59<27:06:34, 12.91s/it, avr_loss=0.0244]
steps:  25%|██▍       | 2459/10016 [8:49:12<27:06:21, 12.91s/it, avr_loss=0.0244]
steps:  25%|██▍       | 2459/10016 [8:49:12<27:06:21, 12.91s/it, avr_loss=0.0246]
steps:  25%|██▍       | 2460/10016 [8:49:25<27:06:08, 12.91s/it, avr_loss=0.0246]
steps:  25%|██▍       | 2460/10016 [8:49:25<27:06:08, 12.91s/it, avr_loss=0.0246]
steps:  25%|██▍       | 2461/10016 [8:49:38<27:05:55, 12.91s/it, avr_loss=0.0246]
steps:  25%|██▍       | 2461/10016 [8:49:38<27:05:55, 12.91s/it, avr_loss=0.0246]
steps:  25%|██▍       | 2462/10016 [8:49:50<27:05:41, 12.91s/it, avr_loss=0.0246]
steps:  25%|██▍       | 2462/10016 [8:49:50<27:05:41, 12.91s/it, avr_loss=0.0245]
steps:  25%|██▍       | 2463/10016 [8:50:03<27:05:28, 12.91s/it, avr_loss=0.0245]
steps:  25%|██▍       | 2463/10016 [8:50:03<27:05:28, 12.91s/it, avr_loss=0.0245]
steps:  25%|██▍       | 2464/10016 [8:50:16<27:05:15, 12.91s/it, avr_loss=0.0245]
steps:  25%|██▍       | 2464/10016 [8:50:16<27:05:15, 12.91s/it, avr_loss=0.0245]
steps:  25%|██▍       | 2465/10016 [8:50:29<27:05:02, 12.91s/it, avr_loss=0.0245]
steps:  25%|██▍       | 2465/10016 [8:50:29<27:05:02, 12.91s/it, avr_loss=0.0245]
steps:  25%|██▍       | 2466/10016 [8:50:42<27:04:49, 12.91s/it, avr_loss=0.0245]
steps:  25%|██▍       | 2466/10016 [8:50:42<27:04:49, 12.91s/it, avr_loss=0.0245]
steps:  25%|██▍       | 2467/10016 [8:50:55<27:04:36, 12.91s/it, avr_loss=0.0245]
steps:  25%|██▍       | 2467/10016 [8:50:55<27:04:36, 12.91s/it, avr_loss=0.0246]
steps:  25%|██▍       | 2468/10016 [8:51:08<27:04:23, 12.91s/it, avr_loss=0.0246]
steps:  25%|██▍       | 2468/10016 [8:51:08<27:04:23, 12.91s/it, avr_loss=0.0246]
steps:  25%|██▍       | 2469/10016 [8:51:20<27:04:10, 12.91s/it, avr_loss=0.0246]
steps:  25%|██▍       | 2469/10016 [8:51:20<27:04:10, 12.91s/it, avr_loss=0.0246]
steps:  25%|██▍       | 2470/10016 [8:51:33<27:03:57, 12.91s/it, avr_loss=0.0246]
steps:  25%|██▍       | 2470/10016 [8:51:33<27:03:57, 12.91s/it, avr_loss=0.0246]
steps:  25%|██▍       | 2471/10016 [8:51:46<27:03:44, 12.91s/it, avr_loss=0.0246]
steps:  25%|██▍       | 2471/10016 [8:51:46<27:03:44, 12.91s/it, avr_loss=0.0247]
steps:  25%|██▍       | 2472/10016 [8:51:59<27:03:31, 12.91s/it, avr_loss=0.0247]
steps:  25%|██▍       | 2472/10016 [8:51:59<27:03:31, 12.91s/it, avr_loss=0.0247]
steps:  25%|██▍       | 2473/10016 [8:52:12<27:03:18, 12.91s/it, avr_loss=0.0247]
steps:  25%|██▍       | 2473/10016 [8:52:12<27:03:18, 12.91s/it, avr_loss=0.0247]
steps:  25%|██▍       | 2474/10016 [8:52:25<27:03:05, 12.91s/it, avr_loss=0.0247]
steps:  25%|██▍       | 2474/10016 [8:52:25<27:03:05, 12.91s/it, avr_loss=0.0247]
steps:  25%|██▍       | 2475/10016 [8:52:38<27:02:52, 12.91s/it, avr_loss=0.0247]
steps:  25%|██▍       | 2475/10016 [8:52:38<27:02:52, 12.91s/it, avr_loss=0.0247]
steps:  25%|██▍       | 2476/10016 [8:52:51<27:02:39, 12.91s/it, avr_loss=0.0247]
steps:  25%|██▍       | 2476/10016 [8:52:51<27:02:39, 12.91s/it, avr_loss=0.0247]
steps:  25%|██▍       | 2477/10016 [8:53:03<27:02:26, 12.91s/it, avr_loss=0.0247]
steps:  25%|██▍       | 2477/10016 [8:53:03<27:02:26, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▍       | 2478/10016 [8:53:16<27:02:13, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▍       | 2478/10016 [8:53:16<27:02:13, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2479/10016 [8:53:29<27:02:00, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2479/10016 [8:53:29<27:02:00, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2480/10016 [8:53:42<27:01:47, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2480/10016 [8:53:42<27:01:47, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2481/10016 [8:53:55<27:01:34, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2481/10016 [8:53:55<27:01:34, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2482/10016 [8:54:08<27:01:21, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2482/10016 [8:54:08<27:01:21, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2483/10016 [8:54:21<27:01:08, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2483/10016 [8:54:21<27:01:08, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2484/10016 [8:54:34<27:00:55, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2484/10016 [8:54:34<27:00:55, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2485/10016 [8:54:46<27:00:42, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2485/10016 [8:54:46<27:00:42, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2486/10016 [8:54:59<27:00:29, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2486/10016 [8:54:59<27:00:29, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2487/10016 [8:55:12<27:00:16, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2487/10016 [8:55:12<27:00:16, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2488/10016 [8:55:25<27:00:02, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2488/10016 [8:55:25<27:00:02, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2489/10016 [8:55:38<26:59:49, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2489/10016 [8:55:38<26:59:49, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2490/10016 [8:55:51<26:59:36, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2490/10016 [8:55:51<26:59:36, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2491/10016 [8:56:04<26:59:23, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2491/10016 [8:56:04<26:59:23, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2492/10016 [8:56:17<26:59:10, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2492/10016 [8:56:17<26:59:10, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2493/10016 [8:56:29<26:58:57, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2493/10016 [8:56:29<26:58:57, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2494/10016 [8:56:42<26:58:44, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2494/10016 [8:56:42<26:58:44, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2495/10016 [8:56:55<26:58:31, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2495/10016 [8:56:55<26:58:31, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2496/10016 [8:57:08<26:58:18, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2496/10016 [8:57:08<26:58:18, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2497/10016 [8:57:21<26:58:05, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2497/10016 [8:57:21<26:58:05, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2498/10016 [8:57:34<26:57:52, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2498/10016 [8:57:34<26:57:52, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2499/10016 [8:57:47<26:57:39, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2499/10016 [8:57:47<26:57:39, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2500/10016 [8:58:00<26:57:26, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2500/10016 [8:58:00<26:57:26, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▍       | 2501/10016 [8:58:12<26:57:13, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▍       | 2501/10016 [8:58:12<26:57:13, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▍       | 2502/10016 [8:58:25<26:57:00, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▍       | 2502/10016 [8:58:25<26:57:00, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2503/10016 [8:58:38<26:56:47, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▍       | 2503/10016 [8:58:38<26:56:47, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2504/10016 [8:58:51<26:56:34, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2504/10016 [8:58:51<26:56:34, 12.91s/it, avr_loss=0.0249]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/loss-curve-retrain/train_lora-000004.safetensors

epoch 5/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5

steps:  25%|██▌       | 2505/10016 [8:59:06<26:56:27, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2505/10016 [8:59:06<26:56:27, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2506/10016 [8:59:19<26:56:14, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2506/10016 [8:59:19<26:56:14, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2507/10016 [8:59:32<26:56:01, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2507/10016 [8:59:32<26:56:01, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2508/10016 [8:59:45<26:55:48, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2508/10016 [8:59:45<26:55:48, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2509/10016 [8:59:58<26:55:35, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2509/10016 [8:59:58<26:55:35, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2510/10016 [9:00:10<26:55:22, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2510/10016 [9:00:10<26:55:22, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2511/10016 [9:00:23<26:55:10, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2511/10016 [9:00:23<26:55:10, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2512/10016 [9:00:36<26:54:57, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2512/10016 [9:00:36<26:54:57, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2513/10016 [9:00:49<26:54:44, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2513/10016 [9:00:49<26:54:44, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2514/10016 [9:01:02<26:54:31, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2514/10016 [9:01:02<26:54:31, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2515/10016 [9:01:15<26:54:18, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2515/10016 [9:01:15<26:54:18, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2516/10016 [9:01:28<26:54:05, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2516/10016 [9:01:28<26:54:05, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2517/10016 [9:01:41<26:53:52, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2517/10016 [9:01:41<26:53:52, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2518/10016 [9:01:54<26:53:39, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2518/10016 [9:01:54<26:53:39, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2519/10016 [9:02:07<26:53:26, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2519/10016 [9:02:07<26:53:26, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2520/10016 [9:02:20<26:53:13, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2520/10016 [9:02:20<26:53:13, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2521/10016 [9:02:32<26:53:00, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2521/10016 [9:02:32<26:53:00, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2522/10016 [9:02:45<26:52:47, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2522/10016 [9:02:45<26:52:47, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2523/10016 [9:02:58<26:52:34, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2523/10016 [9:02:58<26:52:34, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2524/10016 [9:03:11<26:52:21, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2524/10016 [9:03:11<26:52:21, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2525/10016 [9:03:24<26:52:08, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2525/10016 [9:03:24<26:52:08, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2526/10016 [9:03:37<26:51:55, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2526/10016 [9:03:37<26:51:55, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2527/10016 [9:03:50<26:51:42, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2527/10016 [9:03:50<26:51:42, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2528/10016 [9:04:03<26:51:29, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2528/10016 [9:04:03<26:51:29, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2529/10016 [9:04:15<26:51:16, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2529/10016 [9:04:15<26:51:16, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2530/10016 [9:04:28<26:51:03, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2530/10016 [9:04:28<26:51:03, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2531/10016 [9:04:41<26:50:50, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2531/10016 [9:04:41<26:50:50, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2532/10016 [9:04:54<26:50:37, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2532/10016 [9:04:54<26:50:37, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2533/10016 [9:05:07<26:50:24, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2533/10016 [9:05:07<26:50:24, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2534/10016 [9:05:20<26:50:11, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2534/10016 [9:05:20<26:50:11, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2535/10016 [9:05:33<26:49:58, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2535/10016 [9:05:33<26:49:58, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2536/10016 [9:05:46<26:49:45, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2536/10016 [9:05:46<26:49:45, 12.91s/it, avr_loss=0.0247]
steps:  25%|██▌       | 2537/10016 [9:05:59<26:49:32, 12.91s/it, avr_loss=0.0247]
steps:  25%|██▌       | 2537/10016 [9:05:59<26:49:32, 12.91s/it, avr_loss=0.0247]
steps:  25%|██▌       | 2538/10016 [9:06:11<26:49:19, 12.91s/it, avr_loss=0.0247]
steps:  25%|██▌       | 2538/10016 [9:06:11<26:49:19, 12.91s/it, avr_loss=0.0247]
steps:  25%|██▌       | 2539/10016 [9:06:24<26:49:06, 12.91s/it, avr_loss=0.0247]
steps:  25%|██▌       | 2539/10016 [9:06:24<26:49:06, 12.91s/it, avr_loss=0.0247]
steps:  25%|██▌       | 2540/10016 [9:06:37<26:48:53, 12.91s/it, avr_loss=0.0247]
steps:  25%|██▌       | 2540/10016 [9:06:37<26:48:53, 12.91s/it, avr_loss=0.0247]
steps:  25%|██▌       | 2541/10016 [9:06:50<26:48:40, 12.91s/it, avr_loss=0.0247]
steps:  25%|██▌       | 2541/10016 [9:06:50<26:48:40, 12.91s/it, avr_loss=0.0247]
steps:  25%|██▌       | 2542/10016 [9:07:03<26:48:27, 12.91s/it, avr_loss=0.0247]
steps:  25%|██▌       | 2542/10016 [9:07:03<26:48:27, 12.91s/it, avr_loss=0.0247]
steps:  25%|██▌       | 2543/10016 [9:07:16<26:48:15, 12.91s/it, avr_loss=0.0247]
steps:  25%|██▌       | 2543/10016 [9:07:16<26:48:15, 12.91s/it, avr_loss=0.0246]
steps:  25%|██▌       | 2544/10016 [9:07:29<26:48:01, 12.91s/it, avr_loss=0.0246]
steps:  25%|██▌       | 2544/10016 [9:07:29<26:48:01, 12.91s/it, avr_loss=0.0247]
steps:  25%|██▌       | 2545/10016 [9:07:42<26:47:49, 12.91s/it, avr_loss=0.0247]
steps:  25%|██▌       | 2545/10016 [9:07:42<26:47:49, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2546/10016 [9:07:55<26:47:36, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2546/10016 [9:07:55<26:47:36, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2547/10016 [9:08:08<26:47:23, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2547/10016 [9:08:08<26:47:23, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2548/10016 [9:08:20<26:47:10, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2548/10016 [9:08:20<26:47:10, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2549/10016 [9:08:33<26:46:57, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2549/10016 [9:08:33<26:46:57, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2550/10016 [9:08:46<26:46:44, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2550/10016 [9:08:46<26:46:44, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2551/10016 [9:08:59<26:46:31, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2551/10016 [9:08:59<26:46:31, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2552/10016 [9:09:12<26:46:18, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2552/10016 [9:09:12<26:46:18, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2553/10016 [9:09:25<26:46:05, 12.91s/it, avr_loss=0.0249]
steps:  25%|██▌       | 2553/10016 [9:09:25<26:46:05, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2554/10016 [9:09:38<26:45:52, 12.91s/it, avr_loss=0.0248]
steps:  25%|██▌       | 2554/10016 [9:09:38<26:45:52, 12.91s/it, avr_loss=0.0252]
steps:  26%|██▌       | 2555/10016 [9:09:51<26:45:39, 12.91s/it, avr_loss=0.0252]
steps:  26%|██▌       | 2555/10016 [9:09:51<26:45:39, 12.91s/it, avr_loss=0.0252]
steps:  26%|██▌       | 2556/10016 [9:10:04<26:45:26, 12.91s/it, avr_loss=0.0252]
steps:  26%|██▌       | 2556/10016 [9:10:04<26:45:26, 12.91s/it, avr_loss=0.0252]
steps:  26%|██▌       | 2557/10016 [9:10:16<26:45:13, 12.91s/it, avr_loss=0.0252]
steps:  26%|██▌       | 2557/10016 [9:10:16<26:45:13, 12.91s/it, avr_loss=0.0252]
steps:  26%|██▌       | 2558/10016 [9:10:29<26:45:00, 12.91s/it, avr_loss=0.0252]
steps:  26%|██▌       | 2558/10016 [9:10:29<26:45:00, 12.91s/it, avr_loss=0.0252]
steps:  26%|██▌       | 2559/10016 [9:10:42<26:44:47, 12.91s/it, avr_loss=0.0252]
steps:  26%|██▌       | 2559/10016 [9:10:42<26:44:47, 12.91s/it, avr_loss=0.0251]
steps:  26%|██▌       | 2560/10016 [9:10:55<26:44:34, 12.91s/it, avr_loss=0.0251]
steps:  26%|██▌       | 2560/10016 [9:10:55<26:44:34, 12.91s/it, avr_loss=0.0251]
steps:  26%|██▌       | 2561/10016 [9:11:08<26:44:21, 12.91s/it, avr_loss=0.0251]
steps:  26%|██▌       | 2561/10016 [9:11:08<26:44:21, 12.91s/it, avr_loss=0.0251]
steps:  26%|██▌       | 2562/10016 [9:11:21<26:44:08, 12.91s/it, avr_loss=0.0251]
steps:  26%|██▌       | 2562/10016 [9:11:21<26:44:08, 12.91s/it, avr_loss=0.0251]
steps:  26%|██▌       | 2563/10016 [9:11:34<26:43:55, 12.91s/it, avr_loss=0.0251]
steps:  26%|██▌       | 2563/10016 [9:11:34<26:43:55, 12.91s/it, avr_loss=0.0251]
steps:  26%|██▌       | 2564/10016 [9:11:47<26:43:42, 12.91s/it, avr_loss=0.0251]
steps:  26%|██▌       | 2564/10016 [9:11:47<26:43:42, 12.91s/it, avr_loss=0.0251]
steps:  26%|██▌       | 2565/10016 [9:12:00<26:43:29, 12.91s/it, avr_loss=0.0251]
steps:  26%|██▌       | 2565/10016 [9:12:00<26:43:29, 12.91s/it, avr_loss=0.0251]
steps:  26%|██▌       | 2566/10016 [9:12:12<26:43:16, 12.91s/it, avr_loss=0.0251]
steps:  26%|██▌       | 2566/10016 [9:12:12<26:43:16, 12.91s/it, avr_loss=0.0251]
steps:  26%|██▌       | 2567/10016 [9:12:25<26:43:03, 12.91s/it, avr_loss=0.0251]
steps:  26%|██▌       | 2567/10016 [9:12:25<26:43:03, 12.91s/it, avr_loss=0.0251]
steps:  26%|██▌       | 2568/10016 [9:12:38<26:42:50, 12.91s/it, avr_loss=0.0251]
steps:  26%|██▌       | 2568/10016 [9:12:38<26:42:50, 12.91s/it, avr_loss=0.0251]
steps:  26%|██▌       | 2569/10016 [9:12:51<26:42:37, 12.91s/it, avr_loss=0.0251]
steps:  26%|██▌       | 2569/10016 [9:12:51<26:42:37, 12.91s/it, avr_loss=0.0251]
steps:  26%|██▌       | 2570/10016 [9:13:04<26:42:24, 12.91s/it, avr_loss=0.0251]
steps:  26%|██▌       | 2570/10016 [9:13:04<26:42:24, 12.91s/it, avr_loss=0.0251]
steps:  26%|██▌       | 2571/10016 [9:13:17<26:42:11, 12.91s/it, avr_loss=0.0251]
steps:  26%|██▌       | 2571/10016 [9:13:17<26:42:11, 12.91s/it, avr_loss=0.0251]
steps:  26%|██▌       | 2572/10016 [9:13:30<26:41:58, 12.91s/it, avr_loss=0.0251]
steps:  26%|██▌       | 2572/10016 [9:13:30<26:41:58, 12.91s/it, avr_loss=0.0251]
steps:  26%|██▌       | 2573/10016 [9:13:43<26:41:45, 12.91s/it, avr_loss=0.0251]
steps:  26%|██▌       | 2573/10016 [9:13:43<26:41:45, 12.91s/it, avr_loss=0.0251]
steps:  26%|██▌       | 2574/10016 [9:13:56<26:41:32, 12.91s/it, avr_loss=0.0251]
steps:  26%|██▌       | 2574/10016 [9:13:56<26:41:32, 12.91s/it, avr_loss=0.0251]
steps:  26%|██▌       | 2575/10016 [9:14:08<26:41:19, 12.91s/it, avr_loss=0.0251]
steps:  26%|██▌       | 2575/10016 [9:14:08<26:41:19, 12.91s/it, avr_loss=0.025] 
steps:  26%|██▌       | 2576/10016 [9:14:21<26:41:06, 12.91s/it, avr_loss=0.025]
steps:  26%|██▌       | 2576/10016 [9:14:21<26:41:06, 12.91s/it, avr_loss=0.025]
steps:  26%|██▌       | 2577/10016 [9:14:34<26:40:53, 12.91s/it, avr_loss=0.025]
steps:  26%|██▌       | 2577/10016 [9:14:34<26:40:53, 12.91s/it, avr_loss=0.025]
steps:  26%|██▌       | 2578/10016 [9:14:47<26:40:40, 12.91s/it, avr_loss=0.025]
steps:  26%|██▌       | 2578/10016 [9:14:47<26:40:40, 12.91s/it, avr_loss=0.025]
steps:  26%|██▌       | 2579/10016 [9:15:00<26:40:27, 12.91s/it, avr_loss=0.025]
steps:  26%|██▌       | 2579/10016 [9:15:00<26:40:27, 12.91s/it, avr_loss=0.025]
steps:  26%|██▌       | 2580/10016 [9:15:13<26:40:14, 12.91s/it, avr_loss=0.025]
steps:  26%|██▌       | 2580/10016 [9:15:13<26:40:14, 12.91s/it, avr_loss=0.025]
steps:  26%|██▌       | 2581/10016 [9:15:26<26:40:01, 12.91s/it, avr_loss=0.025]
steps:  26%|██▌       | 2581/10016 [9:15:26<26:40:01, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2582/10016 [9:15:39<26:39:48, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2582/10016 [9:15:39<26:39:48, 12.91s/it, avr_loss=0.025] 
steps:  26%|██▌       | 2583/10016 [9:15:52<26:39:35, 12.91s/it, avr_loss=0.025]
steps:  26%|██▌       | 2583/10016 [9:15:52<26:39:35, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2584/10016 [9:16:04<26:39:22, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2584/10016 [9:16:04<26:39:22, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2585/10016 [9:16:17<26:39:09, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2585/10016 [9:16:17<26:39:09, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2586/10016 [9:16:30<26:38:56, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2586/10016 [9:16:30<26:38:56, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2587/10016 [9:16:43<26:38:43, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2587/10016 [9:16:43<26:38:43, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2588/10016 [9:16:56<26:38:30, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2588/10016 [9:16:56<26:38:30, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2589/10016 [9:17:09<26:38:17, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2589/10016 [9:17:09<26:38:17, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2590/10016 [9:17:22<26:38:04, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2590/10016 [9:17:22<26:38:04, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2591/10016 [9:17:35<26:37:51, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2591/10016 [9:17:35<26:37:51, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2592/10016 [9:17:47<26:37:38, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2592/10016 [9:17:47<26:37:38, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2593/10016 [9:18:00<26:37:25, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2593/10016 [9:18:00<26:37:25, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2594/10016 [9:18:13<26:37:12, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2594/10016 [9:18:13<26:37:12, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2595/10016 [9:18:26<26:36:59, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2595/10016 [9:18:26<26:36:59, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2596/10016 [9:18:39<26:36:46, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2596/10016 [9:18:39<26:36:46, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2597/10016 [9:18:52<26:36:33, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2597/10016 [9:18:52<26:36:33, 12.91s/it, avr_loss=0.0247]
steps:  26%|██▌       | 2598/10016 [9:19:05<26:36:20, 12.91s/it, avr_loss=0.0247]
steps:  26%|██▌       | 2598/10016 [9:19:05<26:36:20, 12.91s/it, avr_loss=0.0247]
steps:  26%|██▌       | 2599/10016 [9:19:18<26:36:07, 12.91s/it, avr_loss=0.0247]
steps:  26%|██▌       | 2599/10016 [9:19:18<26:36:07, 12.91s/it, avr_loss=0.0247]
steps:  26%|██▌       | 2600/10016 [9:19:31<26:35:55, 12.91s/it, avr_loss=0.0247]
steps:  26%|██▌       | 2600/10016 [9:19:31<26:35:55, 12.91s/it, avr_loss=0.0247]
steps:  26%|██▌       | 2601/10016 [9:19:43<26:35:42, 12.91s/it, avr_loss=0.0247]
steps:  26%|██▌       | 2601/10016 [9:19:43<26:35:42, 12.91s/it, avr_loss=0.0247]
steps:  26%|██▌       | 2602/10016 [9:19:56<26:35:29, 12.91s/it, avr_loss=0.0247]
steps:  26%|██▌       | 2602/10016 [9:19:56<26:35:29, 12.91s/it, avr_loss=0.0247]
steps:  26%|██▌       | 2603/10016 [9:20:09<26:35:16, 12.91s/it, avr_loss=0.0247]
steps:  26%|██▌       | 2603/10016 [9:20:09<26:35:16, 12.91s/it, avr_loss=0.0247]
steps:  26%|██▌       | 2604/10016 [9:20:22<26:35:03, 12.91s/it, avr_loss=0.0247]
steps:  26%|██▌       | 2604/10016 [9:20:22<26:35:03, 12.91s/it, avr_loss=0.0247]
steps:  26%|██▌       | 2605/10016 [9:20:35<26:34:50, 12.91s/it, avr_loss=0.0247]
steps:  26%|██▌       | 2605/10016 [9:20:35<26:34:50, 12.91s/it, avr_loss=0.0247]
steps:  26%|██▌       | 2606/10016 [9:20:48<26:34:37, 12.91s/it, avr_loss=0.0247]
steps:  26%|██▌       | 2606/10016 [9:20:48<26:34:37, 12.91s/it, avr_loss=0.0247]
steps:  26%|██▌       | 2607/10016 [9:21:01<26:34:24, 12.91s/it, avr_loss=0.0247]
steps:  26%|██▌       | 2607/10016 [9:21:01<26:34:24, 12.91s/it, avr_loss=0.0248]
steps:  26%|██▌       | 2608/10016 [9:21:14<26:34:11, 12.91s/it, avr_loss=0.0248]
steps:  26%|██▌       | 2608/10016 [9:21:14<26:34:11, 12.91s/it, avr_loss=0.0248]
steps:  26%|██▌       | 2609/10016 [9:21:27<26:33:58, 12.91s/it, avr_loss=0.0248]
steps:  26%|██▌       | 2609/10016 [9:21:27<26:33:58, 12.91s/it, avr_loss=0.0248]
steps:  26%|██▌       | 2610/10016 [9:21:39<26:33:45, 12.91s/it, avr_loss=0.0248]
steps:  26%|██▌       | 2610/10016 [9:21:39<26:33:45, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2611/10016 [9:21:52<26:33:32, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2611/10016 [9:21:52<26:33:32, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2612/10016 [9:22:05<26:33:19, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2612/10016 [9:22:05<26:33:19, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2613/10016 [9:22:18<26:33:06, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2613/10016 [9:22:18<26:33:06, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2614/10016 [9:22:31<26:32:53, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2614/10016 [9:22:31<26:32:53, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2615/10016 [9:22:44<26:32:40, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2615/10016 [9:22:44<26:32:40, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2616/10016 [9:22:57<26:32:27, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2616/10016 [9:22:57<26:32:27, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2617/10016 [9:23:10<26:32:14, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2617/10016 [9:23:10<26:32:14, 12.91s/it, avr_loss=0.025] 
steps:  26%|██▌       | 2618/10016 [9:23:23<26:32:01, 12.91s/it, avr_loss=0.025]
steps:  26%|██▌       | 2618/10016 [9:23:23<26:32:01, 12.91s/it, avr_loss=0.025]
steps:  26%|██▌       | 2619/10016 [9:23:35<26:31:48, 12.91s/it, avr_loss=0.025]
steps:  26%|██▌       | 2619/10016 [9:23:35<26:31:48, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2620/10016 [9:23:48<26:31:35, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▌       | 2620/10016 [9:23:48<26:31:35, 12.91s/it, avr_loss=0.0248]
steps:  26%|██▌       | 2621/10016 [9:24:01<26:31:22, 12.91s/it, avr_loss=0.0248]
steps:  26%|██▌       | 2621/10016 [9:24:01<26:31:22, 12.91s/it, avr_loss=0.0248]
steps:  26%|██▌       | 2622/10016 [9:24:14<26:31:09, 12.91s/it, avr_loss=0.0248]
steps:  26%|██▌       | 2622/10016 [9:24:14<26:31:09, 12.91s/it, avr_loss=0.0248]
steps:  26%|██▌       | 2623/10016 [9:24:27<26:30:56, 12.91s/it, avr_loss=0.0248]
steps:  26%|██▌       | 2623/10016 [9:24:27<26:30:56, 12.91s/it, avr_loss=0.0248]
steps:  26%|██▌       | 2624/10016 [9:24:40<26:30:43, 12.91s/it, avr_loss=0.0248]
steps:  26%|██▌       | 2624/10016 [9:24:40<26:30:43, 12.91s/it, avr_loss=0.0248]
steps:  26%|██▌       | 2625/10016 [9:24:53<26:30:30, 12.91s/it, avr_loss=0.0248]
steps:  26%|██▌       | 2625/10016 [9:24:53<26:30:30, 12.91s/it, avr_loss=0.0247]
steps:  26%|██▌       | 2626/10016 [9:25:06<26:30:17, 12.91s/it, avr_loss=0.0247]
steps:  26%|██▌       | 2626/10016 [9:25:06<26:30:17, 12.91s/it, avr_loss=0.0247]
steps:  26%|██▌       | 2627/10016 [9:25:19<26:30:05, 12.91s/it, avr_loss=0.0247]
steps:  26%|██▌       | 2627/10016 [9:25:19<26:30:05, 12.91s/it, avr_loss=0.0247]
steps:  26%|██▌       | 2628/10016 [9:25:32<26:29:52, 12.91s/it, avr_loss=0.0247]
steps:  26%|██▌       | 2628/10016 [9:25:32<26:29:52, 12.91s/it, avr_loss=0.0247]
steps:  26%|██▌       | 2629/10016 [9:25:44<26:29:39, 12.91s/it, avr_loss=0.0247]
steps:  26%|██▌       | 2629/10016 [9:25:44<26:29:39, 12.91s/it, avr_loss=0.0247]
steps:  26%|██▋       | 2630/10016 [9:25:57<26:29:26, 12.91s/it, avr_loss=0.0247]
steps:  26%|██▋       | 2630/10016 [9:25:57<26:29:26, 12.91s/it, avr_loss=0.0248]
steps:  26%|██▋       | 2631/10016 [9:26:10<26:29:13, 12.91s/it, avr_loss=0.0248]
steps:  26%|██▋       | 2631/10016 [9:26:10<26:29:13, 12.91s/it, avr_loss=0.0248]
steps:  26%|██▋       | 2632/10016 [9:26:23<26:29:00, 12.91s/it, avr_loss=0.0248]
steps:  26%|██▋       | 2632/10016 [9:26:23<26:29:00, 12.91s/it, avr_loss=0.0247]
steps:  26%|██▋       | 2633/10016 [9:26:36<26:28:47, 12.91s/it, avr_loss=0.0247]
steps:  26%|██▋       | 2633/10016 [9:26:36<26:28:47, 12.91s/it, avr_loss=0.0248]
steps:  26%|██▋       | 2634/10016 [9:26:49<26:28:34, 12.91s/it, avr_loss=0.0248]
steps:  26%|██▋       | 2634/10016 [9:26:49<26:28:34, 12.91s/it, avr_loss=0.0248]
steps:  26%|██▋       | 2635/10016 [9:27:02<26:28:21, 12.91s/it, avr_loss=0.0248]
steps:  26%|██▋       | 2635/10016 [9:27:02<26:28:21, 12.91s/it, avr_loss=0.0248]
steps:  26%|██▋       | 2636/10016 [9:27:15<26:28:08, 12.91s/it, avr_loss=0.0248]
steps:  26%|██▋       | 2636/10016 [9:27:15<26:28:08, 12.91s/it, avr_loss=0.0248]
steps:  26%|██▋       | 2637/10016 [9:27:28<26:27:55, 12.91s/it, avr_loss=0.0248]
steps:  26%|██▋       | 2637/10016 [9:27:28<26:27:55, 12.91s/it, avr_loss=0.0248]
steps:  26%|██▋       | 2638/10016 [9:27:41<26:27:42, 12.91s/it, avr_loss=0.0248]
steps:  26%|██▋       | 2638/10016 [9:27:41<26:27:42, 12.91s/it, avr_loss=0.0248]
steps:  26%|██▋       | 2639/10016 [9:27:53<26:27:29, 12.91s/it, avr_loss=0.0248]
steps:  26%|██▋       | 2639/10016 [9:27:53<26:27:29, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▋       | 2640/10016 [9:28:06<26:27:16, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▋       | 2640/10016 [9:28:06<26:27:16, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▋       | 2641/10016 [9:28:19<26:27:03, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▋       | 2641/10016 [9:28:19<26:27:03, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▋       | 2642/10016 [9:28:32<26:26:50, 12.91s/it, avr_loss=0.0249]
steps:  26%|██▋       | 2642/10016 [9:28:32<26:26:50, 12.91s/it, avr_loss=0.025] 
steps:  26%|██▋       | 2643/10016 [9:28:45<26:26:37, 12.91s/it, avr_loss=0.025]
steps:  26%|██▋       | 2643/10016 [9:28:45<26:26:37, 12.91s/it, avr_loss=0.025]
steps:  26%|██▋       | 2644/10016 [9:28:58<26:26:24, 12.91s/it, avr_loss=0.025]
steps:  26%|██▋       | 2644/10016 [9:28:58<26:26:24, 12.91s/it, avr_loss=0.025]
steps:  26%|██▋       | 2645/10016 [9:29:11<26:26:11, 12.91s/it, avr_loss=0.025]
steps:  26%|██▋       | 2645/10016 [9:29:11<26:26:11, 12.91s/it, avr_loss=0.025]
steps:  26%|██▋       | 2646/10016 [9:29:24<26:25:58, 12.91s/it, avr_loss=0.025]
steps:  26%|██▋       | 2646/10016 [9:29:24<26:25:58, 12.91s/it, avr_loss=0.025]
steps:  26%|██▋       | 2647/10016 [9:29:37<26:25:45, 12.91s/it, avr_loss=0.025]
steps:  26%|██▋       | 2647/10016 [9:29:37<26:25:45, 12.91s/it, avr_loss=0.025]
steps:  26%|██▋       | 2648/10016 [9:29:49<26:25:32, 12.91s/it, avr_loss=0.025]
steps:  26%|██▋       | 2648/10016 [9:29:49<26:25:32, 12.91s/it, avr_loss=0.025]
steps:  26%|██▋       | 2649/10016 [9:30:02<26:25:19, 12.91s/it, avr_loss=0.025]
steps:  26%|██▋       | 2649/10016 [9:30:02<26:25:19, 12.91s/it, avr_loss=0.025]
steps:  26%|██▋       | 2650/10016 [9:30:15<26:25:06, 12.91s/it, avr_loss=0.025]
steps:  26%|██▋       | 2650/10016 [9:30:15<26:25:06, 12.91s/it, avr_loss=0.025]
steps:  26%|██▋       | 2651/10016 [9:30:28<26:24:53, 12.91s/it, avr_loss=0.025]
steps:  26%|██▋       | 2651/10016 [9:30:28<26:24:53, 12.91s/it, avr_loss=0.025]
steps:  26%|██▋       | 2652/10016 [9:30:41<26:24:40, 12.91s/it, avr_loss=0.025]
steps:  26%|██▋       | 2652/10016 [9:30:41<26:24:40, 12.91s/it, avr_loss=0.025]
steps:  26%|██▋       | 2653/10016 [9:30:54<26:24:27, 12.91s/it, avr_loss=0.025]
steps:  26%|██▋       | 2653/10016 [9:30:54<26:24:27, 12.91s/it, avr_loss=0.025]
steps:  26%|██▋       | 2654/10016 [9:31:07<26:24:14, 12.91s/it, avr_loss=0.025]
steps:  26%|██▋       | 2654/10016 [9:31:07<26:24:14, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2655/10016 [9:31:20<26:24:01, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2655/10016 [9:31:20<26:24:01, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2656/10016 [9:31:33<26:23:48, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2656/10016 [9:31:33<26:23:48, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2657/10016 [9:31:45<26:23:35, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2657/10016 [9:31:45<26:23:35, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2658/10016 [9:31:58<26:23:22, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2658/10016 [9:31:58<26:23:22, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2659/10016 [9:32:11<26:23:10, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2659/10016 [9:32:11<26:23:10, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2660/10016 [9:32:24<26:22:57, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2660/10016 [9:32:24<26:22:57, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2661/10016 [9:32:37<26:22:44, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2661/10016 [9:32:37<26:22:44, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2662/10016 [9:32:50<26:22:31, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2662/10016 [9:32:50<26:22:31, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2663/10016 [9:33:03<26:22:18, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2663/10016 [9:33:03<26:22:18, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2664/10016 [9:33:16<26:22:05, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2664/10016 [9:33:16<26:22:05, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2665/10016 [9:33:29<26:21:52, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2665/10016 [9:33:29<26:21:52, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2666/10016 [9:33:41<26:21:39, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2666/10016 [9:33:41<26:21:39, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2667/10016 [9:33:54<26:21:26, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2667/10016 [9:33:54<26:21:26, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2668/10016 [9:34:07<26:21:13, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2668/10016 [9:34:07<26:21:13, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2669/10016 [9:34:20<26:21:00, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2669/10016 [9:34:20<26:21:00, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2670/10016 [9:34:33<26:20:47, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2670/10016 [9:34:33<26:20:47, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2671/10016 [9:34:46<26:20:34, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2671/10016 [9:34:46<26:20:34, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2672/10016 [9:34:59<26:20:21, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2672/10016 [9:34:59<26:20:21, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2673/10016 [9:35:12<26:20:08, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2673/10016 [9:35:12<26:20:08, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2674/10016 [9:35:25<26:19:55, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2674/10016 [9:35:25<26:19:55, 12.91s/it, avr_loss=0.025] 
steps:  27%|██▋       | 2675/10016 [9:35:37<26:19:42, 12.91s/it, avr_loss=0.025]
steps:  27%|██▋       | 2675/10016 [9:35:37<26:19:42, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2676/10016 [9:35:50<26:19:29, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2676/10016 [9:35:50<26:19:29, 12.91s/it, avr_loss=0.0249]
steps:  27%|██▋       | 2677/10016 [9:36:03<26:19:16, 12.91s/it, avr_loss=0.0249]
steps:  27%|██▋       | 2677/10016 [9:36:03<26:19:16, 12.91s/it, avr_loss=0.0249]
steps:  27%|██▋       | 2678/10016 [9:36:16<26:19:03, 12.91s/it, avr_loss=0.0249]
steps:  27%|██▋       | 2678/10016 [9:36:16<26:19:03, 12.91s/it, avr_loss=0.0249]
steps:  27%|██▋       | 2679/10016 [9:36:29<26:18:50, 12.91s/it, avr_loss=0.0249]
steps:  27%|██▋       | 2679/10016 [9:36:29<26:18:50, 12.91s/it, avr_loss=0.025] 
steps:  27%|██▋       | 2680/10016 [9:36:42<26:18:37, 12.91s/it, avr_loss=0.025]
steps:  27%|██▋       | 2680/10016 [9:36:42<26:18:37, 12.91s/it, avr_loss=0.025]
steps:  27%|██▋       | 2681/10016 [9:36:55<26:18:24, 12.91s/it, avr_loss=0.025]
steps:  27%|██▋       | 2681/10016 [9:36:55<26:18:24, 12.91s/it, avr_loss=0.0248]
steps:  27%|██▋       | 2682/10016 [9:37:08<26:18:11, 12.91s/it, avr_loss=0.0248]
steps:  27%|██▋       | 2682/10016 [9:37:08<26:18:11, 12.91s/it, avr_loss=0.0248]
steps:  27%|██▋       | 2683/10016 [9:37:21<26:17:58, 12.91s/it, avr_loss=0.0248]
steps:  27%|██▋       | 2683/10016 [9:37:21<26:17:58, 12.91s/it, avr_loss=0.0249]
steps:  27%|██▋       | 2684/10016 [9:37:33<26:17:45, 12.91s/it, avr_loss=0.0249]
steps:  27%|██▋       | 2684/10016 [9:37:33<26:17:45, 12.91s/it, avr_loss=0.0249]
steps:  27%|██▋       | 2685/10016 [9:37:46<26:17:32, 12.91s/it, avr_loss=0.0249]
steps:  27%|██▋       | 2685/10016 [9:37:46<26:17:32, 12.91s/it, avr_loss=0.0249]
steps:  27%|██▋       | 2686/10016 [9:37:59<26:17:19, 12.91s/it, avr_loss=0.0249]
steps:  27%|██▋       | 2686/10016 [9:37:59<26:17:19, 12.91s/it, avr_loss=0.0249]
steps:  27%|██▋       | 2687/10016 [9:38:12<26:17:06, 12.91s/it, avr_loss=0.0249]
steps:  27%|██▋       | 2687/10016 [9:38:12<26:17:06, 12.91s/it, avr_loss=0.0249]
steps:  27%|██▋       | 2688/10016 [9:38:25<26:16:53, 12.91s/it, avr_loss=0.0249]
steps:  27%|██▋       | 2688/10016 [9:38:25<26:16:53, 12.91s/it, avr_loss=0.0249]
steps:  27%|██▋       | 2689/10016 [9:38:38<26:16:40, 12.91s/it, avr_loss=0.0249]
steps:  27%|██▋       | 2689/10016 [9:38:38<26:16:40, 12.91s/it, avr_loss=0.0249]
steps:  27%|██▋       | 2690/10016 [9:38:51<26:16:27, 12.91s/it, avr_loss=0.0249]
steps:  27%|██▋       | 2690/10016 [9:38:51<26:16:27, 12.91s/it, avr_loss=0.0249]
steps:  27%|██▋       | 2691/10016 [9:39:04<26:16:14, 12.91s/it, avr_loss=0.0249]
steps:  27%|██▋       | 2691/10016 [9:39:04<26:16:14, 12.91s/it, avr_loss=0.0249]
steps:  27%|██▋       | 2692/10016 [9:39:17<26:16:01, 12.91s/it, avr_loss=0.0249]
steps:  27%|██▋       | 2692/10016 [9:39:17<26:16:01, 12.91s/it, avr_loss=0.025] 
steps:  27%|██▋       | 2693/10016 [9:39:29<26:15:49, 12.91s/it, avr_loss=0.025]
steps:  27%|██▋       | 2693/10016 [9:39:29<26:15:49, 12.91s/it, avr_loss=0.0249]
steps:  27%|██▋       | 2694/10016 [9:39:42<26:15:36, 12.91s/it, avr_loss=0.0249]
steps:  27%|██▋       | 2694/10016 [9:39:42<26:15:36, 12.91s/it, avr_loss=0.0249]
steps:  27%|██▋       | 2695/10016 [9:39:55<26:15:23, 12.91s/it, avr_loss=0.0249]
steps:  27%|██▋       | 2695/10016 [9:39:55<26:15:23, 12.91s/it, avr_loss=0.025] 
steps:  27%|██▋       | 2696/10016 [9:40:08<26:15:10, 12.91s/it, avr_loss=0.025]
steps:  27%|██▋       | 2696/10016 [9:40:08<26:15:10, 12.91s/it, avr_loss=0.025]
steps:  27%|██▋       | 2697/10016 [9:40:21<26:14:57, 12.91s/it, avr_loss=0.025]
steps:  27%|██▋       | 2697/10016 [9:40:21<26:14:57, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2698/10016 [9:40:34<26:14:44, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2698/10016 [9:40:34<26:14:44, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2699/10016 [9:40:47<26:14:31, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2699/10016 [9:40:47<26:14:31, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2700/10016 [9:41:00<26:14:18, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2700/10016 [9:41:00<26:14:18, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2701/10016 [9:41:13<26:14:05, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2701/10016 [9:41:13<26:14:05, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2702/10016 [9:41:25<26:13:52, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2702/10016 [9:41:25<26:13:52, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2703/10016 [9:41:38<26:13:39, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2703/10016 [9:41:38<26:13:39, 12.91s/it, avr_loss=0.025] 
steps:  27%|██▋       | 2704/10016 [9:41:51<26:13:26, 12.91s/it, avr_loss=0.025]
steps:  27%|██▋       | 2704/10016 [9:41:51<26:13:26, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2705/10016 [9:42:04<26:13:13, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2705/10016 [9:42:04<26:13:13, 12.91s/it, avr_loss=0.025] 
steps:  27%|██▋       | 2706/10016 [9:42:17<26:13:00, 12.91s/it, avr_loss=0.025]
steps:  27%|██▋       | 2706/10016 [9:42:17<26:13:00, 12.91s/it, avr_loss=0.025]
steps:  27%|██▋       | 2707/10016 [9:42:30<26:12:47, 12.91s/it, avr_loss=0.025]
steps:  27%|██▋       | 2707/10016 [9:42:30<26:12:47, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2708/10016 [9:42:43<26:12:34, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2708/10016 [9:42:43<26:12:34, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2709/10016 [9:42:56<26:12:21, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2709/10016 [9:42:56<26:12:21, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2710/10016 [9:43:09<26:12:08, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2710/10016 [9:43:09<26:12:08, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2711/10016 [9:43:22<26:11:55, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2711/10016 [9:43:22<26:11:55, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2712/10016 [9:43:34<26:11:42, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2712/10016 [9:43:34<26:11:42, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2713/10016 [9:43:47<26:11:29, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2713/10016 [9:43:47<26:11:29, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2714/10016 [9:44:00<26:11:16, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2714/10016 [9:44:00<26:11:16, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2715/10016 [9:44:13<26:11:03, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2715/10016 [9:44:13<26:11:03, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2716/10016 [9:44:26<26:10:50, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2716/10016 [9:44:26<26:10:50, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2717/10016 [9:44:39<26:10:38, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2717/10016 [9:44:39<26:10:38, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2718/10016 [9:44:52<26:10:25, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2718/10016 [9:44:52<26:10:25, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2719/10016 [9:45:05<26:10:12, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2719/10016 [9:45:05<26:10:12, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2720/10016 [9:45:18<26:09:59, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2720/10016 [9:45:18<26:09:59, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2721/10016 [9:45:31<26:09:46, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2721/10016 [9:45:31<26:09:46, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2722/10016 [9:45:44<26:09:33, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2722/10016 [9:45:44<26:09:33, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2723/10016 [9:45:56<26:09:20, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2723/10016 [9:45:56<26:09:20, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2724/10016 [9:46:09<26:09:07, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2724/10016 [9:46:09<26:09:07, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2725/10016 [9:46:22<26:08:54, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2725/10016 [9:46:22<26:08:54, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2726/10016 [9:46:35<26:08:41, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2726/10016 [9:46:35<26:08:41, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2727/10016 [9:46:48<26:08:29, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2727/10016 [9:46:48<26:08:29, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2728/10016 [9:47:01<26:08:16, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2728/10016 [9:47:01<26:08:16, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2729/10016 [9:47:14<26:08:03, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2729/10016 [9:47:14<26:08:03, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2730/10016 [9:47:27<26:07:50, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2730/10016 [9:47:27<26:07:50, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2731/10016 [9:47:40<26:07:37, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2731/10016 [9:47:40<26:07:37, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2732/10016 [9:47:53<26:07:24, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2732/10016 [9:47:53<26:07:24, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2733/10016 [9:48:05<26:07:11, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2733/10016 [9:48:05<26:07:11, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2734/10016 [9:48:18<26:06:58, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2734/10016 [9:48:18<26:06:58, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2735/10016 [9:48:31<26:06:45, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2735/10016 [9:48:31<26:06:45, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2736/10016 [9:48:44<26:06:32, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2736/10016 [9:48:44<26:06:32, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2737/10016 [9:48:57<26:06:19, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2737/10016 [9:48:57<26:06:19, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2738/10016 [9:49:10<26:06:06, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2738/10016 [9:49:10<26:06:06, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2739/10016 [9:49:23<26:05:53, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2739/10016 [9:49:23<26:05:53, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2740/10016 [9:49:36<26:05:40, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2740/10016 [9:49:36<26:05:40, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2741/10016 [9:49:49<26:05:27, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2741/10016 [9:49:49<26:05:27, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2742/10016 [9:50:02<26:05:14, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2742/10016 [9:50:02<26:05:14, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2743/10016 [9:50:14<26:05:01, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2743/10016 [9:50:14<26:05:01, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2744/10016 [9:50:27<26:04:48, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2744/10016 [9:50:27<26:04:48, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2745/10016 [9:50:40<26:04:35, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2745/10016 [9:50:40<26:04:35, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2746/10016 [9:50:53<26:04:22, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2746/10016 [9:50:53<26:04:22, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2747/10016 [9:51:06<26:04:09, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2747/10016 [9:51:06<26:04:09, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2748/10016 [9:51:19<26:03:57, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2748/10016 [9:51:19<26:03:57, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2749/10016 [9:51:32<26:03:43, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2749/10016 [9:51:32<26:03:43, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2750/10016 [9:51:45<26:03:30, 12.91s/it, avr_loss=0.0251]
steps:  27%|██▋       | 2750/10016 [9:51:45<26:03:30, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2751/10016 [9:51:57<26:03:17, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2751/10016 [9:51:57<26:03:17, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2752/10016 [9:52:10<26:03:05, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2752/10016 [9:52:10<26:03:05, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2753/10016 [9:52:23<26:02:52, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2753/10016 [9:52:23<26:02:52, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2754/10016 [9:52:36<26:02:39, 12.91s/it, avr_loss=0.0252]
steps:  27%|██▋       | 2754/10016 [9:52:36<26:02:39, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2755/10016 [9:52:49<26:02:25, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2755/10016 [9:52:49<26:02:25, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2756/10016 [9:53:02<26:02:12, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2756/10016 [9:53:02<26:02:12, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2757/10016 [9:53:15<26:01:59, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2757/10016 [9:53:15<26:01:59, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2758/10016 [9:53:28<26:01:46, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2758/10016 [9:53:28<26:01:46, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2759/10016 [9:53:41<26:01:33, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2759/10016 [9:53:41<26:01:33, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2760/10016 [9:53:53<26:01:20, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2760/10016 [9:53:53<26:01:20, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2761/10016 [9:54:06<26:01:07, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2761/10016 [9:54:06<26:01:07, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2762/10016 [9:54:19<26:00:54, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2762/10016 [9:54:19<26:00:54, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2763/10016 [9:54:32<26:00:42, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2763/10016 [9:54:32<26:00:42, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2764/10016 [9:54:45<26:00:29, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2764/10016 [9:54:45<26:00:29, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2765/10016 [9:54:58<26:00:16, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2765/10016 [9:54:58<26:00:16, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2766/10016 [9:55:11<26:00:03, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2766/10016 [9:55:11<26:00:03, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2767/10016 [9:55:24<25:59:50, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2767/10016 [9:55:24<25:59:50, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2768/10016 [9:55:36<25:59:37, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2768/10016 [9:55:36<25:59:37, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2769/10016 [9:55:49<25:59:24, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2769/10016 [9:55:49<25:59:24, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2770/10016 [9:56:02<25:59:11, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2770/10016 [9:56:02<25:59:11, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2771/10016 [9:56:15<25:58:58, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2771/10016 [9:56:15<25:58:58, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2772/10016 [9:56:28<25:58:45, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2772/10016 [9:56:28<25:58:45, 12.91s/it, avr_loss=0.0253]
steps:  28%|██▊       | 2773/10016 [9:56:41<25:58:32, 12.91s/it, avr_loss=0.0253]
steps:  28%|██▊       | 2773/10016 [9:56:41<25:58:32, 12.91s/it, avr_loss=0.0253]
steps:  28%|██▊       | 2774/10016 [9:56:54<25:58:19, 12.91s/it, avr_loss=0.0253]
steps:  28%|██▊       | 2774/10016 [9:56:54<25:58:19, 12.91s/it, avr_loss=0.0253]
steps:  28%|██▊       | 2775/10016 [9:57:07<25:58:06, 12.91s/it, avr_loss=0.0253]
steps:  28%|██▊       | 2775/10016 [9:57:07<25:58:06, 12.91s/it, avr_loss=0.0253]
steps:  28%|██▊       | 2776/10016 [9:57:19<25:57:53, 12.91s/it, avr_loss=0.0253]
steps:  28%|██▊       | 2776/10016 [9:57:19<25:57:53, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2777/10016 [9:57:32<25:57:40, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2777/10016 [9:57:32<25:57:40, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2778/10016 [9:57:45<25:57:27, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2778/10016 [9:57:45<25:57:27, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2779/10016 [9:57:58<25:57:14, 12.91s/it, avr_loss=0.0252]
steps:  28%|██▊       | 2779/10016 [9:57:58<25:57:14, 12.91s/it, avr_loss=0.0253]
steps:  28%|██▊       | 2780/10016 [9:58:11<25:57:01, 12.91s/it, avr_loss=0.0253]
steps:  28%|██▊       | 2780/10016 [9:58:11<25:57:01, 12.91s/it, avr_loss=0.0251]
steps:  28%|██▊       | 2781/10016 [9:58:24<25:56:48, 12.91s/it, avr_loss=0.0251]
steps:  28%|██▊       | 2781/10016 [9:58:24<25:56:48, 12.91s/it, avr_loss=0.0251]
steps:  28%|██▊       | 2782/10016 [9:58:37<25:56:35, 12.91s/it, avr_loss=0.0251]
steps:  28%|██▊       | 2782/10016 [9:58:37<25:56:35, 12.91s/it, avr_loss=0.025] 
steps:  28%|██▊       | 2783/10016 [9:58:50<25:56:22, 12.91s/it, avr_loss=0.025]
steps:  28%|██▊       | 2783/10016 [9:58:50<25:56:22, 12.91s/it, avr_loss=0.025]
steps:  28%|██▊       | 2784/10016 [9:59:03<25:56:09, 12.91s/it, avr_loss=0.025]
steps:  28%|██▊       | 2784/10016 [9:59:03<25:56:09, 12.91s/it, avr_loss=0.025]
steps:  28%|██▊       | 2785/10016 [9:59:15<25:55:56, 12.91s/it, avr_loss=0.025]
steps:  28%|██▊       | 2785/10016 [9:59:15<25:55:56, 12.91s/it, avr_loss=0.025]
steps:  28%|██▊       | 2786/10016 [9:59:28<25:55:43, 12.91s/it, avr_loss=0.025]
steps:  28%|██▊       | 2786/10016 [9:59:28<25:55:43, 12.91s/it, avr_loss=0.025]
steps:  28%|██▊       | 2787/10016 [9:59:41<25:55:30, 12.91s/it, avr_loss=0.025]
steps:  28%|██▊       | 2787/10016 [9:59:41<25:55:30, 12.91s/it, avr_loss=0.0249]
steps:  28%|██▊       | 2788/10016 [9:59:54<25:55:17, 12.91s/it, avr_loss=0.0249]
steps:  28%|██▊       | 2788/10016 [9:59:54<25:55:17, 12.91s/it, avr_loss=0.025] 
steps:  28%|██▊       | 2789/10016 [10:00:07<25:55:04, 12.91s/it, avr_loss=0.025]
steps:  28%|██▊       | 2789/10016 [10:00:07<25:55:04, 12.91s/it, avr_loss=0.0249]
steps:  28%|██▊       | 2790/10016 [10:00:20<25:54:51, 12.91s/it, avr_loss=0.0249]
steps:  28%|██▊       | 2790/10016 [10:00:20<25:54:51, 12.91s/it, avr_loss=0.0249]
steps:  28%|██▊       | 2791/10016 [10:00:33<25:54:38, 12.91s/it, avr_loss=0.0249]
steps:  28%|██▊       | 2791/10016 [10:00:33<25:54:38, 12.91s/it, avr_loss=0.0249]
steps:  28%|██▊       | 2792/10016 [10:00:46<25:54:25, 12.91s/it, avr_loss=0.0249]
steps:  28%|██▊       | 2792/10016 [10:00:46<25:54:25, 12.91s/it, avr_loss=0.0249]
steps:  28%|██▊       | 2793/10016 [10:00:58<25:54:12, 12.91s/it, avr_loss=0.0249]
steps:  28%|██▊       | 2793/10016 [10:00:58<25:54:12, 12.91s/it, avr_loss=0.0249]
steps:  28%|██▊       | 2794/10016 [10:01:11<25:53:59, 12.91s/it, avr_loss=0.0249]
steps:  28%|██▊       | 2794/10016 [10:01:11<25:53:59, 12.91s/it, avr_loss=0.0249]
steps:  28%|██▊       | 2795/10016 [10:01:24<25:53:46, 12.91s/it, avr_loss=0.0249]
steps:  28%|██▊       | 2795/10016 [10:01:24<25:53:46, 12.91s/it, avr_loss=0.0249]