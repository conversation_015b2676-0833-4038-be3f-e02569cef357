nohup: ignoring input
The following values were not passed to `accelerate launch` and had defaults used instead:
	`--num_machines` was set to a value of `1`
	`--mixed_precision` was set to a value of `'no'`
	`--dynamo_backend` was set to a value of `'no'`
To avoid this warning pass in values for each of the problematic parameters or run `accelerate config`.
Xformers is not installed!Xformers is not installed!

Xformers is not installed!
Flash Attn is not installed!
Flash Attn is not installed!
Xformers is not installed!
Sage Attn is not installed!Flash Attn is not installed!
Sage Attn is not installed!

Flash Attn is not installed!
Sage Attn is not installed!
Sage Attn is not installed!
Trying to import sageattention
Failed to import sageattention
Trying to import sageattention
Trying to import sageattention
Failed to import sageattention
Failed to import sageattention
Trying to import sageattention
Failed to import sageattention
INFO:hv_train_network:Load dataset config from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_config.toml
INFO:hv_train_network:Load dataset config from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_config.toml
INFO:hv_train_network:Load dataset config from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_config.toml
INFO:hv_train_network:Load dataset config from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_config.toml
WARNING:dataset.image_video_dataset:target_frames are rounded to [41, 69]
INFO:dataset.image_video_dataset:load video jsonl from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl
WARNING:dataset.image_video_dataset:target_frames are rounded to [41, 69]
INFO:dataset.image_video_dataset:load video jsonl from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl
WARNING:dataset.image_video_dataset:target_frames are rounded to [41, 69]
INFO:dataset.image_video_dataset:load video jsonl from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl
WARNING:dataset.image_video_dataset:target_frames are rounded to [41, 69]
INFO:dataset.image_video_dataset:load video jsonl from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl
INFO:dataset.image_video_dataset:loaded 100 videos
INFO:dataset.image_video_dataset:loaded 100 videos
INFO:dataset.config_utils:[Dataset 0]
  is_image_dataset: False
  resolution: (768, 512)
  batch_size: 1
  num_repeats: 1
  caption_extension: "None"
  enable_bucket: True
  bucket_no_upscale: False
  cache_directory: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_cache"
  debug_dataset: False
    video_directory: "None"
    video_jsonl_file: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl"
    control_directory: "None"
    target_frames: (41, 69)
    frame_extraction: slide
    frame_stride: 30
    frame_sample: 1
    max_frames: 129
    source_fps: 30.0


INFO:dataset.config_utils:[Dataset 0]
  is_image_dataset: False
  resolution: (768, 512)
  batch_size: 1
  num_repeats: 1
  caption_extension: "None"
  enable_bucket: True
  bucket_no_upscale: False
  cache_directory: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_cache"
  debug_dataset: False
    video_directory: "None"
    video_jsonl_file: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl"
    control_directory: "None"
    target_frames: (41, 69)
    frame_extraction: slide
    frame_stride: 30
    frame_sample: 1
    max_frames: 129
    source_fps: 30.0


INFO:dataset.image_video_dataset:loaded 100 videos
INFO:dataset.config_utils:[Dataset 0]
  is_image_dataset: False
  resolution: (768, 512)
  batch_size: 1
  num_repeats: 1
  caption_extension: "None"
  enable_bucket: True
  bucket_no_upscale: False
  cache_directory: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_cache"
  debug_dataset: False
    video_directory: "None"
    video_jsonl_file: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl"
    control_directory: "None"
    target_frames: (41, 69)
    frame_extraction: slide
    frame_stride: 30
    frame_sample: 1
    max_frames: 129
    source_fps: 30.0


INFO:dataset.image_video_dataset:loaded 100 videos
INFO:dataset.config_utils:[Dataset 0]
  is_image_dataset: False
  resolution: (768, 512)
  batch_size: 1
  num_repeats: 1
  caption_extension: "None"
  enable_bucket: True
  bucket_no_upscale: False
  cache_directory: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_cache"
  debug_dataset: False
    video_directory: "None"
    video_jsonl_file: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl"
    control_directory: "None"
    target_frames: (41, 69)
    frame_extraction: slide
    frame_stride: 30
    frame_sample: 1
    max_frames: 129
    source_fps: 30.0


INFO:dataset.image_video_dataset:bucket: (768, 512, 41), count: 1300
INFO:dataset.image_video_dataset:bucket: (768, 512, 69), count: 1204
INFO:dataset.image_video_dataset:total batches: 2504
INFO:dataset.image_video_dataset:bucket: (768, 512, 41), count: 1300
INFO:dataset.image_video_dataset:bucket: (768, 512, 41), count: 1300
INFO:dataset.image_video_dataset:bucket: (768, 512, 69), count: 1204
INFO:dataset.image_video_dataset:bucket: (768, 512, 69), count: 1204
INFO:dataset.image_video_dataset:total batches: 2504
INFO:dataset.image_video_dataset:total batches: 2504
INFO:dataset.image_video_dataset:bucket: (768, 512, 41), count: 1300
INFO:dataset.image_video_dataset:bucket: (768, 512, 69), count: 1204
INFO:dataset.image_video_dataset:total batches: 2504
INFO:hv_train_network:preparing accelerator
INFO:hv_train_network:preparing accelerator
INFO:hv_train_network:preparing accelerator
INFO:hv_train_network:preparing accelerator
[E708 23:42:41.510683947 socket.cpp:1019] [c10d] The client socket has timed out after 600000ms while trying to connect to (127.0.0.1, 0).
[W708 23:42:41.511120024 TCPStore.cpp:343] [c10d] TCP client failed to connect/validate to host 127.0.0.1:0 - retrying (try=0, timeout=600000ms, delay=83602ms): The client socket has timed out after 600000ms while trying to connect to (127.0.0.1, 0).
Exception raised from throwTimeoutError at /pytorch/torch/csrc/distributed/c10d/socket.cpp:1021 (most recent call first):
frame #0: c10::Error::Error(c10::SourceLocation, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >) + 0x98 (0x7f5dbb9785e8 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #1: <unknown function> + 0x5ba8afe (0x7f5da4836afe in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #2: <unknown function> + 0x1369113 (0x7f5d9fff7113 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #3: <unknown function> + 0x5bf5691 (0x7f5da4883691 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #4: <unknown function> + 0x5bf5849 (0x7f5da4883849 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #5: <unknown function> + 0x5bf5c01 (0x7f5da4883c01 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #6: <unknown function> + 0x5ba3deb (0x7f5da4831deb in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #7: c10d::TCPStore::TCPStore(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, c10d::TCPStoreOptions const&) + 0x4b5 (0x7f5da48346f5 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #8: <unknown function> + 0xc1e575 (0x7f5db3b76575 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #9: <unknown function> + 0xc52e64 (0x7f5db3baae64 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #10: <unknown function> + 0x3895ce (0x7f5db32e15ce in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #11: <unknown function> + 0x13d0e6 (0x55e4504d50e6 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #12: _PyObject_MakeTpCall + 0x2d3 (0x55e4504ce0b3 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #13: <unknown function> + 0x1490b6 (0x55e4504e10b6 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #14: PyVectorcall_Call + 0xc9 (0x55e4504e1c59 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #15: <unknown function> + 0x146cc4 (0x55e4504decc4 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #16: <unknown function> + 0x1363bb (0x55e4504ce3bb in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #17: <unknown function> + 0x38815b (0x7f5db32e015b in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #18: _PyObject_MakeTpCall + 0x2d3 (0x55e4504ce0b3 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #19: _PyEval_EvalFrameDefault + 0x5362 (0x55e4504ca332 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #20: _PyFunction_Vectorcall + 0x6c (0x55e4504d556c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #21: _PyEval_EvalFrameDefault + 0x30c (0x55e4504c52dc in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #22: <unknown function> + 0x1ae3e0 (0x55e4505463e0 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #23: <unknown function> + 0x13d733 (0x55e4504d5733 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #24: _PyEval_EvalFrameDefault + 0x30c (0x55e4504c52dc in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #25: _PyFunction_Vectorcall + 0x6c (0x55e4504d556c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #26: PyObject_Call + 0xbc (0x55e4504e18dc in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #27: _PyEval_EvalFrameDefault + 0x2c2a (0x55e4504c7bfa in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #28: _PyFunction_Vectorcall + 0x6c (0x55e4504d556c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #29: PyObject_Call + 0xbc (0x55e4504e18dc in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #30: _PyEval_EvalFrameDefault + 0x2c2a (0x55e4504c7bfa in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #31: _PyFunction_Vectorcall + 0x6c (0x55e4504d556c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #32: PyObject_Call + 0xbc (0x55e4504e18dc in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #33: _PyEval_EvalFrameDefault + 0x2c2a (0x55e4504c7bfa in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #34: _PyObject_FastCallDictTstate + 0xd0 (0x55e4504cd3d0 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #35: <unknown function> + 0x146799 (0x55e4504de799 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #36: <unknown function> + 0x1363bb (0x55e4504ce3bb in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #37: PyObject_Call + 0x20f (0x55e4504e1a2f in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #38: _PyEval_EvalFrameDefault + 0x2c2a (0x55e4504c7bfa in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #39: _PyFunction_Vectorcall + 0x6c (0x55e4504d556c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #40: _PyObject_FastCallDictTstate + 0x187 (0x55e4504cd487 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #41: <unknown function> + 0x146799 (0x55e4504de799 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #42: <unknown function> + 0x1363bb (0x55e4504ce3bb in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #43: PyObject_Call + 0x20f (0x55e4504e1a2f in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #44: _PyEval_EvalFrameDefault + 0x2c2a (0x55e4504c7bfa in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #45: _PyFunction_Vectorcall + 0x6c (0x55e4504d556c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #46: _PyObject_FastCallDictTstate + 0x187 (0x55e4504cd487 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #47: <unknown function> + 0x146799 (0x55e4504de799 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #48: _PyObject_MakeTpCall + 0x2eb (0x55e4504ce0cb in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #49: _PyEval_EvalFrameDefault + 0x5362 (0x55e4504ca332 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #50: _PyFunction_Vectorcall + 0x6c (0x55e4504d556c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #51: _PyEval_EvalFrameDefault + 0x30c (0x55e4504c52dc in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #52: _PyFunction_Vectorcall + 0x6c (0x55e4504d556c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #53: _PyEval_EvalFrameDefault + 0x700 (0x55e4504c56d0 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #54: <unknown function> + 0x1cfd2c (0x55e450567d2c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #55: PyEval_EvalCode + 0x87 (0x55e450567c77 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #56: <unknown function> + 0x2001da (0x55e4505981da in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #57: <unknown function> + 0x1fb663 (0x55e450593663 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #58: <unknown function> + 0x975bf (0x55e45042f5bf in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #59: _PyRun_SimpleFileObject + 0x1bd (0x55e45058de9d in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #60: _PyRun_AnyFileObject + 0x44 (0x55e45058da34 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #61: Py_RunMain + 0x31b (0x55e45058ad9b in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #62: Py_BytesMain + 0x37 (0x55e45055b897 in /media/miniconda3/envs/musubi_copy/bin/python3.10)

[E708 23:43:04.080390882 socket.cpp:1019] [c10d] The client socket has timed out after 600000ms while trying to connect to (127.0.0.1, 0).
[W708 23:43:04.080784890 TCPStore.cpp:343] [c10d] TCP client failed to connect/validate to host 127.0.0.1:0 - retrying (try=0, timeout=600000ms, delay=81814ms): The client socket has timed out after 600000ms while trying to connect to (127.0.0.1, 0).
Exception raised from throwTimeoutError at /pytorch/torch/csrc/distributed/c10d/socket.cpp:1021 (most recent call first):
frame #0: c10::Error::Error(c10::SourceLocation, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >) + 0x98 (0x7f7da47785e8 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #1: <unknown function> + 0x5ba8afe (0x7f7d8da36afe in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #2: <unknown function> + 0x1369113 (0x7f7d891f7113 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #3: <unknown function> + 0x5bf5691 (0x7f7d8da83691 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #4: <unknown function> + 0x5bf5849 (0x7f7d8da83849 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #5: <unknown function> + 0x5bf5c01 (0x7f7d8da83c01 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #6: <unknown function> + 0x5ba3deb (0x7f7d8da31deb in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #7: c10d::TCPStore::TCPStore(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, c10d::TCPStoreOptions const&) + 0x4b5 (0x7f7d8da346f5 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #8: <unknown function> + 0xc1e575 (0x7f7d9cd76575 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #9: <unknown function> + 0xc52e64 (0x7f7d9cdaae64 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #10: <unknown function> + 0x3895ce (0x7f7d9c4e15ce in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #11: <unknown function> + 0x13d0e6 (0x55ee755d20e6 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #12: _PyObject_MakeTpCall + 0x2d3 (0x55ee755cb0b3 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #13: <unknown function> + 0x1490b6 (0x55ee755de0b6 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #14: PyVectorcall_Call + 0xc9 (0x55ee755dec59 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #15: <unknown function> + 0x146cc4 (0x55ee755dbcc4 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #16: <unknown function> + 0x1363bb (0x55ee755cb3bb in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #17: <unknown function> + 0x38815b (0x7f7d9c4e015b in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #18: _PyObject_MakeTpCall + 0x2d3 (0x55ee755cb0b3 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #19: _PyEval_EvalFrameDefault + 0x5362 (0x55ee755c7332 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #20: _PyFunction_Vectorcall + 0x6c (0x55ee755d256c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #21: _PyEval_EvalFrameDefault + 0x30c (0x55ee755c22dc in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #22: <unknown function> + 0x1ae3e0 (0x55ee756433e0 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #23: <unknown function> + 0x13d733 (0x55ee755d2733 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #24: _PyEval_EvalFrameDefault + 0x30c (0x55ee755c22dc in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #25: _PyFunction_Vectorcall + 0x6c (0x55ee755d256c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #26: PyObject_Call + 0xbc (0x55ee755de8dc in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #27: _PyEval_EvalFrameDefault + 0x2c2a (0x55ee755c4bfa in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #28: _PyFunction_Vectorcall + 0x6c (0x55ee755d256c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #29: PyObject_Call + 0xbc (0x55ee755de8dc in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #30: _PyEval_EvalFrameDefault + 0x2c2a (0x55ee755c4bfa in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #31: _PyFunction_Vectorcall + 0x6c (0x55ee755d256c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #32: PyObject_Call + 0xbc (0x55ee755de8dc in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #33: _PyEval_EvalFrameDefault + 0x2c2a (0x55ee755c4bfa in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #34: _PyObject_FastCallDictTstate + 0xd0 (0x55ee755ca3d0 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #35: <unknown function> + 0x146799 (0x55ee755db799 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #36: <unknown function> + 0x1363bb (0x55ee755cb3bb in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #37: PyObject_Call + 0x20f (0x55ee755dea2f in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #38: _PyEval_EvalFrameDefault + 0x2c2a (0x55ee755c4bfa in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #39: _PyFunction_Vectorcall + 0x6c (0x55ee755d256c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #40: _PyObject_FastCallDictTstate + 0x187 (0x55ee755ca487 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #41: <unknown function> + 0x146799 (0x55ee755db799 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #42: <unknown function> + 0x1363bb (0x55ee755cb3bb in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #43: PyObject_Call + 0x20f (0x55ee755dea2f in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #44: _PyEval_EvalFrameDefault + 0x2c2a (0x55ee755c4bfa in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #45: _PyFunction_Vectorcall + 0x6c (0x55ee755d256c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #46: _PyObject_FastCallDictTstate + 0x187 (0x55ee755ca487 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #47: <unknown function> + 0x146799 (0x55ee755db799 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #48: _PyObject_MakeTpCall + 0x2eb (0x55ee755cb0cb in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #49: _PyEval_EvalFrameDefault + 0x5362 (0x55ee755c7332 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #50: _PyFunction_Vectorcall + 0x6c (0x55ee755d256c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #51: _PyEval_EvalFrameDefault + 0x30c (0x55ee755c22dc in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #52: _PyFunction_Vectorcall + 0x6c (0x55ee755d256c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #53: _PyEval_EvalFrameDefault + 0x700 (0x55ee755c26d0 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #54: <unknown function> + 0x1cfd2c (0x55ee75664d2c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #55: PyEval_EvalCode + 0x87 (0x55ee75664c77 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #56: <unknown function> + 0x2001da (0x55ee756951da in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #57: <unknown function> + 0x1fb663 (0x55ee75690663 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #58: <unknown function> + 0x975bf (0x55ee7552c5bf in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #59: _PyRun_SimpleFileObject + 0x1bd (0x55ee7568ae9d in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #60: _PyRun_AnyFileObject + 0x44 (0x55ee7568aa34 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #61: Py_RunMain + 0x31b (0x55ee75687d9b in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #62: Py_BytesMain + 0x37 (0x55ee75658897 in /media/miniconda3/envs/musubi_copy/bin/python3.10)

[E708 23:43:26.213473398 socket.cpp:1019] [c10d] The client socket has timed out after 600000ms while trying to connect to (127.0.0.1, 0).
[W708 23:43:26.213815449 TCPStore.cpp:343] [c10d] TCP client failed to connect/validate to host 127.0.0.1:0 - retrying (try=0, timeout=600000ms, delay=73791ms): The client socket has timed out after 600000ms while trying to connect to (127.0.0.1, 0).
Exception raised from throwTimeoutError at /pytorch/torch/csrc/distributed/c10d/socket.cpp:1021 (most recent call first):
frame #0: c10::Error::Error(c10::SourceLocation, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >) + 0x98 (0x7f699c1785e8 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #1: <unknown function> + 0x5ba8afe (0x7f6985036afe in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #2: <unknown function> + 0x1369113 (0x7f69807f7113 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #3: <unknown function> + 0x5bf5691 (0x7f6985083691 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #4: <unknown function> + 0x5bf5849 (0x7f6985083849 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #5: <unknown function> + 0x5bf5c01 (0x7f6985083c01 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #6: <unknown function> + 0x5ba3deb (0x7f6985031deb in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #7: c10d::TCPStore::TCPStore(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, c10d::TCPStoreOptions const&) + 0x4b5 (0x7f69850346f5 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #8: <unknown function> + 0xc1e575 (0x7f6994376575 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #9: <unknown function> + 0xc52e64 (0x7f69943aae64 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #10: <unknown function> + 0x3895ce (0x7f6993ae15ce in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #11: <unknown function> + 0x13d0e6 (0x55a5e20310e6 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #12: _PyObject_MakeTpCall + 0x2d3 (0x55a5e202a0b3 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #13: <unknown function> + 0x1490b6 (0x55a5e203d0b6 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #14: PyVectorcall_Call + 0xc9 (0x55a5e203dc59 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #15: <unknown function> + 0x146cc4 (0x55a5e203acc4 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #16: <unknown function> + 0x1363bb (0x55a5e202a3bb in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #17: <unknown function> + 0x38815b (0x7f6993ae015b in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #18: _PyObject_MakeTpCall + 0x2d3 (0x55a5e202a0b3 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #19: _PyEval_EvalFrameDefault + 0x5362 (0x55a5e2026332 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #20: _PyFunction_Vectorcall + 0x6c (0x55a5e203156c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #21: _PyEval_EvalFrameDefault + 0x30c (0x55a5e20212dc in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #22: <unknown function> + 0x1ae3e0 (0x55a5e20a23e0 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #23: <unknown function> + 0x13d733 (0x55a5e2031733 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #24: _PyEval_EvalFrameDefault + 0x30c (0x55a5e20212dc in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #25: _PyFunction_Vectorcall + 0x6c (0x55a5e203156c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #26: PyObject_Call + 0xbc (0x55a5e203d8dc in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #27: _PyEval_EvalFrameDefault + 0x2c2a (0x55a5e2023bfa in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #28: _PyFunction_Vectorcall + 0x6c (0x55a5e203156c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #29: PyObject_Call + 0xbc (0x55a5e203d8dc in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #30: _PyEval_EvalFrameDefault + 0x2c2a (0x55a5e2023bfa in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #31: _PyFunction_Vectorcall + 0x6c (0x55a5e203156c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #32: PyObject_Call + 0xbc (0x55a5e203d8dc in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #33: _PyEval_EvalFrameDefault + 0x2c2a (0x55a5e2023bfa in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #34: _PyObject_FastCallDictTstate + 0xd0 (0x55a5e20293d0 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #35: <unknown function> + 0x146799 (0x55a5e203a799 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #36: <unknown function> + 0x1363bb (0x55a5e202a3bb in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #37: PyObject_Call + 0x20f (0x55a5e203da2f in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #38: _PyEval_EvalFrameDefault + 0x2c2a (0x55a5e2023bfa in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #39: _PyFunction_Vectorcall + 0x6c (0x55a5e203156c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #40: _PyObject_FastCallDictTstate + 0x187 (0x55a5e2029487 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #41: <unknown function> + 0x146799 (0x55a5e203a799 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #42: <unknown function> + 0x1363bb (0x55a5e202a3bb in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #43: PyObject_Call + 0x20f (0x55a5e203da2f in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #44: _PyEval_EvalFrameDefault + 0x2c2a (0x55a5e2023bfa in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #45: _PyFunction_Vectorcall + 0x6c (0x55a5e203156c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #46: _PyObject_FastCallDictTstate + 0x187 (0x55a5e2029487 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #47: <unknown function> + 0x146799 (0x55a5e203a799 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #48: _PyObject_MakeTpCall + 0x2eb (0x55a5e202a0cb in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #49: _PyEval_EvalFrameDefault + 0x5362 (0x55a5e2026332 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #50: _PyFunction_Vectorcall + 0x6c (0x55a5e203156c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #51: _PyEval_EvalFrameDefault + 0x30c (0x55a5e20212dc in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #52: _PyFunction_Vectorcall + 0x6c (0x55a5e203156c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #53: _PyEval_EvalFrameDefault + 0x700 (0x55a5e20216d0 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #54: <unknown function> + 0x1cfd2c (0x55a5e20c3d2c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #55: PyEval_EvalCode + 0x87 (0x55a5e20c3c77 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #56: <unknown function> + 0x2001da (0x55a5e20f41da in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #57: <unknown function> + 0x1fb663 (0x55a5e20ef663 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #58: <unknown function> + 0x975bf (0x55a5e1f8b5bf in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #59: _PyRun_SimpleFileObject + 0x1bd (0x55a5e20e9e9d in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #60: _PyRun_AnyFileObject + 0x44 (0x55a5e20e9a34 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #61: Py_RunMain + 0x31b (0x55a5e20e6d9b in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #62: Py_BytesMain + 0x37 (0x55a5e20b7897 in /media/miniconda3/envs/musubi_copy/bin/python3.10)

[E708 23:43:36.289039359 socket.cpp:1019] [c10d] The client socket has timed out after 600000ms while trying to connect to (127.0.0.1, 0).
[W708 23:43:36.289460801 TCPStore.cpp:343] [c10d] TCP client failed to connect/validate to host 127.0.0.1:0 - retrying (try=0, timeout=600000ms, delay=64864ms): The client socket has timed out after 600000ms while trying to connect to (127.0.0.1, 0).
Exception raised from throwTimeoutError at /pytorch/torch/csrc/distributed/c10d/socket.cpp:1021 (most recent call first):
frame #0: c10::Error::Error(c10::SourceLocation, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >) + 0x98 (0x7fabadf785e8 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #1: <unknown function> + 0x5ba8afe (0x7fab96e36afe in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #2: <unknown function> + 0x1369113 (0x7fab925f7113 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #3: <unknown function> + 0x5bf5691 (0x7fab96e83691 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #4: <unknown function> + 0x5bf5849 (0x7fab96e83849 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #5: <unknown function> + 0x5bf5c01 (0x7fab96e83c01 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #6: <unknown function> + 0x5ba3deb (0x7fab96e31deb in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #7: c10d::TCPStore::TCPStore(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, c10d::TCPStoreOptions const&) + 0x4b5 (0x7fab96e346f5 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #8: <unknown function> + 0xc1e575 (0x7faba6176575 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #9: <unknown function> + 0xc52e64 (0x7faba61aae64 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #10: <unknown function> + 0x3895ce (0x7faba58e15ce in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #11: <unknown function> + 0x13d0e6 (0x561a5d6a10e6 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #12: _PyObject_MakeTpCall + 0x2d3 (0x561a5d69a0b3 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #13: <unknown function> + 0x1490b6 (0x561a5d6ad0b6 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #14: PyVectorcall_Call + 0xc9 (0x561a5d6adc59 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #15: <unknown function> + 0x146cc4 (0x561a5d6aacc4 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #16: <unknown function> + 0x1363bb (0x561a5d69a3bb in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #17: <unknown function> + 0x38815b (0x7faba58e015b in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #18: _PyObject_MakeTpCall + 0x2d3 (0x561a5d69a0b3 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #19: _PyEval_EvalFrameDefault + 0x5362 (0x561a5d696332 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #20: _PyFunction_Vectorcall + 0x6c (0x561a5d6a156c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #21: _PyEval_EvalFrameDefault + 0x30c (0x561a5d6912dc in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #22: <unknown function> + 0x1ae3e0 (0x561a5d7123e0 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #23: <unknown function> + 0x13d733 (0x561a5d6a1733 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #24: _PyEval_EvalFrameDefault + 0x30c (0x561a5d6912dc in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #25: _PyFunction_Vectorcall + 0x6c (0x561a5d6a156c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #26: PyObject_Call + 0xbc (0x561a5d6ad8dc in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #27: _PyEval_EvalFrameDefault + 0x2c2a (0x561a5d693bfa in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #28: _PyFunction_Vectorcall + 0x6c (0x561a5d6a156c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #29: PyObject_Call + 0xbc (0x561a5d6ad8dc in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #30: _PyEval_EvalFrameDefault + 0x2c2a (0x561a5d693bfa in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #31: _PyFunction_Vectorcall + 0x6c (0x561a5d6a156c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #32: PyObject_Call + 0xbc (0x561a5d6ad8dc in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #33: _PyEval_EvalFrameDefault + 0x2c2a (0x561a5d693bfa in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #34: _PyObject_FastCallDictTstate + 0xd0 (0x561a5d6993d0 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #35: <unknown function> + 0x146799 (0x561a5d6aa799 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #36: <unknown function> + 0x1363bb (0x561a5d69a3bb in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #37: PyObject_Call + 0x20f (0x561a5d6ada2f in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #38: _PyEval_EvalFrameDefault + 0x2c2a (0x561a5d693bfa in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #39: _PyFunction_Vectorcall + 0x6c (0x561a5d6a156c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #40: _PyObject_FastCallDictTstate + 0x187 (0x561a5d699487 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #41: <unknown function> + 0x146799 (0x561a5d6aa799 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #42: <unknown function> + 0x1363bb (0x561a5d69a3bb in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #43: PyObject_Call + 0x20f (0x561a5d6ada2f in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #44: _PyEval_EvalFrameDefault + 0x2c2a (0x561a5d693bfa in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #45: _PyFunction_Vectorcall + 0x6c (0x561a5d6a156c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #46: _PyObject_FastCallDictTstate + 0x187 (0x561a5d699487 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #47: <unknown function> + 0x146799 (0x561a5d6aa799 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #48: _PyObject_MakeTpCall + 0x2eb (0x561a5d69a0cb in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #49: _PyEval_EvalFrameDefault + 0x5362 (0x561a5d696332 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #50: _PyFunction_Vectorcall + 0x6c (0x561a5d6a156c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #51: _PyEval_EvalFrameDefault + 0x30c (0x561a5d6912dc in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #52: _PyFunction_Vectorcall + 0x6c (0x561a5d6a156c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #53: _PyEval_EvalFrameDefault + 0x700 (0x561a5d6916d0 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #54: <unknown function> + 0x1cfd2c (0x561a5d733d2c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #55: PyEval_EvalCode + 0x87 (0x561a5d733c77 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #56: <unknown function> + 0x2001da (0x561a5d7641da in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #57: <unknown function> + 0x1fb663 (0x561a5d75f663 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #58: <unknown function> + 0x975bf (0x561a5d5fb5bf in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #59: _PyRun_SimpleFileObject + 0x1bd (0x561a5d759e9d in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #60: _PyRun_AnyFileObject + 0x44 (0x561a5d759a34 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #61: Py_RunMain + 0x31b (0x561a5d756d9b in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #62: Py_BytesMain + 0x37 (0x561a5d727897 in /media/miniconda3/envs/musubi_copy/bin/python3.10)

