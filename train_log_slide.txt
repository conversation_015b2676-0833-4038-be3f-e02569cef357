nohup: ignoring input
The following values were not passed to `accelerate launch` and had defaults used instead:
	`--num_machines` was set to a value of `1`
	`--mixed_precision` was set to a value of `'no'`
	`--dynamo_backend` was set to a value of `'no'`
To avoid this warning pass in values for each of the problematic parameters or run `accelerate config`.
Xformers is not installed!Xformers is not installed!

Flash Attn is not installed!
Flash Attn is not installed!
Xformers is not installed!
Xformers is not installed!
Sage Attn is not installed!
Sage Attn is not installed!
Flash Attn is not installed!
Flash Attn is not installed!
Sage Attn is not installed!
Sage Attn is not installed!
Trying to import sageattention
Failed to import sageattention
Trying to import sageattention
Trying to import sageattention
Trying to import sageattention
Failed to import sageattention
Failed to import sageattention
Failed to import sageattention
INFO:hv_train_network:Load dataset config from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_config.toml
INFO:hv_train_network:Load dataset config from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_config.toml
INFO:hv_train_network:Load dataset config from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_config.toml
INFO:hv_train_network:Load dataset config from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_config.toml
WARNING:dataset.image_video_dataset:target_frames are rounded to [41, 69]
INFO:dataset.image_video_dataset:load video jsonl from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl
WARNING:dataset.image_video_dataset:target_frames are rounded to [41, 69]
WARNING:dataset.image_video_dataset:target_frames are rounded to [41, 69]
INFO:dataset.image_video_dataset:load video jsonl from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl
INFO:dataset.image_video_dataset:load video jsonl from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl
WARNING:dataset.image_video_dataset:target_frames are rounded to [41, 69]
INFO:dataset.image_video_dataset:loaded 100 videos
INFO:dataset.image_video_dataset:load video jsonl from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl
INFO:dataset.config_utils:[Dataset 0]
  is_image_dataset: False
  resolution: (768, 512)
  batch_size: 1
  num_repeats: 1
  caption_extension: "None"
  enable_bucket: True
  bucket_no_upscale: False
  cache_directory: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_cache"
  debug_dataset: False
    video_directory: "None"
    video_jsonl_file: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl"
    control_directory: "None"
    target_frames: (41, 69)
    frame_extraction: slide
    frame_stride: 30
    frame_sample: 1
    max_frames: 129
    source_fps: 30.0


INFO:dataset.image_video_dataset:loaded 100 videos
INFO:dataset.image_video_dataset:loaded 100 videos
INFO:dataset.config_utils:[Dataset 0]
  is_image_dataset: False
  resolution: (768, 512)
  batch_size: 1
  num_repeats: 1
  caption_extension: "None"
  enable_bucket: True
  bucket_no_upscale: False
  cache_directory: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_cache"
  debug_dataset: False
    video_directory: "None"
    video_jsonl_file: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl"
    control_directory: "None"
    target_frames: (41, 69)
    frame_extraction: slide
    frame_stride: 30
    frame_sample: 1
    max_frames: 129
    source_fps: 30.0


INFO:dataset.config_utils:[Dataset 0]
  is_image_dataset: False
  resolution: (768, 512)
  batch_size: 1
  num_repeats: 1
  caption_extension: "None"
  enable_bucket: True
  bucket_no_upscale: False
  cache_directory: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_cache"
  debug_dataset: False
    video_directory: "None"
    video_jsonl_file: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl"
    control_directory: "None"
    target_frames: (41, 69)
    frame_extraction: slide
    frame_stride: 30
    frame_sample: 1
    max_frames: 129
    source_fps: 30.0


INFO:dataset.image_video_dataset:loaded 100 videos
INFO:dataset.config_utils:[Dataset 0]
  is_image_dataset: False
  resolution: (768, 512)
  batch_size: 1
  num_repeats: 1
  caption_extension: "None"
  enable_bucket: True
  bucket_no_upscale: False
  cache_directory: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_cache"
  debug_dataset: False
    video_directory: "None"
    video_jsonl_file: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl"
    control_directory: "None"
    target_frames: (41, 69)
    frame_extraction: slide
    frame_stride: 30
    frame_sample: 1
    max_frames: 129
    source_fps: 30.0


INFO:dataset.image_video_dataset:bucket: (768, 512, 41), count: 1300
INFO:dataset.image_video_dataset:bucket: (768, 512, 69), count: 1204
INFO:dataset.image_video_dataset:total batches: 2504
INFO:dataset.image_video_dataset:bucket: (768, 512, 41), count: 1300
INFO:dataset.image_video_dataset:bucket: (768, 512, 41), count: 1300
INFO:dataset.image_video_dataset:bucket: (768, 512, 41), count: 1300
INFO:dataset.image_video_dataset:bucket: (768, 512, 69), count: 1204
INFO:dataset.image_video_dataset:bucket: (768, 512, 69), count: 1204
INFO:dataset.image_video_dataset:bucket: (768, 512, 69), count: 1204
INFO:dataset.image_video_dataset:total batches: 2504
INFO:dataset.image_video_dataset:total batches: 2504
INFO:dataset.image_video_dataset:total batches: 2504
INFO:hv_train_network:preparing accelerator
INFO:hv_train_network:preparing accelerator
INFO:hv_train_network:preparing accelerator
INFO:hv_train_network:preparing accelerator
accelerator device: cuda:0
INFO:hv_train_network:DiT precision: torch.bfloat16, weight precision: torch.bfloat16
INFO:hv_train_network:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:__main__:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:frame_pack.hunyuan_video_packed:Creating HunyuanVideoTransformer3DModelPacked
INFO:frame_pack.hunyuan_video_packed:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, device=cuda:0
accelerator device: cuda:1
INFO:hv_train_network:DiT precision: torch.bfloat16, weight precision: torch.bfloat16
INFO:hv_train_network:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:__main__:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:frame_pack.hunyuan_video_packed:Creating HunyuanVideoTransformer3DModelPacked
accelerator device: cuda:3
INFO:hv_train_network:DiT precision: torch.bfloat16, weight precision: torch.bfloat16
INFO:hv_train_network:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:__main__:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
accelerator device: cuda:2
INFO:hv_train_network:DiT precision: torch.bfloat16, weight precision: torch.bfloat16
INFO:hv_train_network:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:frame_pack.hunyuan_video_packed:Creating HunyuanVideoTransformer3DModelPacked
INFO:__main__:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:frame_pack.hunyuan_video_packed:Creating HunyuanVideoTransformer3DModelPacked
INFO:frame_pack.hunyuan_video_packed:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, device=cuda:1
INFO:frame_pack.hunyuan_video_packed:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, device=cuda:3
INFO:frame_pack.hunyuan_video_packed:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, device=cuda:2
INFO:frame_pack.hunyuan_video_packed:Loaded DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, info=<All keys matched successfully>
INFO:networks.lora:create LoRA network. base dim (rank): 32, alpha: 1
INFO:networks.lora:neuron dropout: p=None, rank dropout: p=None, module dropout: p=None
INFO:frame_pack.hunyuan_video_packed:Loaded DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, info=<All keys matched successfully>
INFO:frame_pack.hunyuan_video_packed:Loaded DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, info=<All keys matched successfully>
INFO:networks.lora:create LoRA network. base dim (rank): 32, alpha: 1
INFO:networks.lora:neuron dropout: p=None, rank dropout: p=None, module dropout: p=None
import network module: networks.lora_framepack
INFO:networks.lora:create LoRA network. base dim (rank): 32, alpha: 1
INFO:networks.lora:neuron dropout: p=None, rank dropout: p=None, module dropout: p=None
INFO:frame_pack.hunyuan_video_packed:Loaded DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, info=<All keys matched successfully>
INFO:networks.lora:create LoRA network. base dim (rank): 32, alpha: 1
INFO:networks.lora:neuron dropout: p=None, rank dropout: p=None, module dropout: p=None
INFO:networks.lora:create LoRA for U-Net/DiT: 440 modules.
INFO:networks.lora:enable LoRA for U-Net: 440 modules
Gradient checkpointing enabled for HunyuanVideoTransformer3DModelPacked.
INFO:hv_train_network:use 8-bit AdamW optimizer | {}
INFO:networks.lora:create LoRA for U-Net/DiT: 440 modules.
INFO:networks.lora:enable LoRA for U-Net: 440 modules
INFO:networks.lora:create LoRA for U-Net/DiT: 440 modules.
INFO:networks.lora:enable LoRA for U-Net: 440 modules
Gradient checkpointing enabled for HunyuanVideoTransformer3DModelPacked.
prepare optimizer, data loader etc.
Gradient checkpointing enabled for HunyuanVideoTransformer3DModelPacked.
INFO:hv_train_network:use 8-bit AdamW optimizer | {}
INFO:hv_train_network:use 8-bit AdamW optimizer | {}
override steps. steps for 16 epochs is / 指定エポックまでのステップ数: 10016
INFO:networks.lora:create LoRA for U-Net/DiT: 440 modules.
INFO:networks.lora:enable LoRA for U-Net: 440 modules
Gradient checkpointing enabled for HunyuanVideoTransformer3DModelPacked.
INFO:hv_train_network:use 8-bit AdamW optimizer | {}
INFO:hv_train_network:set DiT model name for metadata: /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:hv_train_network:set VAE model name for metadata: /media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/vae/diffusion_pytorch_model.safetensors
running training / 学習開始
INFO:hv_train_network:set DiT model name for metadata: /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
  num train items / 学習画像、動画数: 2504
  num batches per epoch / 1epochのバッチ数: 626
  num epochs / epoch数: 16
  batch size per device / バッチサイズ: 1
  gradient accumulation steps / 勾配を合計するステップ数 = 1
  total optimization steps / 学習ステップ数: 10016
INFO:hv_train_network:set VAE model name for metadata: /media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/vae/diffusion_pytorch_model.safetensors
INFO:hv_train_network:set DiT model name for metadata: /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:hv_train_network:set VAE model name for metadata: /media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/vae/diffusion_pytorch_model.safetensors
INFO:hv_train_network:set DiT model name for metadata: /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:hv_train_network:set VAE model name for metadata: /media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/vae/diffusion_pytorch_model.safetensors
INFO:hv_train_network:DiT dtype: torch.bfloat16, device: cuda:1
INFO:hv_train_network:DiT dtype: torch.bfloat16, device: cuda:3
INFO:hv_train_network:DiT dtype: torch.bfloat16, device: cuda:2

steps:   0%|          | 0/10016 [00:00<?, ?it/s]INFO:hv_train_network:DiT dtype: torch.bfloat16, device: cuda:0

epoch 1/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1

steps:   0%|          | 1/10016 [00:14<41:26:56, 14.90s/it]
steps:   0%|          | 1/10016 [00:14<41:27:01, 14.90s/it, avr_loss=0.0273]
steps:   0%|          | 2/10016 [00:27<38:29:00, 13.83s/it, avr_loss=0.0273]
steps:   0%|          | 2/10016 [00:27<38:29:01, 13.83s/it, avr_loss=0.0295]
steps:   0%|          | 3/10016 [00:40<37:35:53, 13.52s/it, avr_loss=0.0295]
steps:   0%|          | 3/10016 [00:40<37:35:54, 13.52s/it, avr_loss=0.0301]
steps:   0%|          | 4/10016 [00:53<37:11:14, 13.37s/it, avr_loss=0.0301]
steps:   0%|          | 4/10016 [00:53<37:11:15, 13.37s/it, avr_loss=0.0278]
steps:   0%|          | 5/10016 [01:06<36:56:51, 13.29s/it, avr_loss=0.0278]
steps:   0%|          | 5/10016 [01:06<36:56:52, 13.29s/it, avr_loss=0.0284]
steps:   0%|          | 6/10016 [01:19<36:46:30, 13.23s/it, avr_loss=0.0284]
steps:   0%|          | 6/10016 [01:19<36:46:31, 13.23s/it, avr_loss=0.0279]
steps:   0%|          | 7/10016 [01:32<36:38:23, 13.18s/it, avr_loss=0.0279]
steps:   0%|          | 7/10016 [01:32<36:38:23, 13.18s/it, avr_loss=0.0272]
steps:   0%|          | 8/10016 [01:45<36:32:07, 13.14s/it, avr_loss=0.0272]
steps:   0%|          | 8/10016 [01:45<36:32:07, 13.14s/it, avr_loss=0.027] 
steps:   0%|          | 9/10016 [01:58<36:27:24, 13.12s/it, avr_loss=0.027]
steps:   0%|          | 9/10016 [01:58<36:27:24, 13.12s/it, avr_loss=0.0276]
steps:   0%|          | 10/10016 [02:10<36:23:26, 13.09s/it, avr_loss=0.0276]
steps:   0%|          | 10/10016 [02:10<36:23:27, 13.09s/it, avr_loss=0.0304]
steps:   0%|          | 11/10016 [02:23<36:20:12, 13.07s/it, avr_loss=0.0304]
steps:   0%|          | 11/10016 [02:23<36:20:12, 13.07s/it, avr_loss=0.0297]
steps:   0%|          | 12/10016 [02:36<36:17:26, 13.06s/it, avr_loss=0.0297]
steps:   0%|          | 12/10016 [02:36<36:17:26, 13.06s/it, avr_loss=0.0306]
steps:   0%|          | 13/10016 [02:49<36:14:55, 13.05s/it, avr_loss=0.0306]
steps:   0%|          | 13/10016 [02:49<36:14:55, 13.05s/it, avr_loss=0.0297]
steps:   0%|          | 14/10016 [03:02<36:12:43, 13.03s/it, avr_loss=0.0297]
steps:   0%|          | 14/10016 [03:02<36:12:43, 13.03s/it, avr_loss=0.0299]
steps:   0%|          | 15/10016 [03:15<36:10:51, 13.02s/it, avr_loss=0.0299]
steps:   0%|          | 15/10016 [03:15<36:10:52, 13.02s/it, avr_loss=0.0299]
steps:   0%|          | 16/10016 [03:28<36:09:14, 13.02s/it, avr_loss=0.0299]
steps:   0%|          | 16/10016 [03:28<36:09:14, 13.02s/it, avr_loss=0.0306]
steps:   0%|          | 17/10016 [03:41<36:07:45, 13.01s/it, avr_loss=0.0306]
steps:   0%|          | 17/10016 [03:41<36:07:45, 13.01s/it, avr_loss=0.0304]
steps:   0%|          | 18/10016 [03:54<36:06:23, 13.00s/it, avr_loss=0.0304]
steps:   0%|          | 18/10016 [03:54<36:06:23, 13.00s/it, avr_loss=0.0301]
steps:   0%|          | 19/10016 [04:06<36:05:03, 12.99s/it, avr_loss=0.0301]
steps:   0%|          | 19/10016 [04:06<36:05:03, 12.99s/it, avr_loss=0.0305]
steps:   0%|          | 20/10016 [04:19<36:03:54, 12.99s/it, avr_loss=0.0305]
steps:   0%|          | 20/10016 [04:19<36:03:54, 12.99s/it, avr_loss=0.03]  
steps:   0%|          | 21/10016 [04:32<36:02:57, 12.98s/it, avr_loss=0.03]
steps:   0%|          | 21/10016 [04:32<36:02:58, 12.98s/it, avr_loss=0.0329]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        [E708 23:53:34.279567494 socket.cpp:1019] [c10d] The client socket has timed out after 600000ms while trying to connect to (127.0.0.1, 0).
[E708 23:53:34.279684904 TCPStore.cpp:331] [c10d] TCP client failed to connect/validate to host 127.0.0.1:0 - timed out (try=1, timeout=600000ms): The client socket has timed out after 600000ms while trying to connect to (127.0.0.1, 0).
Exception raised from throwTimeoutError at /pytorch/torch/csrc/distributed/c10d/socket.cpp:1021 (most recent call first):
frame #0: c10::Error::Error(c10::SourceLocation, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >) + 0x98 (0x7f5dbb9785e8 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #1: <unknown function> + 0x5ba8afe (0x7f5da4836afe in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #2: <unknown function> + 0x1369113 (0x7f5d9fff7113 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #3: <unknown function> + 0x5bf5691 (0x7f5da4883691 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #4: <unknown function> + 0x5bf5849 (0x7f5da4883849 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #5: <unknown function> + 0x5bf5c01 (0x7f5da4883c01 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #6: <unknown function> + 0x5ba3deb (0x7f5da4831deb in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #7: c10d::TCPStore::TCPStore(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, c10d::TCPStoreOptions const&) + 0x4b5 (0x7f5da48346f5 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #8: <unknown function> + 0xc1e575 (0x7f5db3b76575 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #9: <unknown function> + 0xc52e64 (0x7f5db3baae64 in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #10: <unknown function> + 0x3895ce (0x7f5db32e15ce in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #11: <unknown function> + 0x13d0e6 (0x55e4504d50e6 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #12: _PyObject_MakeTpCall + 0x2d3 (0x55e4504ce0b3 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #13: <unknown function> + 0x1490b6 (0x55e4504e10b6 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #14: PyVectorcall_Call + 0xc9 (0x55e4504e1c59 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #15: <unknown function> + 0x146cc4 (0x55e4504decc4 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #16: <unknown function> + 0x1363bb (0x55e4504ce3bb in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #17: <unknown function> + 0x38815b (0x7f5db32e015b in /media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
frame #18: _PyObject_MakeTpCall + 0x2d3 (0x55e4504ce0b3 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #19: _PyEval_EvalFrameDefault + 0x5362 (0x55e4504ca332 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #20: _PyFunction_Vectorcall + 0x6c (0x55e4504d556c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #21: _PyEval_EvalFrameDefault + 0x30c (0x55e4504c52dc in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #22: <unknown function> + 0x1ae3e0 (0x55e4505463e0 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #23: <unknown function> + 0x13d733 (0x55e4504d5733 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #24: _PyEval_EvalFrameDefault + 0x30c (0x55e4504c52dc in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #25: _PyFunction_Vectorcall + 0x6c (0x55e4504d556c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #26: PyObject_Call + 0xbc (0x55e4504e18dc in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #27: _PyEval_EvalFrameDefault + 0x2c2a (0x55e4504c7bfa in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #28: _PyFunction_Vectorcall + 0x6c (0x55e4504d556c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #29: PyObject_Call + 0xbc (0x55e4504e18dc in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #30: _PyEval_EvalFrameDefault + 0x2c2a (0x55e4504c7bfa in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #31: _PyFunction_Vectorcall + 0x6c (0x55e4504d556c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #32: PyObject_Call + 0xbc (0x55e4504e18dc in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #33: _PyEval_EvalFrameDefault + 0x2c2a (0x55e4504c7bfa in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #34: _PyObject_FastCallDictTstate + 0xd0 (0x55e4504cd3d0 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #35: <unknown function> + 0x146799 (0x55e4504de799 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #36: <unknown function> + 0x1363bb (0x55e4504ce3bb in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #37: PyObject_Call + 0x20f (0x55e4504e1a2f in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #38: _PyEval_EvalFrameDefault + 0x2c2a (0x55e4504c7bfa in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #39: _PyFunction_Vectorcall + 0x6c (0x55e4504d556c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #40: _PyObject_FastCallDictTstate + 0x187 (0x55e4504cd487 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #41: <unknown function> + 0x146799 (0x55e4504de799 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #42: <unknown function> + 0x1363bb (0x55e4504ce3bb in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #43: PyObject_Call + 0x20f (0x55e4504e1a2f in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #44: _PyEval_EvalFrameDefault + 0x2c2a (0x55e4504c7bfa in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #45: _PyFunction_Vectorcall + 0x6c (0x55e4504d556c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #46: _PyObject_FastCallDictTstate + 0x187 (0x55e4504cd487 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #47: <unknown function> + 0x146799 (0x55e4504de799 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #48: _PyObject_MakeTpCall + 0x2eb (0x55e4504ce0cb in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #49: _PyEval_EvalFrameDefault + 0x5362 (0x55e4504ca332 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #50: _PyFunction_Vectorcall + 0x6c (0x55e4504d556c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #51: _PyEval_EvalFrameDefault + 0x30c (0x55e4504c52dc in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #52: _PyFunction_Vectorcall + 0x6c (0x55e4504d556c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #53: _PyEval_EvalFrameDefault + 0x700 (0x55e4504c56d0 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #54: <unknown function> + 0x1cfd2c (0x55e450567d2c in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #55: PyEval_EvalCode + 0x87 (0x55e450567c77 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #56: <unknown function> + 0x2001da (0x55e4505981da in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #57: <unknown function> + 0x1fb663 (0x55e450593663 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #58: <unknown function> + 0x975bf (0x55e45042f5bf in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #59: _PyRun_SimpleFileObject + 0x1bd (0x55e45058de9d in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #60: _PyRun_AnyFileObject + 0x44 (0x55e45058da34 in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #61: Py_RunMain + 0x31b (0x55e45058ad9b in /media/miniconda3/envs/musubi_copy/bin/python3.10)
frame #62: Py_BytesMain + 0x37 (0x55e45055b897 in /media/miniconda3/envs/musubi_copy/bin/python3.10)

Traceback (most recent call last):
  File "/media/jiayueru/yuwanhe/musubi-tuner/fpack_train_network.py", line 454, in <module>
    trainer.train(args)
  File "/media/jiayueru/yuwanhe/musubi-tuner/hv_train_network.py", line 1432, in train
    accelerator = prepare_accelerator(args)
  File "/media/jiayueru/yuwanhe/musubi-tuner/hv_train_network.py", line 171, in prepare_accelerator
    accelerator = Accelerator(
  File "/media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/accelerate/accelerator.py", line 461, in __init__
    self.state = AcceleratorState(
  File "/media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/accelerate/state.py", line 901, in __init__
    PartialState(cpu, **kwargs)
  File "/media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/accelerate/state.py", line 227, in __init__
    torch.distributed.init_process_group(backend=self.backend, **kwargs)
  File "/media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/distributed/c10d_logger.py", line 81, in wrapper
    return func(*args, **kwargs)
  File "/media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/distributed/c10d_logger.py", line 95, in wrapper
    func_return = func(*args, **kwargs)
  File "/media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py", line 1710, in init_process_group
    store, rank, world_size = next(rendezvous_iterator)
  File "/media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/distributed/rendezvous.py", line 278, in _env_rendezvous_handler
    store = _create_c10d_store(
  File "/media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/distributed/rendezvous.py", line 189, in _create_c10d_store
    return TCPStore(
torch.distributed.DistNetworkError: The client socket has timed out after 600000ms while trying to connect to (127.0.0.1, 0).
W0708 23:53:35.003000 459295 site-packages/torch/distributed/elastic/multiprocessing/api.py:900] Sending process 459467 closing signal SIGTERM
W0708 23:53:35.006000 459295 site-packages/torch/distributed/elastic/multiprocessing/api.py:900] Sending process 459468 closing signal SIGTERM
W0708 23:53:35.007000 459295 site-packages/torch/distributed/elastic/multiprocessing/api.py:900] Sending process 459469 closing signal SIGTERM
E0708 23:53:35.121000 459295 site-packages/torch/distributed/elastic/multiprocessing/api.py:874] failed (exitcode: 1) local_rank: 0 (pid: 459466) of binary: /media/miniconda3/envs/musubi_copy/bin/python3.10
Traceback (most recent call last):
  File "/media/miniconda3/envs/musubi_copy/bin/accelerate", line 8, in <module>
    sys.exit(main())
  File "/media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/accelerate/commands/accelerate_cli.py", line 50, in main
    args.func(args)
  File "/media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/accelerate/commands/launch.py", line 1204, in launch_command
    multi_gpu_launcher(args)
  File "/media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/accelerate/commands/launch.py", line 825, in multi_gpu_launcher
    distrib_run.run(args)
  File "/media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/distributed/run.py", line 883, in run
    elastic_launch(
  File "/media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/distributed/launcher/api.py", line 139, in __call__
    return launch_agent(self._config, self._entrypoint, list(args))
  File "/media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/distributed/launcher/api.py", line 270, in launch_agent
    raise ChildFailedError(
torch.distributed.elastic.multiprocessing.errors.ChildFailedError: 
============================================================
fpack_train_network.py FAILED
------------------------------------------------------------
Failures:
  <NO_OTHER_FAILURES>
------------------------------------------------------------
Root Cause (first observed failure):
[0]:
  time      : 2025-07-08_23:53:35
  host      : aibox-rf1c6e68e-5fd6c6b499-hj2lh
  rank      : 0 (local_rank: 0)
  exitcode  : 1 (pid: 459466)
  error_file: <N/A>
  traceback : To enable traceback see: https://pytorch.org/docs/stable/elastic/errors.html
============================================================
