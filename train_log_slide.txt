nohup: ignoring input
The following values were not passed to `accelerate launch` and had defaults used instead:
	`--num_machines` was set to a value of `1`
	`--dynamo_backend` was set to a value of `'no'`
To avoid this warning pass in values for each of the problematic parameters or run `accelerate config`.
Xformers is not installed!
Flash Attn is not installed!
Xformers is not installed!
Xformers is not installed!
Sage Attn is not installed!
Flash Attn is not installed!
Flash Attn is not installed!
Sage Attn is not installed!
Sage Attn is not installed!
Xformers is not installed!
Flash Attn is not installed!
Sage Attn is not installed!
Trying to import sageattention
Failed to import sageattention
Trying to import sageattention
Trying to import sageattention
Failed to import sageattention
Failed to import sageattention
Trying to import sageattention
Failed to import sageattention
INFO:hv_train_network:Load dataset config from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_config.toml
INFO:hv_train_network:Load dataset config from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_config.toml
INFO:hv_train_network:Load dataset config from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_config.toml
INFO:hv_train_network:Load dataset config from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_config.toml
WARNING:dataset.image_video_dataset:target_frames are rounded to [41, 69]
INFO:dataset.image_video_dataset:load video jsonl from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl
WARNING:dataset.image_video_dataset:target_frames are rounded to [41, 69]
WARNING:dataset.image_video_dataset:target_frames are rounded to [41, 69]
INFO:dataset.image_video_dataset:load video jsonl from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl
INFO:dataset.image_video_dataset:load video jsonl from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl
WARNING:dataset.image_video_dataset:target_frames are rounded to [41, 69]
INFO:dataset.image_video_dataset:loaded 100 videos
INFO:dataset.image_video_dataset:loaded 100 videos
INFO:dataset.image_video_dataset:load video jsonl from /media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl
INFO:dataset.config_utils:[Dataset 0]
  is_image_dataset: False
  resolution: (768, 512)
  batch_size: 1
  num_repeats: 1
  caption_extension: "None"
  enable_bucket: True
  bucket_no_upscale: False
  cache_directory: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_cache"
  debug_dataset: False
    video_directory: "None"
    video_jsonl_file: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl"
    control_directory: "None"
    target_frames: (41, 69)
    frame_extraction: slide
    frame_stride: 30
    frame_sample: 1
    max_frames: 129
    source_fps: 30.0


INFO:dataset.config_utils:[Dataset 0]
  is_image_dataset: False
  resolution: (768, 512)
  batch_size: 1
  num_repeats: 1
  caption_extension: "None"
  enable_bucket: True
  bucket_no_upscale: False
  cache_directory: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_cache"
  debug_dataset: False
    video_directory: "None"
    video_jsonl_file: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl"
    control_directory: "None"
    target_frames: (41, 69)
    frame_extraction: slide
    frame_stride: 30
    frame_sample: 1
    max_frames: 129
    source_fps: 30.0


INFO:dataset.image_video_dataset:loaded 100 videos
INFO:dataset.config_utils:[Dataset 0]
  is_image_dataset: False
  resolution: (768, 512)
  batch_size: 1
  num_repeats: 1
  caption_extension: "None"
  enable_bucket: True
  bucket_no_upscale: False
  cache_directory: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_cache"
  debug_dataset: False
    video_directory: "None"
    video_jsonl_file: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl"
    control_directory: "None"
    target_frames: (41, 69)
    frame_extraction: slide
    frame_stride: 30
    frame_sample: 1
    max_frames: 129
    source_fps: 30.0


INFO:dataset.image_video_dataset:loaded 100 videos
INFO:dataset.config_utils:[Dataset 0]
  is_image_dataset: False
  resolution: (768, 512)
  batch_size: 1
  num_repeats: 1
  caption_extension: "None"
  enable_bucket: True
  bucket_no_upscale: False
  cache_directory: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_cache"
  debug_dataset: False
    video_directory: "None"
    video_jsonl_file: "/media/jiayueru/yuwanhe/musubi-tuner/data/robotwin/block_handover_D435_metadata.jsonl"
    control_directory: "None"
    target_frames: (41, 69)
    frame_extraction: slide
    frame_stride: 30
    frame_sample: 1
    max_frames: 129
    source_fps: 30.0


INFO:dataset.image_video_dataset:bucket: (768, 512, 41), count: 1300
INFO:dataset.image_video_dataset:bucket: (768, 512, 41), count: 1300
INFO:dataset.image_video_dataset:bucket: (768, 512, 41), count: 1300
INFO:dataset.image_video_dataset:bucket: (768, 512, 41), count: 1300
INFO:dataset.image_video_dataset:bucket: (768, 512, 69), count: 1204
INFO:dataset.image_video_dataset:bucket: (768, 512, 69), count: 1204
INFO:dataset.image_video_dataset:bucket: (768, 512, 69), count: 1204
INFO:dataset.image_video_dataset:bucket: (768, 512, 69), count: 1204
INFO:dataset.image_video_dataset:total batches: 2504
INFO:dataset.image_video_dataset:total batches: 2504
INFO:dataset.image_video_dataset:total batches: 2504
INFO:dataset.image_video_dataset:total batches: 2504
INFO:hv_train_network:preparing accelerator
INFO:hv_train_network:preparing accelerator
INFO:hv_train_network:preparing accelerator
INFO:hv_train_network:preparing accelerator
accelerator device: cuda:0
INFO:hv_train_network:DiT precision: torch.bfloat16, weight precision: torch.bfloat16
INFO:hv_train_network:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:__main__:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:frame_pack.hunyuan_video_packed:Creating HunyuanVideoTransformer3DModelPacked
INFO:frame_pack.hunyuan_video_packed:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, device=cuda:0
accelerator device: cuda:2
INFO:hv_train_network:DiT precision: torch.bfloat16, weight precision: torch.bfloat16
INFO:hv_train_network:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:__main__:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:frame_pack.hunyuan_video_packed:Creating HunyuanVideoTransformer3DModelPacked
accelerator device: cuda:3
INFO:hv_train_network:DiT precision: torch.bfloat16, weight precision: torch.bfloat16
INFO:hv_train_network:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:__main__:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:frame_pack.hunyuan_video_packed:Creating HunyuanVideoTransformer3DModelPacked
accelerator device: cuda:1
INFO:hv_train_network:DiT precision: torch.bfloat16, weight precision: torch.bfloat16
INFO:hv_train_network:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:__main__:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:frame_pack.hunyuan_video_packed:Creating HunyuanVideoTransformer3DModelPacked
INFO:frame_pack.hunyuan_video_packed:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, device=cuda:2
INFO:frame_pack.hunyuan_video_packed:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, device=cuda:3
INFO:frame_pack.hunyuan_video_packed:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, device=cuda:1
INFO:frame_pack.hunyuan_video_packed:Loaded DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, info=<All keys matched successfully>
INFO:networks.lora:create LoRA network. base dim (rank): 32, alpha: 1
INFO:networks.lora:neuron dropout: p=None, rank dropout: p=None, module dropout: p=None
INFO:frame_pack.hunyuan_video_packed:Loaded DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, info=<All keys matched successfully>
INFO:networks.lora:create LoRA network. base dim (rank): 32, alpha: 1
INFO:networks.lora:neuron dropout: p=None, rank dropout: p=None, module dropout: p=None
INFO:frame_pack.hunyuan_video_packed:Loaded DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, info=<All keys matched successfully>
INFO:networks.lora:create LoRA network. base dim (rank): 32, alpha: 1
INFO:networks.lora:neuron dropout: p=None, rank dropout: p=None, module dropout: p=None
INFO:frame_pack.hunyuan_video_packed:Loaded DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, info=<All keys matched successfully>
import network module: networks.lora_framepack
INFO:networks.lora:create LoRA network. base dim (rank): 32, alpha: 1
INFO:networks.lora:neuron dropout: p=None, rank dropout: p=None, module dropout: p=None
INFO:networks.lora:create LoRA for U-Net/DiT: 440 modules.
INFO:networks.lora:enable LoRA for U-Net: 440 modules
Gradient checkpointing enabled for HunyuanVideoTransformer3DModelPacked.
INFO:hv_train_network:use 8-bit AdamW optimizer | {}
INFO:networks.lora:create LoRA for U-Net/DiT: 440 modules.
INFO:networks.lora:enable LoRA for U-Net: 440 modules
Gradient checkpointing enabled for HunyuanVideoTransformer3DModelPacked.
INFO:hv_train_network:use 8-bit AdamW optimizer | {}
INFO:networks.lora:create LoRA for U-Net/DiT: 440 modules.
INFO:networks.lora:enable LoRA for U-Net: 440 modules
Gradient checkpointing enabled for HunyuanVideoTransformer3DModelPacked.
INFO:hv_train_network:use 8-bit AdamW optimizer | {}
INFO:networks.lora:create LoRA for U-Net/DiT: 440 modules.
INFO:networks.lora:enable LoRA for U-Net: 440 modules
Gradient checkpointing enabled for HunyuanVideoTransformer3DModelPacked.
prepare optimizer, data loader etc.
INFO:hv_train_network:use 8-bit AdamW optimizer | {}
override steps. steps for 16 epochs is / 指定エポックまでのステップ数: 10016
INFO:hv_train_network:set DiT model name for metadata: /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
running training / 学習開始
  num train items / 学習画像、動画数: 2504
INFO:hv_train_network:set DiT model name for metadata: /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
  num batches per epoch / 1epochのバッチ数: 626
  num epochs / epoch数: 16
  batch size per device / バッチサイズ: 1
  gradient accumulation steps / 勾配を合計するステップ数 = 1
  total optimization steps / 学習ステップ数: 10016
INFO:hv_train_network:set DiT model name for metadata: /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:hv_train_network:set DiT model name for metadata: /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:hv_train_network:set VAE model name for metadata: /media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/vae/diffusion_pytorch_model.safetensors
INFO:hv_train_network:set VAE model name for metadata: /media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/vae/diffusion_pytorch_model.safetensors
INFO:hv_train_network:set VAE model name for metadata: /media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/vae/diffusion_pytorch_model.safetensors
INFO:hv_train_network:set VAE model name for metadata: /media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/vae/diffusion_pytorch_model.safetensors
INFO:hv_train_network:DiT dtype: torch.bfloat16, device: cuda:1
INFO:hv_train_network:DiT dtype: torch.bfloat16, device: cuda:2
INFO:hv_train_network:DiT dtype: torch.bfloat16, device: cuda:3

steps:   0%|          | 0/10016 [00:00<?, ?it/s]INFO:hv_train_network:DiT dtype: torch.bfloat16, device: cuda:0

epoch 1/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1

steps:   0%|          | 1/10016 [00:13<38:27:48, 13.83s/it]
steps:   0%|          | 1/10016 [00:13<38:27:53, 13.83s/it, avr_loss=0.0273]
steps:   0%|          | 2/10016 [00:26<37:03:36, 13.32s/it, avr_loss=0.0273]
steps:   0%|          | 2/10016 [00:26<37:03:37, 13.32s/it, avr_loss=0.0295]
steps:   0%|          | 3/10016 [00:39<36:40:44, 13.19s/it, avr_loss=0.0295]
steps:   0%|          | 3/10016 [00:39<36:40:46, 13.19s/it, avr_loss=0.0301]
steps:   0%|          | 4/10016 [00:52<36:30:45, 13.13s/it, avr_loss=0.0301]
steps:   0%|          | 4/10016 [00:52<36:30:46, 13.13s/it, avr_loss=0.0278]
steps:   0%|          | 5/10016 [01:05<36:23:59, 13.09s/it, avr_loss=0.0278]
steps:   0%|          | 5/10016 [01:05<36:23:59, 13.09s/it, avr_loss=0.0284]
steps:   0%|          | 6/10016 [01:18<36:18:41, 13.06s/it, avr_loss=0.0284]
steps:   0%|          | 6/10016 [01:18<36:18:41, 13.06s/it, avr_loss=0.0279]
steps:   0%|          | 7/10016 [01:31<36:14:25, 13.03s/it, avr_loss=0.0279]
steps:   0%|          | 7/10016 [01:31<36:14:25, 13.03s/it, avr_loss=0.0272]
steps:   0%|          | 8/10016 [01:44<36:11:00, 13.02s/it, avr_loss=0.0272]
steps:   0%|          | 8/10016 [01:44<36:11:01, 13.02s/it, avr_loss=0.027] 
steps:   0%|          | 9/10016 [01:57<36:08:29, 13.00s/it, avr_loss=0.027]
steps:   0%|          | 9/10016 [01:57<36:08:29, 13.00s/it, avr_loss=0.0276]
steps:   0%|          | 10/10016 [02:09<36:06:42, 12.99s/it, avr_loss=0.0276]
steps:   0%|          | 10/10016 [02:09<36:06:42, 12.99s/it, avr_loss=0.0304]
steps:   0%|          | 11/10016 [02:22<36:05:00, 12.98s/it, avr_loss=0.0304]
steps:   0%|          | 11/10016 [02:22<36:05:00, 12.98s/it, avr_loss=0.0297]
steps:   0%|          | 12/10016 [02:35<36:03:31, 12.98s/it, avr_loss=0.0297]
steps:   0%|          | 12/10016 [02:35<36:03:32, 12.98s/it, avr_loss=0.0306]
steps:   0%|          | 13/10016 [02:48<36:02:19, 12.97s/it, avr_loss=0.0306]
steps:   0%|          | 13/10016 [02:48<36:02:19, 12.97s/it, avr_loss=0.0297]
steps:   0%|          | 14/10016 [03:01<36:01:12, 12.96s/it, avr_loss=0.0297]
steps:   0%|          | 14/10016 [03:01<36:01:12, 12.96s/it, avr_loss=0.0299]
steps:   0%|          | 15/10016 [03:14<36:00:16, 12.96s/it, avr_loss=0.0299]
steps:   0%|          | 15/10016 [03:14<36:00:16, 12.96s/it, avr_loss=0.0299]
steps:   0%|          | 16/10016 [03:27<35:59:27, 12.96s/it, avr_loss=0.0299]
steps:   0%|          | 16/10016 [03:27<35:59:27, 12.96s/it, avr_loss=0.0306]
steps:   0%|          | 17/10016 [03:40<35:58:33, 12.95s/it, avr_loss=0.0306]
steps:   0%|          | 17/10016 [03:40<35:58:33, 12.95s/it, avr_loss=0.0304]
steps:   0%|          | 18/10016 [03:53<35:57:41, 12.95s/it, avr_loss=0.0304]
steps:   0%|          | 18/10016 [03:53<35:57:41, 12.95s/it, avr_loss=0.0301]
steps:   0%|          | 19/10016 [04:05<35:56:50, 12.94s/it, avr_loss=0.0301]
steps:   0%|          | 19/10016 [04:05<35:56:50, 12.94s/it, avr_loss=0.0305]
steps:   0%|          | 20/10016 [04:18<35:56:08, 12.94s/it, avr_loss=0.0305]
steps:   0%|          | 20/10016 [04:18<35:56:08, 12.94s/it, avr_loss=0.03]  
steps:   0%|          | 21/10016 [04:31<35:55:30, 12.94s/it, avr_loss=0.03]
steps:   0%|          | 21/10016 [04:31<35:55:30, 12.94s/it, avr_loss=0.0329]
steps:   0%|          | 22/10016 [04:44<35:54:56, 12.94s/it, avr_loss=0.0329]
steps:   0%|          | 22/10016 [04:44<35:54:56, 12.94s/it, avr_loss=0.0326]
steps:   0%|          | 23/10016 [04:57<35:54:21, 12.94s/it, avr_loss=0.0326]
steps:   0%|          | 23/10016 [04:57<35:54:21, 12.94s/it, avr_loss=0.0333]
steps:   0%|          | 24/10016 [05:10<35:53:51, 12.93s/it, avr_loss=0.0333]
steps:   0%|          | 24/10016 [05:10<35:53:51, 12.93s/it, avr_loss=0.0332]
steps:   0%|          | 25/10016 [05:23<35:53:25, 12.93s/it, avr_loss=0.0332]
steps:   0%|          | 25/10016 [05:23<35:53:25, 12.93s/it, avr_loss=0.0329]
steps:   0%|          | 26/10016 [05:36<35:52:57, 12.93s/it, avr_loss=0.0329]
steps:   0%|          | 26/10016 [05:36<35:52:57, 12.93s/it, avr_loss=0.0326]
steps:   0%|          | 27/10016 [05:49<35:52:35, 12.93s/it, avr_loss=0.0326]
steps:   0%|          | 27/10016 [05:49<35:52:35, 12.93s/it, avr_loss=0.0338]
steps:   0%|          | 28/10016 [06:02<35:52:11, 12.93s/it, avr_loss=0.0338]
steps:   0%|          | 28/10016 [06:02<35:52:11, 12.93s/it, avr_loss=0.0335]
steps:   0%|          | 29/10016 [06:14<35:51:40, 12.93s/it, avr_loss=0.0335]
steps:   0%|          | 29/10016 [06:14<35:51:40, 12.93s/it, avr_loss=0.0335]
steps:   0%|          | 30/10016 [06:27<35:51:11, 12.93s/it, avr_loss=0.0335]
steps:   0%|          | 30/10016 [06:27<35:51:11, 12.93s/it, avr_loss=0.0335]
steps:   0%|          | 31/10016 [06:40<35:50:44, 12.92s/it, avr_loss=0.0335]
steps:   0%|          | 31/10016 [06:40<35:50:44, 12.92s/it, avr_loss=0.0333]
steps:   0%|          | 32/10016 [06:53<35:50:20, 12.92s/it, avr_loss=0.0333]
steps:   0%|          | 32/10016 [06:53<35:50:20, 12.92s/it, avr_loss=0.0332]
steps:   0%|          | 33/10016 [07:06<35:49:58, 12.92s/it, avr_loss=0.0332]
steps:   0%|          | 33/10016 [07:06<35:49:58, 12.92s/it, avr_loss=0.033] 
steps:   0%|          | 34/10016 [07:19<35:49:33, 12.92s/it, avr_loss=0.033]
steps:   0%|          | 34/10016 [07:19<35:49:33, 12.92s/it, avr_loss=0.033]
steps:   0%|          | 35/10016 [07:32<35:49:11, 12.92s/it, avr_loss=0.033]
steps:   0%|          | 35/10016 [07:32<35:49:11, 12.92s/it, avr_loss=0.0328]
steps:   0%|          | 36/10016 [07:45<35:48:48, 12.92s/it, avr_loss=0.0328]
steps:   0%|          | 36/10016 [07:45<35:48:49, 12.92s/it, avr_loss=0.0324]
steps:   0%|          | 37/10016 [07:57<35:48:28, 12.92s/it, avr_loss=0.0324]
steps:   0%|          | 37/10016 [07:57<35:48:29, 12.92s/it, avr_loss=0.0323]
steps:   0%|          | 38/10016 [08:10<35:48:09, 12.92s/it, avr_loss=0.0323]
steps:   0%|          | 38/10016 [08:10<35:48:09, 12.92s/it, avr_loss=0.032] 
steps:   0%|          | 39/10016 [08:23<35:47:50, 12.92s/it, avr_loss=0.032]
steps:   0%|          | 39/10016 [08:23<35:47:50, 12.92s/it, avr_loss=0.0318]
steps:   0%|          | 40/10016 [08:36<35:47:29, 12.92s/it, avr_loss=0.0318]
steps:   0%|          | 40/10016 [08:36<35:47:29, 12.92s/it, avr_loss=0.0316]
steps:   0%|          | 41/10016 [08:49<35:47:11, 12.92s/it, avr_loss=0.0316]
steps:   0%|          | 41/10016 [08:49<35:47:11, 12.92s/it, avr_loss=0.0314]
steps:   0%|          | 42/10016 [09:02<35:46:51, 12.91s/it, avr_loss=0.0314]
steps:   0%|          | 42/10016 [09:02<35:46:51, 12.91s/it, avr_loss=0.0312]
steps:   0%|          | 43/10016 [09:15<35:46:32, 12.91s/it, avr_loss=0.0312]
steps:   0%|          | 43/10016 [09:15<35:46:32, 12.91s/it, avr_loss=0.031] 
steps:   0%|          | 44/10016 [09:28<35:46:12, 12.91s/it, avr_loss=0.031]
steps:   0%|          | 44/10016 [09:28<35:46:12, 12.91s/it, avr_loss=0.0308]
steps:   0%|          | 45/10016 [09:41<35:45:53, 12.91s/it, avr_loss=0.0308]
steps:   0%|          | 45/10016 [09:41<35:45:53, 12.91s/it, avr_loss=0.0305]
steps:   0%|          | 46/10016 [09:53<35:45:33, 12.91s/it, avr_loss=0.0305]
steps:   0%|          | 46/10016 [09:53<35:45:34, 12.91s/it, avr_loss=0.0319]
steps:   0%|          | 47/10016 [10:06<35:45:14, 12.91s/it, avr_loss=0.0319]
steps:   0%|          | 47/10016 [10:06<35:45:15, 12.91s/it, avr_loss=0.0322]
steps:   0%|          | 48/10016 [10:19<35:44:54, 12.91s/it, avr_loss=0.0322]
steps:   0%|          | 48/10016 [10:19<35:44:54, 12.91s/it, avr_loss=0.032] 
steps:   0%|          | 49/10016 [10:32<35:44:35, 12.91s/it, avr_loss=0.032]
steps:   0%|          | 49/10016 [10:32<35:44:35, 12.91s/it, avr_loss=0.0319]
steps:   0%|          | 50/10016 [10:45<35:44:17, 12.91s/it, avr_loss=0.0319]
steps:   0%|          | 50/10016 [10:45<35:44:17, 12.91s/it, avr_loss=0.0317]
steps:   1%|          | 51/10016 [10:58<35:44:00, 12.91s/it, avr_loss=0.0317]
steps:   1%|          | 51/10016 [10:58<35:44:00, 12.91s/it, avr_loss=0.0315]
steps:   1%|          | 52/10016 [11:11<35:43:42, 12.91s/it, avr_loss=0.0315]
steps:   1%|          | 52/10016 [11:11<35:43:42, 12.91s/it, avr_loss=0.0314]
steps:   1%|          | 53/10016 [11:24<35:43:24, 12.91s/it, avr_loss=0.0314]
steps:   1%|          | 53/10016 [11:24<35:43:24, 12.91s/it, avr_loss=0.0313]
steps:   1%|          | 54/10016 [11:37<35:43:06, 12.91s/it, avr_loss=0.0313]
steps:   1%|          | 54/10016 [11:37<35:43:06, 12.91s/it, avr_loss=0.031] 
steps:   1%|          | 55/10016 [11:49<35:42:49, 12.91s/it, avr_loss=0.031]
steps:   1%|          | 55/10016 [11:49<35:42:49, 12.91s/it, avr_loss=0.031]
steps:   1%|          | 56/10016 [12:02<35:42:32, 12.91s/it, avr_loss=0.031]
steps:   1%|          | 56/10016 [12:02<35:42:32, 12.91s/it, avr_loss=0.0309]
steps:   1%|          | 57/10016 [12:15<35:42:14, 12.91s/it, avr_loss=0.0309]
steps:   1%|          | 57/10016 [12:15<35:42:14, 12.91s/it, avr_loss=0.0307]
steps:   1%|          | 58/10016 [12:28<35:41:56, 12.91s/it, avr_loss=0.0307]
steps:   1%|          | 58/10016 [12:28<35:41:56, 12.91s/it, avr_loss=0.0307]
steps:   1%|          | 59/10016 [12:41<35:41:38, 12.91s/it, avr_loss=0.0307]
steps:   1%|          | 59/10016 [12:41<35:41:38, 12.91s/it, avr_loss=0.0307]
steps:   1%|          | 60/10016 [12:54<35:41:20, 12.90s/it, avr_loss=0.0307]
steps:   1%|          | 60/10016 [12:54<35:41:21, 12.90s/it, avr_loss=0.0307]
steps:   1%|          | 61/10016 [13:07<35:41:04, 12.90s/it, avr_loss=0.0307]
steps:   1%|          | 61/10016 [13:07<35:41:04, 12.90s/it, avr_loss=0.0305]
steps:   1%|          | 62/10016 [13:20<35:40:50, 12.90s/it, avr_loss=0.0305]
steps:   1%|          | 62/10016 [13:20<35:40:50, 12.90s/it, avr_loss=0.0305]
steps:   1%|          | 63/10016 [13:32<35:40:34, 12.90s/it, avr_loss=0.0305]
steps:   1%|          | 63/10016 [13:32<35:40:34, 12.90s/it, avr_loss=0.0305]
steps:   1%|          | 64/10016 [13:45<35:40:19, 12.90s/it, avr_loss=0.0305]
steps:   1%|          | 64/10016 [13:45<35:40:19, 12.90s/it, avr_loss=0.0305]
steps:   1%|          | 65/10016 [13:58<35:40:06, 12.90s/it, avr_loss=0.0305]
steps:   1%|          | 65/10016 [13:58<35:40:06, 12.90s/it, avr_loss=0.0304]
steps:   1%|          | 66/10016 [14:11<35:39:52, 12.90s/it, avr_loss=0.0304]
steps:   1%|          | 66/10016 [14:11<35:39:53, 12.90s/it, avr_loss=0.0303]
steps:   1%|          | 67/10016 [14:24<35:39:36, 12.90s/it, avr_loss=0.0303]
steps:   1%|          | 67/10016 [14:24<35:39:36, 12.90s/it, avr_loss=0.0301]
steps:   1%|          | 68/10016 [14:37<35:39:20, 12.90s/it, avr_loss=0.0301]
steps:   1%|          | 68/10016 [14:37<35:39:20, 12.90s/it, avr_loss=0.03]  
steps:   1%|          | 69/10016 [14:50<35:39:04, 12.90s/it, avr_loss=0.03]
steps:   1%|          | 69/10016 [14:50<35:39:04, 12.90s/it, avr_loss=0.0299]
steps:   1%|          | 70/10016 [15:03<35:38:50, 12.90s/it, avr_loss=0.0299]
steps:   1%|          | 70/10016 [15:03<35:38:50, 12.90s/it, avr_loss=0.0297]
steps:   1%|          | 71/10016 [15:16<35:38:36, 12.90s/it, avr_loss=0.0297]
steps:   1%|          | 71/10016 [15:16<35:38:36, 12.90s/it, avr_loss=0.0297]
steps:   1%|          | 72/10016 [15:28<35:38:21, 12.90s/it, avr_loss=0.0297]
steps:   1%|          | 72/10016 [15:28<35:38:21, 12.90s/it, avr_loss=0.0296]
steps:   1%|          | 73/10016 [15:41<35:38:07, 12.90s/it, avr_loss=0.0296]
steps:   1%|          | 73/10016 [15:41<35:38:07, 12.90s/it, avr_loss=0.0296]
steps:   1%|          | 74/10016 [15:54<35:37:52, 12.90s/it, avr_loss=0.0296]
steps:   1%|          | 74/10016 [15:54<35:37:52, 12.90s/it, avr_loss=0.0295]
steps:   1%|          | 75/10016 [16:07<35:37:36, 12.90s/it, avr_loss=0.0295]
steps:   1%|          | 75/10016 [16:07<35:37:36, 12.90s/it, avr_loss=0.0294]
steps:   1%|          | 76/10016 [16:20<35:37:20, 12.90s/it, avr_loss=0.0294]
steps:   1%|          | 76/10016 [16:20<35:37:21, 12.90s/it, avr_loss=0.0293]
steps:   1%|          | 77/10016 [16:33<35:37:05, 12.90s/it, avr_loss=0.0293]
steps:   1%|          | 77/10016 [16:33<35:37:05, 12.90s/it, avr_loss=0.0292]
steps:   1%|          | 78/10016 [16:46<35:36:50, 12.90s/it, avr_loss=0.0292]
steps:   1%|          | 78/10016 [16:46<35:36:50, 12.90s/it, avr_loss=0.0295]
steps:   1%|          | 79/10016 [16:59<35:36:36, 12.90s/it, avr_loss=0.0295]
steps:   1%|          | 79/10016 [16:59<35:36:36, 12.90s/it, avr_loss=0.0294]
steps:   1%|          | 80/10016 [17:12<35:36:22, 12.90s/it, avr_loss=0.0294]
steps:   1%|          | 80/10016 [17:12<35:36:22, 12.90s/it, avr_loss=0.0306]
steps:   1%|          | 81/10016 [17:24<35:36:08, 12.90s/it, avr_loss=0.0306]
steps:   1%|          | 81/10016 [17:24<35:36:08, 12.90s/it, avr_loss=0.0305]
steps:   1%|          | 82/10016 [17:37<35:35:54, 12.90s/it, avr_loss=0.0305]
steps:   1%|          | 82/10016 [17:37<35:35:54, 12.90s/it, avr_loss=0.0306]
steps:   1%|          | 83/10016 [17:50<35:35:39, 12.90s/it, avr_loss=0.0306]
steps:   1%|          | 83/10016 [17:50<35:35:39, 12.90s/it, avr_loss=0.0305]
steps:   1%|          | 84/10016 [18:03<35:35:23, 12.90s/it, avr_loss=0.0305]
steps:   1%|          | 84/10016 [18:03<35:35:23, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 85/10016 [18:16<35:35:10, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 85/10016 [18:16<35:35:10, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 86/10016 [18:29<35:34:56, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 86/10016 [18:29<35:34:56, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 87/10016 [18:42<35:34:42, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 87/10016 [18:42<35:34:42, 12.90s/it, avr_loss=0.0316]
steps:   1%|          | 88/10016 [18:55<35:34:26, 12.90s/it, avr_loss=0.0316]
steps:   1%|          | 88/10016 [18:55<35:34:26, 12.90s/it, avr_loss=0.0315]
steps:   1%|          | 89/10016 [19:08<35:34:11, 12.90s/it, avr_loss=0.0315]
steps:   1%|          | 89/10016 [19:08<35:34:11, 12.90s/it, avr_loss=0.0314]
steps:   1%|          | 90/10016 [19:20<35:33:57, 12.90s/it, avr_loss=0.0314]
steps:   1%|          | 90/10016 [19:20<35:33:57, 12.90s/it, avr_loss=0.0316]
steps:   1%|          | 91/10016 [19:33<35:33:42, 12.90s/it, avr_loss=0.0316]
steps:   1%|          | 91/10016 [19:33<35:33:42, 12.90s/it, avr_loss=0.0315]
steps:   1%|          | 92/10016 [19:46<35:33:27, 12.90s/it, avr_loss=0.0315]
steps:   1%|          | 92/10016 [19:46<35:33:27, 12.90s/it, avr_loss=0.0314]
steps:   1%|          | 93/10016 [19:59<35:33:13, 12.90s/it, avr_loss=0.0314]
steps:   1%|          | 93/10016 [19:59<35:33:14, 12.90s/it, avr_loss=0.0315]
steps:   1%|          | 94/10016 [20:12<35:32:59, 12.90s/it, avr_loss=0.0315]
steps:   1%|          | 94/10016 [20:12<35:32:59, 12.90s/it, avr_loss=0.0322]
steps:   1%|          | 95/10016 [20:25<35:32:45, 12.90s/it, avr_loss=0.0322]
steps:   1%|          | 95/10016 [20:25<35:32:45, 12.90s/it, avr_loss=0.0323]
steps:   1%|          | 96/10016 [20:38<35:32:32, 12.90s/it, avr_loss=0.0323]
steps:   1%|          | 96/10016 [20:38<35:32:32, 12.90s/it, avr_loss=0.0323]
steps:   1%|          | 97/10016 [20:51<35:32:19, 12.90s/it, avr_loss=0.0323]
steps:   1%|          | 97/10016 [20:51<35:32:19, 12.90s/it, avr_loss=0.0322]
steps:   1%|          | 98/10016 [21:04<35:32:06, 12.90s/it, avr_loss=0.0322]
steps:   1%|          | 98/10016 [21:04<35:32:06, 12.90s/it, avr_loss=0.0321]
steps:   1%|          | 99/10016 [21:16<35:31:52, 12.90s/it, avr_loss=0.0321]
steps:   1%|          | 99/10016 [21:16<35:31:52, 12.90s/it, avr_loss=0.032] 
steps:   1%|          | 100/10016 [21:29<35:31:39, 12.90s/it, avr_loss=0.032]
steps:   1%|          | 100/10016 [21:29<35:31:39, 12.90s/it, avr_loss=0.0319]
steps:   1%|          | 101/10016 [21:42<35:31:25, 12.90s/it, avr_loss=0.0319]
steps:   1%|          | 101/10016 [21:42<35:31:25, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 102/10016 [21:55<35:31:12, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 102/10016 [21:55<35:31:12, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 103/10016 [22:08<35:30:58, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 103/10016 [22:08<35:30:58, 12.90s/it, avr_loss=0.0319]
steps:   1%|          | 104/10016 [22:21<35:30:44, 12.90s/it, avr_loss=0.0319]
steps:   1%|          | 104/10016 [22:21<35:30:44, 12.90s/it, avr_loss=0.0319]
steps:   1%|          | 105/10016 [22:34<35:30:30, 12.90s/it, avr_loss=0.0319]
steps:   1%|          | 105/10016 [22:34<35:30:30, 12.90s/it, avr_loss=0.0319]
steps:   1%|          | 106/10016 [22:47<35:30:16, 12.90s/it, avr_loss=0.0319]
steps:   1%|          | 106/10016 [22:47<35:30:16, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 107/10016 [23:00<35:30:01, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 107/10016 [23:00<35:30:01, 12.90s/it, avr_loss=0.0319]
steps:   1%|          | 108/10016 [23:12<35:29:47, 12.90s/it, avr_loss=0.0319]
steps:   1%|          | 108/10016 [23:12<35:29:47, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 109/10016 [23:25<35:29:32, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 109/10016 [23:25<35:29:32, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 110/10016 [23:38<35:29:18, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 110/10016 [23:38<35:29:18, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 111/10016 [23:51<35:29:04, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 111/10016 [23:51<35:29:04, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 112/10016 [24:04<35:28:51, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 112/10016 [24:04<35:28:51, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 113/10016 [24:17<35:28:36, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 113/10016 [24:17<35:28:36, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 114/10016 [24:30<35:28:22, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 114/10016 [24:30<35:28:22, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 115/10016 [24:43<35:28:07, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 115/10016 [24:43<35:28:07, 12.90s/it, avr_loss=0.0319]
steps:   1%|          | 116/10016 [24:55<35:27:54, 12.90s/it, avr_loss=0.0319]
steps:   1%|          | 116/10016 [24:55<35:27:54, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 117/10016 [25:08<35:27:41, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 117/10016 [25:08<35:27:41, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 118/10016 [25:21<35:27:29, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 118/10016 [25:21<35:27:29, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 119/10016 [25:34<35:27:25, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 119/10016 [25:34<35:27:25, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 120/10016 [25:47<35:27:10, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 120/10016 [25:47<35:27:10, 12.90s/it, avr_loss=0.0316]
steps:   1%|          | 121/10016 [26:00<35:26:57, 12.90s/it, avr_loss=0.0316]
steps:   1%|          | 121/10016 [26:00<35:26:57, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 122/10016 [26:13<35:26:41, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 122/10016 [26:13<35:26:41, 12.90s/it, avr_loss=0.0319]
steps:   1%|          | 123/10016 [26:26<35:26:26, 12.90s/it, avr_loss=0.0319]
steps:   1%|          | 123/10016 [26:26<35:26:26, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 124/10016 [26:39<35:26:12, 12.90s/it, avr_loss=0.0318]
steps:   1%|          | 124/10016 [26:39<35:26:12, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 125/10016 [26:52<35:25:57, 12.90s/it, avr_loss=0.0317]
steps:   1%|          | 125/10016 [26:52<35:25:57, 12.90s/it, avr_loss=0.0316]
steps:   1%|▏         | 126/10016 [27:04<35:25:42, 12.90s/it, avr_loss=0.0316]
steps:   1%|▏         | 126/10016 [27:04<35:25:42, 12.90s/it, avr_loss=0.0316]
steps:   1%|▏         | 127/10016 [27:17<35:25:28, 12.90s/it, avr_loss=0.0316]
steps:   1%|▏         | 127/10016 [27:17<35:25:28, 12.90s/it, avr_loss=0.0315]
steps:   1%|▏         | 128/10016 [27:30<35:25:13, 12.90s/it, avr_loss=0.0315]
steps:   1%|▏         | 128/10016 [27:30<35:25:13, 12.90s/it, avr_loss=0.0314]
steps:   1%|▏         | 129/10016 [27:43<35:24:59, 12.90s/it, avr_loss=0.0314]
steps:   1%|▏         | 129/10016 [27:43<35:24:59, 12.90s/it, avr_loss=0.0314]
steps:   1%|▏         | 130/10016 [27:56<35:24:45, 12.90s/it, avr_loss=0.0314]
steps:   1%|▏         | 130/10016 [27:56<35:24:45, 12.90s/it, avr_loss=0.0314]
steps:   1%|▏         | 131/10016 [28:09<35:24:31, 12.90s/it, avr_loss=0.0314]
steps:   1%|▏         | 131/10016 [28:09<35:24:31, 12.90s/it, avr_loss=0.0313]
steps:   1%|▏         | 132/10016 [28:22<35:24:17, 12.90s/it, avr_loss=0.0313]
steps:   1%|▏         | 132/10016 [28:22<35:24:17, 12.90s/it, avr_loss=0.0312]
steps:   1%|▏         | 133/10016 [28:35<35:24:02, 12.90s/it, avr_loss=0.0312]
steps:   1%|▏         | 133/10016 [28:35<35:24:02, 12.90s/it, avr_loss=0.0311]
steps:   1%|▏         | 134/10016 [28:47<35:23:47, 12.89s/it, avr_loss=0.0311]
steps:   1%|▏         | 134/10016 [28:47<35:23:47, 12.89s/it, avr_loss=0.0312]
steps:   1%|▏         | 135/10016 [29:00<35:23:33, 12.89s/it, avr_loss=0.0312]
steps:   1%|▏         | 135/10016 [29:00<35:23:33, 12.89s/it, avr_loss=0.0311]
steps:   1%|▏         | 136/10016 [29:13<35:23:19, 12.89s/it, avr_loss=0.0311]
steps:   1%|▏         | 136/10016 [29:13<35:23:19, 12.89s/it, avr_loss=0.031] 
steps:   1%|▏         | 137/10016 [29:26<35:23:04, 12.89s/it, avr_loss=0.031]
steps:   1%|▏         | 137/10016 [29:26<35:23:04, 12.89s/it, avr_loss=0.0309]
steps:   1%|▏         | 138/10016 [29:39<35:22:50, 12.89s/it, avr_loss=0.0309]
steps:   1%|▏         | 138/10016 [29:39<35:22:50, 12.89s/it, avr_loss=0.0308]
steps:   1%|▏         | 139/10016 [29:52<35:22:36, 12.89s/it, avr_loss=0.0308]
steps:   1%|▏         | 139/10016 [29:52<35:22:36, 12.89s/it, avr_loss=0.0308]
steps:   1%|▏         | 140/10016 [30:05<35:22:21, 12.89s/it, avr_loss=0.0308]
steps:   1%|▏         | 140/10016 [30:05<35:22:21, 12.89s/it, avr_loss=0.0307]
steps:   1%|▏         | 141/10016 [30:18<35:22:06, 12.89s/it, avr_loss=0.0307]
steps:   1%|▏         | 141/10016 [30:18<35:22:06, 12.89s/it, avr_loss=0.0307]
steps:   1%|▏         | 142/10016 [30:30<35:21:52, 12.89s/it, avr_loss=0.0307]
steps:   1%|▏         | 142/10016 [30:30<35:21:52, 12.89s/it, avr_loss=0.0306]
steps:   1%|▏         | 143/10016 [30:43<35:21:38, 12.89s/it, avr_loss=0.0306]
steps:   1%|▏         | 143/10016 [30:43<35:21:38, 12.89s/it, avr_loss=0.0306]
steps:   1%|▏         | 144/10016 [30:56<35:21:25, 12.89s/it, avr_loss=0.0306]
steps:   1%|▏         | 144/10016 [30:56<35:21:26, 12.89s/it, avr_loss=0.0305]
steps:   1%|▏         | 145/10016 [31:09<35:21:11, 12.89s/it, avr_loss=0.0305]
steps:   1%|▏         | 145/10016 [31:09<35:21:11, 12.89s/it, avr_loss=0.0305]
steps:   1%|▏         | 146/10016 [31:22<35:20:58, 12.89s/it, avr_loss=0.0305]
steps:   1%|▏         | 146/10016 [31:22<35:20:58, 12.89s/it, avr_loss=0.0309]
steps:   1%|▏         | 147/10016 [31:35<35:20:45, 12.89s/it, avr_loss=0.0309]
steps:   1%|▏         | 147/10016 [31:35<35:20:45, 12.89s/it, avr_loss=0.0308]
steps:   1%|▏         | 148/10016 [31:48<35:20:32, 12.89s/it, avr_loss=0.0308]
steps:   1%|▏         | 148/10016 [31:48<35:20:32, 12.89s/it, avr_loss=0.0308]
steps:   1%|▏         | 149/10016 [32:01<35:20:18, 12.89s/it, avr_loss=0.0308]
steps:   1%|▏         | 149/10016 [32:01<35:20:18, 12.89s/it, avr_loss=0.0307]
steps:   1%|▏         | 150/10016 [32:13<35:20:03, 12.89s/it, avr_loss=0.0307]
steps:   1%|▏         | 150/10016 [32:13<35:20:03, 12.89s/it, avr_loss=0.0309]
steps:   2%|▏         | 151/10016 [32:26<35:19:49, 12.89s/it, avr_loss=0.0309]
steps:   2%|▏         | 151/10016 [32:26<35:19:49, 12.89s/it, avr_loss=0.0308]
steps:   2%|▏         | 152/10016 [32:39<35:19:36, 12.89s/it, avr_loss=0.0308]
steps:   2%|▏         | 152/10016 [32:39<35:19:36, 12.89s/it, avr_loss=0.0307]
steps:   2%|▏         | 153/10016 [32:52<35:19:22, 12.89s/it, avr_loss=0.0307]
steps:   2%|▏         | 153/10016 [32:52<35:19:22, 12.89s/it, avr_loss=0.0306]
steps:   2%|▏         | 154/10016 [33:05<35:19:08, 12.89s/it, avr_loss=0.0306]
steps:   2%|▏         | 154/10016 [33:05<35:19:08, 12.89s/it, avr_loss=0.0305]
steps:   2%|▏         | 155/10016 [33:18<35:18:55, 12.89s/it, avr_loss=0.0305]
steps:   2%|▏         | 155/10016 [33:18<35:18:55, 12.89s/it, avr_loss=0.0305]
steps:   2%|▏         | 156/10016 [33:31<35:18:40, 12.89s/it, avr_loss=0.0305]
steps:   2%|▏         | 156/10016 [33:31<35:18:40, 12.89s/it, avr_loss=0.0307]
steps:   2%|▏         | 157/10016 [33:44<35:18:26, 12.89s/it, avr_loss=0.0307]
steps:   2%|▏         | 157/10016 [33:44<35:18:26, 12.89s/it, avr_loss=0.0306]
steps:   2%|▏         | 158/10016 [33:56<35:18:12, 12.89s/it, avr_loss=0.0306]
steps:   2%|▏         | 158/10016 [33:56<35:18:12, 12.89s/it, avr_loss=0.0306]
steps:   2%|▏         | 159/10016 [34:09<35:17:58, 12.89s/it, avr_loss=0.0306]
steps:   2%|▏         | 159/10016 [34:09<35:17:58, 12.89s/it, avr_loss=0.0306]
steps:   2%|▏         | 160/10016 [34:22<35:17:44, 12.89s/it, avr_loss=0.0306]
steps:   2%|▏         | 160/10016 [34:22<35:17:44, 12.89s/it, avr_loss=0.0306]
steps:   2%|▏         | 161/10016 [34:35<35:17:31, 12.89s/it, avr_loss=0.0306]
steps:   2%|▏         | 161/10016 [34:35<35:17:31, 12.89s/it, avr_loss=0.0306]
steps:   2%|▏         | 162/10016 [34:48<35:17:17, 12.89s/it, avr_loss=0.0306]
steps:   2%|▏         | 162/10016 [34:48<35:17:17, 12.89s/it, avr_loss=0.0309]
steps:   2%|▏         | 163/10016 [35:01<35:17:03, 12.89s/it, avr_loss=0.0309]
steps:   2%|▏         | 163/10016 [35:01<35:17:03, 12.89s/it, avr_loss=0.0308]
steps:   2%|▏         | 164/10016 [35:14<35:16:49, 12.89s/it, avr_loss=0.0308]
steps:   2%|▏         | 164/10016 [35:14<35:16:49, 12.89s/it, avr_loss=0.0309]
steps:   2%|▏         | 165/10016 [35:27<35:16:35, 12.89s/it, avr_loss=0.0309]
steps:   2%|▏         | 165/10016 [35:27<35:16:35, 12.89s/it, avr_loss=0.0308]
steps:   2%|▏         | 166/10016 [35:40<35:16:22, 12.89s/it, avr_loss=0.0308]
steps:   2%|▏         | 166/10016 [35:40<35:16:22, 12.89s/it, avr_loss=0.0308]
steps:   2%|▏         | 167/10016 [35:52<35:16:07, 12.89s/it, avr_loss=0.0308]
steps:   2%|▏         | 167/10016 [35:52<35:16:07, 12.89s/it, avr_loss=0.0307]
steps:   2%|▏         | 168/10016 [36:05<35:15:54, 12.89s/it, avr_loss=0.0307]
steps:   2%|▏         | 168/10016 [36:05<35:15:54, 12.89s/it, avr_loss=0.0307]
steps:   2%|▏         | 169/10016 [36:18<35:15:41, 12.89s/it, avr_loss=0.0307]
steps:   2%|▏         | 169/10016 [36:18<35:15:41, 12.89s/it, avr_loss=0.0306]
steps:   2%|▏         | 170/10016 [36:31<35:15:27, 12.89s/it, avr_loss=0.0306]
steps:   2%|▏         | 170/10016 [36:31<35:15:27, 12.89s/it, avr_loss=0.0305]
steps:   2%|▏         | 171/10016 [36:44<35:15:13, 12.89s/it, avr_loss=0.0305]
steps:   2%|▏         | 171/10016 [36:44<35:15:13, 12.89s/it, avr_loss=0.0305]
steps:   2%|▏         | 172/10016 [36:57<35:15:00, 12.89s/it, avr_loss=0.0305]
steps:   2%|▏         | 172/10016 [36:57<35:15:00, 12.89s/it, avr_loss=0.0304]
steps:   2%|▏         | 173/10016 [37:10<35:14:46, 12.89s/it, avr_loss=0.0304]
steps:   2%|▏         | 173/10016 [37:10<35:14:46, 12.89s/it, avr_loss=0.0303]
steps:   2%|▏         | 174/10016 [37:23<35:14:32, 12.89s/it, avr_loss=0.0303]
steps:   2%|▏         | 174/10016 [37:23<35:14:32, 12.89s/it, avr_loss=0.0304]
steps:   2%|▏         | 175/10016 [37:35<35:14:19, 12.89s/it, avr_loss=0.0304]
steps:   2%|▏         | 175/10016 [37:35<35:14:19, 12.89s/it, avr_loss=0.0304]
steps:   2%|▏         | 176/10016 [37:48<35:14:05, 12.89s/it, avr_loss=0.0304]
steps:   2%|▏         | 176/10016 [37:48<35:14:05, 12.89s/it, avr_loss=0.0304]
steps:   2%|▏         | 177/10016 [38:01<35:13:52, 12.89s/it, avr_loss=0.0304]
steps:   2%|▏         | 177/10016 [38:01<35:13:52, 12.89s/it, avr_loss=0.0304]
steps:   2%|▏         | 178/10016 [38:14<35:13:39, 12.89s/it, avr_loss=0.0304]
steps:   2%|▏         | 178/10016 [38:14<35:13:39, 12.89s/it, avr_loss=0.0303]
steps:   2%|▏         | 179/10016 [38:27<35:13:25, 12.89s/it, avr_loss=0.0303]
steps:   2%|▏         | 179/10016 [38:27<35:13:25, 12.89s/it, avr_loss=0.0303]
steps:   2%|▏         | 180/10016 [38:40<35:13:11, 12.89s/it, avr_loss=0.0303]
steps:   2%|▏         | 180/10016 [38:40<35:13:11, 12.89s/it, avr_loss=0.0305]
steps:   2%|▏         | 181/10016 [38:53<35:12:58, 12.89s/it, avr_loss=0.0305]
steps:   2%|▏         | 181/10016 [38:53<35:12:58, 12.89s/it, avr_loss=0.0305]
steps:   2%|▏         | 182/10016 [39:06<35:12:44, 12.89s/it, avr_loss=0.0305]
steps:   2%|▏         | 182/10016 [39:06<35:12:44, 12.89s/it, avr_loss=0.0309]
steps:   2%|▏         | 183/10016 [39:18<35:12:31, 12.89s/it, avr_loss=0.0309]
steps:   2%|▏         | 183/10016 [39:18<35:12:31, 12.89s/it, avr_loss=0.0308]
steps:   2%|▏         | 184/10016 [39:31<35:12:17, 12.89s/it, avr_loss=0.0308]
steps:   2%|▏         | 184/10016 [39:31<35:12:17, 12.89s/it, avr_loss=0.0309]
steps:   2%|▏         | 185/10016 [39:44<35:12:03, 12.89s/it, avr_loss=0.0309]
steps:   2%|▏         | 185/10016 [39:44<35:12:03, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 186/10016 [39:57<35:11:49, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 186/10016 [39:57<35:11:49, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 187/10016 [40:10<35:11:35, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 187/10016 [40:10<35:11:35, 12.89s/it, avr_loss=0.0318]
steps:   2%|▏         | 188/10016 [40:23<35:11:22, 12.89s/it, avr_loss=0.0318]
steps:   2%|▏         | 188/10016 [40:23<35:11:22, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 189/10016 [40:36<35:11:08, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 189/10016 [40:36<35:11:08, 12.89s/it, avr_loss=0.0321]
steps:   2%|▏         | 190/10016 [40:49<35:10:55, 12.89s/it, avr_loss=0.0321]
steps:   2%|▏         | 190/10016 [40:49<35:10:55, 12.89s/it, avr_loss=0.0321]
steps:   2%|▏         | 191/10016 [41:01<35:10:41, 12.89s/it, avr_loss=0.0321]
steps:   2%|▏         | 191/10016 [41:01<35:10:41, 12.89s/it, avr_loss=0.032] 
steps:   2%|▏         | 192/10016 [41:14<35:10:28, 12.89s/it, avr_loss=0.032]
steps:   2%|▏         | 192/10016 [41:14<35:10:28, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 193/10016 [41:27<35:10:14, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 193/10016 [41:27<35:10:14, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 194/10016 [41:40<35:10:01, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 194/10016 [41:40<35:10:01, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 195/10016 [41:53<35:09:47, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 195/10016 [41:53<35:09:47, 12.89s/it, avr_loss=0.0318]
steps:   2%|▏         | 196/10016 [42:06<35:09:34, 12.89s/it, avr_loss=0.0318]
steps:   2%|▏         | 196/10016 [42:06<35:09:34, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 197/10016 [42:19<35:09:21, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 197/10016 [42:19<35:09:21, 12.89s/it, avr_loss=0.0318]
steps:   2%|▏         | 198/10016 [42:32<35:09:07, 12.89s/it, avr_loss=0.0318]
steps:   2%|▏         | 198/10016 [42:32<35:09:07, 12.89s/it, avr_loss=0.0318]
steps:   2%|▏         | 199/10016 [42:44<35:08:54, 12.89s/it, avr_loss=0.0318]
steps:   2%|▏         | 199/10016 [42:44<35:08:54, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 200/10016 [42:57<35:08:40, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 200/10016 [42:57<35:08:40, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 201/10016 [43:10<35:08:27, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 201/10016 [43:10<35:08:27, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 202/10016 [43:23<35:08:14, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 202/10016 [43:23<35:08:14, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 203/10016 [43:36<35:08:00, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 203/10016 [43:36<35:08:00, 12.89s/it, avr_loss=0.0321]
steps:   2%|▏         | 204/10016 [43:49<35:07:47, 12.89s/it, avr_loss=0.0321]
steps:   2%|▏         | 204/10016 [43:49<35:07:47, 12.89s/it, avr_loss=0.0321]
steps:   2%|▏         | 205/10016 [44:02<35:07:34, 12.89s/it, avr_loss=0.0321]
steps:   2%|▏         | 205/10016 [44:02<35:07:34, 12.89s/it, avr_loss=0.032] 
steps:   2%|▏         | 206/10016 [44:15<35:07:21, 12.89s/it, avr_loss=0.032]
steps:   2%|▏         | 206/10016 [44:15<35:07:21, 12.89s/it, avr_loss=0.032]
steps:   2%|▏         | 207/10016 [44:28<35:07:08, 12.89s/it, avr_loss=0.032]
steps:   2%|▏         | 207/10016 [44:28<35:07:08, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 208/10016 [44:40<35:06:55, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 208/10016 [44:40<35:06:55, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 209/10016 [44:53<35:06:41, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 209/10016 [44:53<35:06:41, 12.89s/it, avr_loss=0.0318]
steps:   2%|▏         | 210/10016 [45:06<35:06:29, 12.89s/it, avr_loss=0.0318]
steps:   2%|▏         | 210/10016 [45:06<35:06:29, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 211/10016 [45:19<35:06:16, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 211/10016 [45:19<35:06:16, 12.89s/it, avr_loss=0.0318]
steps:   2%|▏         | 212/10016 [45:32<35:06:02, 12.89s/it, avr_loss=0.0318]
steps:   2%|▏         | 212/10016 [45:32<35:06:02, 12.89s/it, avr_loss=0.0325]
steps:   2%|▏         | 213/10016 [45:45<35:05:48, 12.89s/it, avr_loss=0.0325]
steps:   2%|▏         | 213/10016 [45:45<35:05:48, 12.89s/it, avr_loss=0.0325]
steps:   2%|▏         | 214/10016 [45:58<35:05:35, 12.89s/it, avr_loss=0.0325]
steps:   2%|▏         | 214/10016 [45:58<35:05:35, 12.89s/it, avr_loss=0.0324]
steps:   2%|▏         | 215/10016 [46:11<35:05:22, 12.89s/it, avr_loss=0.0324]
steps:   2%|▏         | 215/10016 [46:11<35:05:22, 12.89s/it, avr_loss=0.0323]
steps:   2%|▏         | 216/10016 [46:23<35:05:08, 12.89s/it, avr_loss=0.0323]
steps:   2%|▏         | 216/10016 [46:23<35:05:08, 12.89s/it, avr_loss=0.0323]
steps:   2%|▏         | 217/10016 [46:36<35:04:55, 12.89s/it, avr_loss=0.0323]
steps:   2%|▏         | 217/10016 [46:36<35:04:55, 12.89s/it, avr_loss=0.0323]
steps:   2%|▏         | 218/10016 [46:49<35:04:42, 12.89s/it, avr_loss=0.0323]
steps:   2%|▏         | 218/10016 [46:49<35:04:42, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 219/10016 [47:02<35:04:28, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 219/10016 [47:02<35:04:28, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 220/10016 [47:15<35:04:15, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 220/10016 [47:15<35:04:15, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 221/10016 [47:28<35:04:02, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 221/10016 [47:28<35:04:02, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 222/10016 [47:41<35:03:49, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 222/10016 [47:41<35:03:49, 12.89s/it, avr_loss=0.0321]
steps:   2%|▏         | 223/10016 [47:54<35:03:35, 12.89s/it, avr_loss=0.0321]
steps:   2%|▏         | 223/10016 [47:54<35:03:35, 12.89s/it, avr_loss=0.0321]
steps:   2%|▏         | 224/10016 [48:06<35:03:22, 12.89s/it, avr_loss=0.0321]
steps:   2%|▏         | 224/10016 [48:06<35:03:22, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 225/10016 [48:19<35:03:09, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 225/10016 [48:19<35:03:09, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 226/10016 [48:32<35:02:55, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 226/10016 [48:32<35:02:55, 12.89s/it, avr_loss=0.0323]
steps:   2%|▏         | 227/10016 [48:45<35:02:42, 12.89s/it, avr_loss=0.0323]
steps:   2%|▏         | 227/10016 [48:45<35:02:42, 12.89s/it, avr_loss=0.0323]
steps:   2%|▏         | 228/10016 [48:58<35:02:34, 12.89s/it, avr_loss=0.0323]
steps:   2%|▏         | 228/10016 [48:58<35:02:34, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 229/10016 [49:11<35:02:21, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 229/10016 [49:11<35:02:21, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 230/10016 [49:24<35:02:08, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 230/10016 [49:24<35:02:08, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 231/10016 [49:37<35:01:55, 12.89s/it, avr_loss=0.0322]
steps:   2%|▏         | 231/10016 [49:37<35:01:55, 12.89s/it, avr_loss=0.0321]
steps:   2%|▏         | 232/10016 [49:50<35:01:42, 12.89s/it, avr_loss=0.0321]
steps:   2%|▏         | 232/10016 [49:50<35:01:42, 12.89s/it, avr_loss=0.0321]
steps:   2%|▏         | 233/10016 [50:03<35:01:29, 12.89s/it, avr_loss=0.0321]
steps:   2%|▏         | 233/10016 [50:03<35:01:29, 12.89s/it, avr_loss=0.032] 
steps:   2%|▏         | 234/10016 [50:15<35:01:16, 12.89s/it, avr_loss=0.032]
steps:   2%|▏         | 234/10016 [50:15<35:01:16, 12.89s/it, avr_loss=0.032]
steps:   2%|▏         | 235/10016 [50:28<35:01:03, 12.89s/it, avr_loss=0.032]
steps:   2%|▏         | 235/10016 [50:28<35:01:03, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 236/10016 [50:41<35:00:51, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 236/10016 [50:41<35:00:51, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 237/10016 [50:54<35:00:39, 12.89s/it, avr_loss=0.0319]
steps:   2%|▏         | 237/10016 [50:54<35:00:39, 12.89s/it, avr_loss=0.0318]
steps:   2%|▏         | 238/10016 [51:07<35:00:27, 12.89s/it, avr_loss=0.0318]
steps:   2%|▏         | 238/10016 [51:07<35:00:27, 12.89s/it, avr_loss=0.0318]
steps:   2%|▏         | 239/10016 [51:20<35:00:14, 12.89s/it, avr_loss=0.0318]
steps:   2%|▏         | 239/10016 [51:20<35:00:14, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 240/10016 [51:33<35:00:02, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 240/10016 [51:33<35:00:02, 12.89s/it, avr_loss=0.0318]
steps:   2%|▏         | 241/10016 [51:46<34:59:50, 12.89s/it, avr_loss=0.0318]
steps:   2%|▏         | 241/10016 [51:46<34:59:50, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 242/10016 [51:59<34:59:37, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 242/10016 [51:59<34:59:37, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 243/10016 [52:12<34:59:24, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 243/10016 [52:12<34:59:24, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 244/10016 [52:24<34:59:11, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 244/10016 [52:24<34:59:11, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 245/10016 [52:37<34:58:59, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 245/10016 [52:37<34:58:59, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 246/10016 [52:50<34:58:46, 12.89s/it, avr_loss=0.0317]
steps:   2%|▏         | 246/10016 [52:50<34:58:46, 12.89s/it, avr_loss=0.0316]
steps:   2%|▏         | 247/10016 [53:03<34:58:34, 12.89s/it, avr_loss=0.0316]
steps:   2%|▏         | 247/10016 [53:03<34:58:34, 12.89s/it, avr_loss=0.0316]
steps:   2%|▏         | 248/10016 [53:16<34:58:21, 12.89s/it, avr_loss=0.0316]
steps:   2%|▏         | 248/10016 [53:16<34:58:21, 12.89s/it, avr_loss=0.0316]
steps:   2%|▏         | 249/10016 [53:29<34:58:08, 12.89s/it, avr_loss=0.0316]
steps:   2%|▏         | 249/10016 [53:29<34:58:08, 12.89s/it, avr_loss=0.0315]
steps:   2%|▏         | 250/10016 [53:42<34:57:56, 12.89s/it, avr_loss=0.0315]
steps:   2%|▏         | 250/10016 [53:42<34:57:56, 12.89s/it, avr_loss=0.0315]
steps:   3%|▎         | 251/10016 [53:55<34:57:43, 12.89s/it, avr_loss=0.0315]
steps:   3%|▎         | 251/10016 [53:55<34:57:43, 12.89s/it, avr_loss=0.0314]
steps:   3%|▎         | 252/10016 [54:08<34:57:31, 12.89s/it, avr_loss=0.0314]
steps:   3%|▎         | 252/10016 [54:08<34:57:31, 12.89s/it, avr_loss=0.0314]
steps:   3%|▎         | 253/10016 [54:21<34:57:19, 12.89s/it, avr_loss=0.0314]
steps:   3%|▎         | 253/10016 [54:21<34:57:19, 12.89s/it, avr_loss=0.0313]
steps:   3%|▎         | 254/10016 [54:33<34:57:07, 12.89s/it, avr_loss=0.0313]
steps:   3%|▎         | 254/10016 [54:33<34:57:07, 12.89s/it, avr_loss=0.0313]
steps:   3%|▎         | 255/10016 [54:46<34:56:55, 12.89s/it, avr_loss=0.0313]
steps:   3%|▎         | 255/10016 [54:46<34:56:55, 12.89s/it, avr_loss=0.0313]
steps:   3%|▎         | 256/10016 [54:59<34:56:43, 12.89s/it, avr_loss=0.0313]
steps:   3%|▎         | 256/10016 [54:59<34:56:43, 12.89s/it, avr_loss=0.0312]
steps:   3%|▎         | 257/10016 [55:12<34:56:30, 12.89s/it, avr_loss=0.0312]
steps:   3%|▎         | 257/10016 [55:12<34:56:30, 12.89s/it, avr_loss=0.0312]
steps:   3%|▎         | 258/10016 [55:25<34:56:18, 12.89s/it, avr_loss=0.0312]
steps:   3%|▎         | 258/10016 [55:25<34:56:18, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 259/10016 [55:38<34:56:05, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 259/10016 [55:38<34:56:05, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 260/10016 [55:51<34:55:53, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 260/10016 [55:51<34:55:53, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 261/10016 [56:04<34:55:40, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 261/10016 [56:04<34:55:40, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 262/10016 [56:17<34:55:28, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 262/10016 [56:17<34:55:28, 12.89s/it, avr_loss=0.031] 
steps:   3%|▎         | 263/10016 [56:30<34:55:15, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 263/10016 [56:30<34:55:15, 12.89s/it, avr_loss=0.0312]
steps:   3%|▎         | 264/10016 [56:42<34:55:02, 12.89s/it, avr_loss=0.0312]
steps:   3%|▎         | 264/10016 [56:42<34:55:02, 12.89s/it, avr_loss=0.0312]
steps:   3%|▎         | 265/10016 [56:55<34:54:50, 12.89s/it, avr_loss=0.0312]
steps:   3%|▎         | 265/10016 [56:55<34:54:50, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 266/10016 [57:08<34:54:38, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 266/10016 [57:08<34:54:38, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 267/10016 [57:21<34:54:25, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 267/10016 [57:21<34:54:25, 12.89s/it, avr_loss=0.0312]
steps:   3%|▎         | 268/10016 [57:34<34:54:13, 12.89s/it, avr_loss=0.0312]
steps:   3%|▎         | 268/10016 [57:34<34:54:13, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 269/10016 [57:47<34:54:01, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 269/10016 [57:47<34:54:01, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 270/10016 [58:00<34:53:48, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 270/10016 [58:00<34:53:48, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 271/10016 [58:13<34:53:35, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 271/10016 [58:13<34:53:35, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 272/10016 [58:26<34:53:23, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 272/10016 [58:26<34:53:23, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 273/10016 [58:39<34:53:10, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 273/10016 [58:39<34:53:10, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 274/10016 [58:51<34:52:57, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 274/10016 [58:51<34:52:57, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 275/10016 [59:04<34:52:44, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 275/10016 [59:04<34:52:44, 12.89s/it, avr_loss=0.031] 
steps:   3%|▎         | 276/10016 [59:17<34:52:32, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 276/10016 [59:17<34:52:32, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 277/10016 [59:30<34:52:19, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 277/10016 [59:30<34:52:19, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 278/10016 [59:43<34:52:06, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 278/10016 [59:43<34:52:06, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 279/10016 [59:56<34:51:53, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 279/10016 [59:56<34:51:53, 12.89s/it, avr_loss=0.0314]
steps:   3%|▎         | 280/10016 [1:00:09<34:51:40, 12.89s/it, avr_loss=0.0314]
steps:   3%|▎         | 280/10016 [1:00:09<34:51:40, 12.89s/it, avr_loss=0.0314]
steps:   3%|▎         | 281/10016 [1:00:22<34:51:27, 12.89s/it, avr_loss=0.0314]
steps:   3%|▎         | 281/10016 [1:00:22<34:51:27, 12.89s/it, avr_loss=0.0314]
steps:   3%|▎         | 282/10016 [1:00:35<34:51:14, 12.89s/it, avr_loss=0.0314]
steps:   3%|▎         | 282/10016 [1:00:35<34:51:14, 12.89s/it, avr_loss=0.0316]
steps:   3%|▎         | 283/10016 [1:00:47<34:51:01, 12.89s/it, avr_loss=0.0316]
steps:   3%|▎         | 283/10016 [1:00:47<34:51:01, 12.89s/it, avr_loss=0.0316]
steps:   3%|▎         | 284/10016 [1:01:00<34:50:48, 12.89s/it, avr_loss=0.0316]
steps:   3%|▎         | 284/10016 [1:01:00<34:50:48, 12.89s/it, avr_loss=0.0316]
steps:   3%|▎         | 285/10016 [1:01:13<34:50:36, 12.89s/it, avr_loss=0.0316]
steps:   3%|▎         | 285/10016 [1:01:13<34:50:36, 12.89s/it, avr_loss=0.0316]
steps:   3%|▎         | 286/10016 [1:01:26<34:50:23, 12.89s/it, avr_loss=0.0316]
steps:   3%|▎         | 286/10016 [1:01:26<34:50:23, 12.89s/it, avr_loss=0.0315]
steps:   3%|▎         | 287/10016 [1:01:39<34:50:11, 12.89s/it, avr_loss=0.0315]
steps:   3%|▎         | 287/10016 [1:01:39<34:50:11, 12.89s/it, avr_loss=0.0316]
steps:   3%|▎         | 288/10016 [1:01:52<34:49:58, 12.89s/it, avr_loss=0.0316]
steps:   3%|▎         | 288/10016 [1:01:52<34:49:58, 12.89s/it, avr_loss=0.0316]
steps:   3%|▎         | 289/10016 [1:02:05<34:49:45, 12.89s/it, avr_loss=0.0316]
steps:   3%|▎         | 289/10016 [1:02:05<34:49:45, 12.89s/it, avr_loss=0.0315]
steps:   3%|▎         | 290/10016 [1:02:18<34:49:32, 12.89s/it, avr_loss=0.0315]
steps:   3%|▎         | 290/10016 [1:02:18<34:49:32, 12.89s/it, avr_loss=0.0315]
steps:   3%|▎         | 291/10016 [1:02:31<34:49:19, 12.89s/it, avr_loss=0.0315]
steps:   3%|▎         | 291/10016 [1:02:31<34:49:19, 12.89s/it, avr_loss=0.0315]
steps:   3%|▎         | 292/10016 [1:02:44<34:49:06, 12.89s/it, avr_loss=0.0315]
steps:   3%|▎         | 292/10016 [1:02:44<34:49:06, 12.89s/it, avr_loss=0.0315]
steps:   3%|▎         | 293/10016 [1:02:56<34:48:53, 12.89s/it, avr_loss=0.0315]
steps:   3%|▎         | 293/10016 [1:02:56<34:48:53, 12.89s/it, avr_loss=0.0314]
steps:   3%|▎         | 294/10016 [1:03:09<34:48:40, 12.89s/it, avr_loss=0.0314]
steps:   3%|▎         | 294/10016 [1:03:09<34:48:41, 12.89s/it, avr_loss=0.0314]
steps:   3%|▎         | 295/10016 [1:03:22<34:48:28, 12.89s/it, avr_loss=0.0314]
steps:   3%|▎         | 295/10016 [1:03:22<34:48:28, 12.89s/it, avr_loss=0.0314]
steps:   3%|▎         | 296/10016 [1:03:35<34:48:15, 12.89s/it, avr_loss=0.0314]
steps:   3%|▎         | 296/10016 [1:03:35<34:48:15, 12.89s/it, avr_loss=0.0314]
steps:   3%|▎         | 297/10016 [1:03:48<34:48:03, 12.89s/it, avr_loss=0.0314]
steps:   3%|▎         | 297/10016 [1:03:48<34:48:03, 12.89s/it, avr_loss=0.0314]
steps:   3%|▎         | 298/10016 [1:04:01<34:47:51, 12.89s/it, avr_loss=0.0314]
steps:   3%|▎         | 298/10016 [1:04:01<34:47:51, 12.89s/it, avr_loss=0.0314]
steps:   3%|▎         | 299/10016 [1:04:14<34:47:38, 12.89s/it, avr_loss=0.0314]
steps:   3%|▎         | 299/10016 [1:04:14<34:47:38, 12.89s/it, avr_loss=0.0314]
steps:   3%|▎         | 300/10016 [1:04:27<34:47:27, 12.89s/it, avr_loss=0.0314]
steps:   3%|▎         | 300/10016 [1:04:27<34:47:27, 12.89s/it, avr_loss=0.0313]
steps:   3%|▎         | 301/10016 [1:04:40<34:47:15, 12.89s/it, avr_loss=0.0313]
steps:   3%|▎         | 301/10016 [1:04:40<34:47:15, 12.89s/it, avr_loss=0.0313]
steps:   3%|▎         | 302/10016 [1:04:53<34:47:03, 12.89s/it, avr_loss=0.0313]
steps:   3%|▎         | 302/10016 [1:04:53<34:47:03, 12.89s/it, avr_loss=0.0313]
steps:   3%|▎         | 303/10016 [1:05:06<34:46:52, 12.89s/it, avr_loss=0.0313]
steps:   3%|▎         | 303/10016 [1:05:06<34:46:52, 12.89s/it, avr_loss=0.0312]
steps:   3%|▎         | 304/10016 [1:05:18<34:46:40, 12.89s/it, avr_loss=0.0312]
steps:   3%|▎         | 304/10016 [1:05:18<34:46:40, 12.89s/it, avr_loss=0.0312]
steps:   3%|▎         | 305/10016 [1:05:31<34:46:28, 12.89s/it, avr_loss=0.0312]
steps:   3%|▎         | 305/10016 [1:05:31<34:46:28, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 306/10016 [1:05:44<34:46:16, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 306/10016 [1:05:44<34:46:16, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 307/10016 [1:05:57<34:46:03, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 307/10016 [1:05:57<34:46:03, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 308/10016 [1:06:10<34:45:50, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 308/10016 [1:06:10<34:45:50, 12.89s/it, avr_loss=0.031] 
steps:   3%|▎         | 309/10016 [1:06:23<34:45:37, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 309/10016 [1:06:23<34:45:37, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 310/10016 [1:06:36<34:45:24, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 310/10016 [1:06:36<34:45:24, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 311/10016 [1:06:49<34:45:11, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 311/10016 [1:06:49<34:45:11, 12.89s/it, avr_loss=0.031] 
steps:   3%|▎         | 312/10016 [1:07:02<34:44:57, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 312/10016 [1:07:02<34:44:57, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 313/10016 [1:07:14<34:44:44, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 313/10016 [1:07:14<34:44:44, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 314/10016 [1:07:27<34:44:31, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 314/10016 [1:07:27<34:44:31, 12.89s/it, avr_loss=0.0309]
steps:   3%|▎         | 315/10016 [1:07:40<34:44:18, 12.89s/it, avr_loss=0.0309]
steps:   3%|▎         | 315/10016 [1:07:40<34:44:18, 12.89s/it, avr_loss=0.0309]
steps:   3%|▎         | 316/10016 [1:07:53<34:44:05, 12.89s/it, avr_loss=0.0309]
steps:   3%|▎         | 316/10016 [1:07:53<34:44:05, 12.89s/it, avr_loss=0.0309]
steps:   3%|▎         | 317/10016 [1:08:06<34:43:52, 12.89s/it, avr_loss=0.0309]
steps:   3%|▎         | 317/10016 [1:08:06<34:43:52, 12.89s/it, avr_loss=0.0309]
steps:   3%|▎         | 318/10016 [1:08:19<34:43:39, 12.89s/it, avr_loss=0.0309]
steps:   3%|▎         | 318/10016 [1:08:19<34:43:39, 12.89s/it, avr_loss=0.031] 
steps:   3%|▎         | 319/10016 [1:08:32<34:43:26, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 319/10016 [1:08:32<34:43:26, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 320/10016 [1:08:45<34:43:13, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 320/10016 [1:08:45<34:43:13, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 321/10016 [1:08:58<34:43:00, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 321/10016 [1:08:58<34:43:00, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 322/10016 [1:09:10<34:42:47, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 322/10016 [1:09:10<34:42:47, 12.89s/it, avr_loss=0.0309]
steps:   3%|▎         | 323/10016 [1:09:23<34:42:34, 12.89s/it, avr_loss=0.0309]
steps:   3%|▎         | 323/10016 [1:09:23<34:42:34, 12.89s/it, avr_loss=0.0309]
steps:   3%|▎         | 324/10016 [1:09:36<34:42:21, 12.89s/it, avr_loss=0.0309]
steps:   3%|▎         | 324/10016 [1:09:36<34:42:21, 12.89s/it, avr_loss=0.0309]
steps:   3%|▎         | 325/10016 [1:09:49<34:42:09, 12.89s/it, avr_loss=0.0309]
steps:   3%|▎         | 325/10016 [1:09:49<34:42:09, 12.89s/it, avr_loss=0.0308]
steps:   3%|▎         | 326/10016 [1:10:02<34:41:56, 12.89s/it, avr_loss=0.0308]
steps:   3%|▎         | 326/10016 [1:10:02<34:41:56, 12.89s/it, avr_loss=0.031] 
steps:   3%|▎         | 327/10016 [1:10:15<34:41:43, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 327/10016 [1:10:15<34:41:43, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 328/10016 [1:10:28<34:41:30, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 328/10016 [1:10:28<34:41:30, 12.89s/it, avr_loss=0.0313]
steps:   3%|▎         | 329/10016 [1:10:41<34:41:17, 12.89s/it, avr_loss=0.0313]
steps:   3%|▎         | 329/10016 [1:10:41<34:41:17, 12.89s/it, avr_loss=0.0312]
steps:   3%|▎         | 330/10016 [1:10:54<34:41:05, 12.89s/it, avr_loss=0.0312]
steps:   3%|▎         | 330/10016 [1:10:54<34:41:05, 12.89s/it, avr_loss=0.0312]
steps:   3%|▎         | 331/10016 [1:11:07<34:40:54, 12.89s/it, avr_loss=0.0312]
steps:   3%|▎         | 331/10016 [1:11:07<34:40:54, 12.89s/it, avr_loss=0.0312]
steps:   3%|▎         | 332/10016 [1:11:19<34:40:41, 12.89s/it, avr_loss=0.0312]
steps:   3%|▎         | 332/10016 [1:11:19<34:40:41, 12.89s/it, avr_loss=0.0312]
steps:   3%|▎         | 333/10016 [1:11:32<34:40:28, 12.89s/it, avr_loss=0.0312]
steps:   3%|▎         | 333/10016 [1:11:32<34:40:28, 12.89s/it, avr_loss=0.0312]
steps:   3%|▎         | 334/10016 [1:11:45<34:40:16, 12.89s/it, avr_loss=0.0312]
steps:   3%|▎         | 334/10016 [1:11:45<34:40:16, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 335/10016 [1:11:58<34:40:03, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 335/10016 [1:11:58<34:40:03, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 336/10016 [1:12:11<34:39:50, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 336/10016 [1:12:11<34:39:50, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 337/10016 [1:12:24<34:39:41, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 337/10016 [1:12:24<34:39:41, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 338/10016 [1:12:37<34:39:28, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 338/10016 [1:12:37<34:39:28, 12.89s/it, avr_loss=0.031] 
steps:   3%|▎         | 339/10016 [1:12:50<34:39:15, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 339/10016 [1:12:50<34:39:15, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 340/10016 [1:13:03<34:39:02, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 340/10016 [1:13:03<34:39:02, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 341/10016 [1:13:16<34:38:50, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 341/10016 [1:13:16<34:38:50, 12.89s/it, avr_loss=0.0309]
steps:   3%|▎         | 342/10016 [1:13:29<34:38:37, 12.89s/it, avr_loss=0.0309]
steps:   3%|▎         | 342/10016 [1:13:29<34:38:37, 12.89s/it, avr_loss=0.031] 
steps:   3%|▎         | 343/10016 [1:13:41<34:38:24, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 343/10016 [1:13:41<34:38:24, 12.89s/it, avr_loss=0.0309]
steps:   3%|▎         | 344/10016 [1:13:54<34:38:12, 12.89s/it, avr_loss=0.0309]
steps:   3%|▎         | 344/10016 [1:13:54<34:38:12, 12.89s/it, avr_loss=0.031] 
steps:   3%|▎         | 345/10016 [1:14:07<34:38:00, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 345/10016 [1:14:07<34:38:00, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 346/10016 [1:14:20<34:37:47, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 346/10016 [1:14:20<34:37:47, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 347/10016 [1:14:33<34:37:34, 12.89s/it, avr_loss=0.031]
steps:   3%|▎         | 347/10016 [1:14:33<34:37:34, 12.89s/it, avr_loss=0.0312]
steps:   3%|▎         | 348/10016 [1:14:46<34:37:21, 12.89s/it, avr_loss=0.0312]
steps:   3%|▎         | 348/10016 [1:14:46<34:37:21, 12.89s/it, avr_loss=0.0312]
steps:   3%|▎         | 349/10016 [1:14:59<34:37:08, 12.89s/it, avr_loss=0.0312]
steps:   3%|▎         | 349/10016 [1:14:59<34:37:08, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 350/10016 [1:15:12<34:36:55, 12.89s/it, avr_loss=0.0311]
steps:   3%|▎         | 350/10016 [1:15:12<34:36:55, 12.89s/it, avr_loss=0.0311]
steps:   4%|▎         | 351/10016 [1:15:25<34:36:43, 12.89s/it, avr_loss=0.0311]
steps:   4%|▎         | 351/10016 [1:15:25<34:36:43, 12.89s/it, avr_loss=0.0311]
steps:   4%|▎         | 352/10016 [1:15:38<34:36:30, 12.89s/it, avr_loss=0.0311]
steps:   4%|▎         | 352/10016 [1:15:38<34:36:30, 12.89s/it, avr_loss=0.031] 
steps:   4%|▎         | 353/10016 [1:15:50<34:36:17, 12.89s/it, avr_loss=0.031]
steps:   4%|▎         | 353/10016 [1:15:50<34:36:17, 12.89s/it, avr_loss=0.031]
steps:   4%|▎         | 354/10016 [1:16:03<34:36:04, 12.89s/it, avr_loss=0.031]
steps:   4%|▎         | 354/10016 [1:16:03<34:36:04, 12.89s/it, avr_loss=0.031]
steps:   4%|▎         | 355/10016 [1:16:16<34:35:52, 12.89s/it, avr_loss=0.031]
steps:   4%|▎         | 355/10016 [1:16:16<34:35:52, 12.89s/it, avr_loss=0.031]
steps:   4%|▎         | 356/10016 [1:16:29<34:35:39, 12.89s/it, avr_loss=0.031]
steps:   4%|▎         | 356/10016 [1:16:29<34:35:39, 12.89s/it, avr_loss=0.031]
steps:   4%|▎         | 357/10016 [1:16:42<34:35:26, 12.89s/it, avr_loss=0.031]
steps:   4%|▎         | 357/10016 [1:16:42<34:35:26, 12.89s/it, avr_loss=0.031]
steps:   4%|▎         | 358/10016 [1:16:55<34:35:14, 12.89s/it, avr_loss=0.031]
steps:   4%|▎         | 358/10016 [1:16:55<34:35:14, 12.89s/it, avr_loss=0.0309]
steps:   4%|▎         | 359/10016 [1:17:08<34:35:01, 12.89s/it, avr_loss=0.0309]
steps:   4%|▎         | 359/10016 [1:17:08<34:35:01, 12.89s/it, avr_loss=0.0309]
steps:   4%|▎         | 360/10016 [1:17:21<34:34:48, 12.89s/it, avr_loss=0.0309]
steps:   4%|▎         | 360/10016 [1:17:21<34:34:48, 12.89s/it, avr_loss=0.0309]
steps:   4%|▎         | 361/10016 [1:17:34<34:34:36, 12.89s/it, avr_loss=0.0309]
steps:   4%|▎         | 361/10016 [1:17:34<34:34:36, 12.89s/it, avr_loss=0.0312]
steps:   4%|▎         | 362/10016 [1:17:47<34:34:23, 12.89s/it, avr_loss=0.0312]
steps:   4%|▎         | 362/10016 [1:17:47<34:34:23, 12.89s/it, avr_loss=0.0312]
steps:   4%|▎         | 363/10016 [1:17:59<34:34:10, 12.89s/it, avr_loss=0.0312]
steps:   4%|▎         | 363/10016 [1:17:59<34:34:10, 12.89s/it, avr_loss=0.0312]
steps:   4%|▎         | 364/10016 [1:18:12<34:33:58, 12.89s/it, avr_loss=0.0312]
steps:   4%|▎         | 364/10016 [1:18:12<34:33:58, 12.89s/it, avr_loss=0.0311]
steps:   4%|▎         | 365/10016 [1:18:25<34:33:45, 12.89s/it, avr_loss=0.0311]
steps:   4%|▎         | 365/10016 [1:18:25<34:33:45, 12.89s/it, avr_loss=0.0311]
steps:   4%|▎         | 366/10016 [1:18:38<34:33:32, 12.89s/it, avr_loss=0.0311]
steps:   4%|▎         | 366/10016 [1:18:38<34:33:32, 12.89s/it, avr_loss=0.0311]
steps:   4%|▎         | 367/10016 [1:18:51<34:33:19, 12.89s/it, avr_loss=0.0311]
steps:   4%|▎         | 367/10016 [1:18:51<34:33:19, 12.89s/it, avr_loss=0.0311]
steps:   4%|▎         | 368/10016 [1:19:04<34:33:06, 12.89s/it, avr_loss=0.0311]
steps:   4%|▎         | 368/10016 [1:19:04<34:33:06, 12.89s/it, avr_loss=0.031] 
steps:   4%|▎         | 369/10016 [1:19:17<34:32:53, 12.89s/it, avr_loss=0.031]
steps:   4%|▎         | 369/10016 [1:19:17<34:32:53, 12.89s/it, avr_loss=0.031]
steps:   4%|▎         | 370/10016 [1:19:30<34:32:40, 12.89s/it, avr_loss=0.031]
steps:   4%|▎         | 370/10016 [1:19:30<34:32:40, 12.89s/it, avr_loss=0.031]
steps:   4%|▎         | 371/10016 [1:19:43<34:32:26, 12.89s/it, avr_loss=0.031]
steps:   4%|▎         | 371/10016 [1:19:43<34:32:26, 12.89s/it, avr_loss=0.031]
steps:   4%|▎         | 372/10016 [1:19:55<34:32:14, 12.89s/it, avr_loss=0.031]
steps:   4%|▎         | 372/10016 [1:19:55<34:32:14, 12.89s/it, avr_loss=0.031]
steps:   4%|▎         | 373/10016 [1:20:08<34:32:01, 12.89s/it, avr_loss=0.031]
steps:   4%|▎         | 373/10016 [1:20:08<34:32:01, 12.89s/it, avr_loss=0.031]
steps:   4%|▎         | 374/10016 [1:20:21<34:31:48, 12.89s/it, avr_loss=0.031]
steps:   4%|▎         | 374/10016 [1:20:21<34:31:48, 12.89s/it, avr_loss=0.0309]
steps:   4%|▎         | 375/10016 [1:20:34<34:31:36, 12.89s/it, avr_loss=0.0309]
steps:   4%|▎         | 375/10016 [1:20:34<34:31:36, 12.89s/it, avr_loss=0.0309]
steps:   4%|▍         | 376/10016 [1:20:47<34:31:23, 12.89s/it, avr_loss=0.0309]
steps:   4%|▍         | 376/10016 [1:20:47<34:31:23, 12.89s/it, avr_loss=0.0309]
steps:   4%|▍         | 377/10016 [1:21:00<34:31:11, 12.89s/it, avr_loss=0.0309]
steps:   4%|▍         | 377/10016 [1:21:00<34:31:11, 12.89s/it, avr_loss=0.0309]
steps:   4%|▍         | 378/10016 [1:21:13<34:30:58, 12.89s/it, avr_loss=0.0309]
steps:   4%|▍         | 378/10016 [1:21:13<34:30:58, 12.89s/it, avr_loss=0.0308]
steps:   4%|▍         | 379/10016 [1:21:26<34:30:45, 12.89s/it, avr_loss=0.0308]
steps:   4%|▍         | 379/10016 [1:21:26<34:30:45, 12.89s/it, avr_loss=0.0308]
steps:   4%|▍         | 380/10016 [1:21:39<34:30:33, 12.89s/it, avr_loss=0.0308]
steps:   4%|▍         | 380/10016 [1:21:39<34:30:33, 12.89s/it, avr_loss=0.0308]
steps:   4%|▍         | 381/10016 [1:21:52<34:30:20, 12.89s/it, avr_loss=0.0308]
steps:   4%|▍         | 381/10016 [1:21:52<34:30:20, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 382/10016 [1:22:05<34:30:08, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 382/10016 [1:22:05<34:30:08, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 383/10016 [1:22:17<34:29:55, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 383/10016 [1:22:17<34:29:55, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 384/10016 [1:22:30<34:29:42, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 384/10016 [1:22:30<34:29:42, 12.89s/it, avr_loss=0.0308]
steps:   4%|▍         | 385/10016 [1:22:43<34:29:29, 12.89s/it, avr_loss=0.0308]
steps:   4%|▍         | 385/10016 [1:22:43<34:29:29, 12.89s/it, avr_loss=0.0308]
steps:   4%|▍         | 386/10016 [1:22:56<34:29:16, 12.89s/it, avr_loss=0.0308]
steps:   4%|▍         | 386/10016 [1:22:56<34:29:16, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 387/10016 [1:23:09<34:29:03, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 387/10016 [1:23:09<34:29:03, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 388/10016 [1:23:22<34:28:50, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 388/10016 [1:23:22<34:28:50, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 389/10016 [1:23:35<34:28:38, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 389/10016 [1:23:35<34:28:38, 12.89s/it, avr_loss=0.0306]
steps:   4%|▍         | 390/10016 [1:23:48<34:28:25, 12.89s/it, avr_loss=0.0306]
steps:   4%|▍         | 390/10016 [1:23:48<34:28:25, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 391/10016 [1:24:01<34:28:12, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 391/10016 [1:24:01<34:28:12, 12.89s/it, avr_loss=0.0306]
steps:   4%|▍         | 392/10016 [1:24:13<34:28:00, 12.89s/it, avr_loss=0.0306]
steps:   4%|▍         | 392/10016 [1:24:13<34:28:00, 12.89s/it, avr_loss=0.0306]
steps:   4%|▍         | 393/10016 [1:24:26<34:27:47, 12.89s/it, avr_loss=0.0306]
steps:   4%|▍         | 393/10016 [1:24:26<34:27:47, 12.89s/it, avr_loss=0.0306]
steps:   4%|▍         | 394/10016 [1:24:39<34:27:35, 12.89s/it, avr_loss=0.0306]
steps:   4%|▍         | 394/10016 [1:24:39<34:27:35, 12.89s/it, avr_loss=0.0306]
steps:   4%|▍         | 395/10016 [1:24:52<34:27:22, 12.89s/it, avr_loss=0.0306]
steps:   4%|▍         | 395/10016 [1:24:52<34:27:22, 12.89s/it, avr_loss=0.0308]
steps:   4%|▍         | 396/10016 [1:25:05<34:27:09, 12.89s/it, avr_loss=0.0308]
steps:   4%|▍         | 396/10016 [1:25:05<34:27:09, 12.89s/it, avr_loss=0.0308]
steps:   4%|▍         | 397/10016 [1:25:18<34:26:57, 12.89s/it, avr_loss=0.0308]
steps:   4%|▍         | 397/10016 [1:25:18<34:26:57, 12.89s/it, avr_loss=0.0308]
steps:   4%|▍         | 398/10016 [1:25:31<34:26:44, 12.89s/it, avr_loss=0.0308]
steps:   4%|▍         | 398/10016 [1:25:31<34:26:44, 12.89s/it, avr_loss=0.0308]
steps:   4%|▍         | 399/10016 [1:25:44<34:26:31, 12.89s/it, avr_loss=0.0308]
steps:   4%|▍         | 399/10016 [1:25:44<34:26:31, 12.89s/it, avr_loss=0.0308]
steps:   4%|▍         | 400/10016 [1:25:57<34:26:18, 12.89s/it, avr_loss=0.0308]
steps:   4%|▍         | 400/10016 [1:25:57<34:26:18, 12.89s/it, avr_loss=0.0309]
steps:   4%|▍         | 401/10016 [1:26:10<34:26:06, 12.89s/it, avr_loss=0.0309]
steps:   4%|▍         | 401/10016 [1:26:10<34:26:06, 12.89s/it, avr_loss=0.0308]
steps:   4%|▍         | 402/10016 [1:26:22<34:25:53, 12.89s/it, avr_loss=0.0308]
steps:   4%|▍         | 402/10016 [1:26:22<34:25:53, 12.89s/it, avr_loss=0.0308]
steps:   4%|▍         | 403/10016 [1:26:35<34:25:40, 12.89s/it, avr_loss=0.0308]
steps:   4%|▍         | 403/10016 [1:26:35<34:25:40, 12.89s/it, avr_loss=0.0308]
steps:   4%|▍         | 404/10016 [1:26:48<34:25:28, 12.89s/it, avr_loss=0.0308]
steps:   4%|▍         | 404/10016 [1:26:48<34:25:28, 12.89s/it, avr_loss=0.0309]
steps:   4%|▍         | 405/10016 [1:27:01<34:25:15, 12.89s/it, avr_loss=0.0309]
steps:   4%|▍         | 405/10016 [1:27:01<34:25:15, 12.89s/it, avr_loss=0.0308]
steps:   4%|▍         | 406/10016 [1:27:14<34:25:02, 12.89s/it, avr_loss=0.0308]
steps:   4%|▍         | 406/10016 [1:27:14<34:25:02, 12.89s/it, avr_loss=0.0308]
steps:   4%|▍         | 407/10016 [1:27:27<34:24:49, 12.89s/it, avr_loss=0.0308]
steps:   4%|▍         | 407/10016 [1:27:27<34:24:49, 12.89s/it, avr_loss=0.0308]
steps:   4%|▍         | 408/10016 [1:27:40<34:24:36, 12.89s/it, avr_loss=0.0308]
steps:   4%|▍         | 408/10016 [1:27:40<34:24:36, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 409/10016 [1:27:53<34:24:23, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 409/10016 [1:27:53<34:24:23, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 410/10016 [1:28:06<34:24:10, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 410/10016 [1:28:06<34:24:10, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 411/10016 [1:28:19<34:23:58, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 411/10016 [1:28:19<34:23:58, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 412/10016 [1:28:31<34:23:45, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 412/10016 [1:28:31<34:23:45, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 413/10016 [1:28:44<34:23:32, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 413/10016 [1:28:44<34:23:32, 12.89s/it, avr_loss=0.0308]
steps:   4%|▍         | 414/10016 [1:28:57<34:23:20, 12.89s/it, avr_loss=0.0308]
steps:   4%|▍         | 414/10016 [1:28:57<34:23:20, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 415/10016 [1:29:10<34:23:07, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 415/10016 [1:29:10<34:23:07, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 416/10016 [1:29:23<34:22:54, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 416/10016 [1:29:23<34:22:55, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 417/10016 [1:29:36<34:22:42, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 417/10016 [1:29:36<34:22:42, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 418/10016 [1:29:49<34:22:29, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 418/10016 [1:29:49<34:22:29, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 419/10016 [1:30:02<34:22:16, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 419/10016 [1:30:02<34:22:16, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 420/10016 [1:30:15<34:22:03, 12.89s/it, avr_loss=0.0307]
steps:   4%|▍         | 420/10016 [1:30:15<34:22:03, 12.89s/it, avr_loss=0.0306]
steps:   4%|▍         | 421/10016 [1:30:28<34:21:50, 12.89s/it, avr_loss=0.0306]
steps:   4%|▍         | 421/10016 [1:30:28<34:21:50, 12.89s/it, avr_loss=0.0306]
steps:   4%|▍         | 422/10016 [1:30:40<34:21:37, 12.89s/it, avr_loss=0.0306]
steps:   4%|▍         | 422/10016 [1:30:40<34:21:37, 12.89s/it, avr_loss=0.0306]
steps:   4%|▍         | 423/10016 [1:30:53<34:21:24, 12.89s/it, avr_loss=0.0306]
steps:   4%|▍         | 423/10016 [1:30:53<34:21:24, 12.89s/it, avr_loss=0.0305]
steps:   4%|▍         | 424/10016 [1:31:06<34:21:11, 12.89s/it, avr_loss=0.0305]
steps:   4%|▍         | 424/10016 [1:31:06<34:21:11, 12.89s/it, avr_loss=0.0305]
steps:   4%|▍         | 425/10016 [1:31:19<34:20:59, 12.89s/it, avr_loss=0.0305]
steps:   4%|▍         | 425/10016 [1:31:19<34:20:59, 12.89s/it, avr_loss=0.0305]
steps:   4%|▍         | 426/10016 [1:31:32<34:20:46, 12.89s/it, avr_loss=0.0305]
steps:   4%|▍         | 426/10016 [1:31:32<34:20:46, 12.89s/it, avr_loss=0.0305]
steps:   4%|▍         | 427/10016 [1:31:45<34:20:34, 12.89s/it, avr_loss=0.0305]
steps:   4%|▍         | 427/10016 [1:31:45<34:20:34, 12.89s/it, avr_loss=0.0305]
steps:   4%|▍         | 428/10016 [1:31:58<34:20:21, 12.89s/it, avr_loss=0.0305]
steps:   4%|▍         | 428/10016 [1:31:58<34:20:21, 12.89s/it, avr_loss=0.0305]
steps:   4%|▍         | 429/10016 [1:32:11<34:20:08, 12.89s/it, avr_loss=0.0305]
steps:   4%|▍         | 429/10016 [1:32:11<34:20:08, 12.89s/it, avr_loss=0.0304]
steps:   4%|▍         | 430/10016 [1:32:24<34:19:55, 12.89s/it, avr_loss=0.0304]
steps:   4%|▍         | 430/10016 [1:32:24<34:19:55, 12.89s/it, avr_loss=0.0304]
steps:   4%|▍         | 431/10016 [1:32:37<34:19:42, 12.89s/it, avr_loss=0.0304]
steps:   4%|▍         | 431/10016 [1:32:37<34:19:42, 12.89s/it, avr_loss=0.0304]
steps:   4%|▍         | 432/10016 [1:32:49<34:19:30, 12.89s/it, avr_loss=0.0304]
steps:   4%|▍         | 432/10016 [1:32:49<34:19:30, 12.89s/it, avr_loss=0.0304]
steps:   4%|▍         | 433/10016 [1:33:02<34:19:17, 12.89s/it, avr_loss=0.0304]
steps:   4%|▍         | 433/10016 [1:33:02<34:19:17, 12.89s/it, avr_loss=0.0304]
steps:   4%|▍         | 434/10016 [1:33:15<34:19:04, 12.89s/it, avr_loss=0.0304]
steps:   4%|▍         | 434/10016 [1:33:15<34:19:04, 12.89s/it, avr_loss=0.0304]
steps:   4%|▍         | 435/10016 [1:33:28<34:18:52, 12.89s/it, avr_loss=0.0304]
steps:   4%|▍         | 435/10016 [1:33:28<34:18:52, 12.89s/it, avr_loss=0.0303]
steps:   4%|▍         | 436/10016 [1:33:41<34:18:39, 12.89s/it, avr_loss=0.0303]
steps:   4%|▍         | 436/10016 [1:33:41<34:18:39, 12.89s/it, avr_loss=0.0303]
steps:   4%|▍         | 437/10016 [1:33:54<34:18:26, 12.89s/it, avr_loss=0.0303]
steps:   4%|▍         | 437/10016 [1:33:54<34:18:26, 12.89s/it, avr_loss=0.0303]
steps:   4%|▍         | 438/10016 [1:34:07<34:18:14, 12.89s/it, avr_loss=0.0303]
steps:   4%|▍         | 438/10016 [1:34:07<34:18:14, 12.89s/it, avr_loss=0.0303]
steps:   4%|▍         | 439/10016 [1:34:20<34:18:01, 12.89s/it, avr_loss=0.0303]
steps:   4%|▍         | 439/10016 [1:34:20<34:18:01, 12.89s/it, avr_loss=0.0303]
steps:   4%|▍         | 440/10016 [1:34:33<34:17:48, 12.89s/it, avr_loss=0.0303]
steps:   4%|▍         | 440/10016 [1:34:33<34:17:48, 12.89s/it, avr_loss=0.0303]
steps:   4%|▍         | 441/10016 [1:34:46<34:17:35, 12.89s/it, avr_loss=0.0303]
steps:   4%|▍         | 441/10016 [1:34:46<34:17:35, 12.89s/it, avr_loss=0.0303]
steps:   4%|▍         | 442/10016 [1:34:58<34:17:23, 12.89s/it, avr_loss=0.0303]
steps:   4%|▍         | 442/10016 [1:34:58<34:17:23, 12.89s/it, avr_loss=0.0303]
steps:   4%|▍         | 443/10016 [1:35:11<34:17:10, 12.89s/it, avr_loss=0.0303]
steps:   4%|▍         | 443/10016 [1:35:11<34:17:10, 12.89s/it, avr_loss=0.0302]
steps:   4%|▍         | 444/10016 [1:35:24<34:16:57, 12.89s/it, avr_loss=0.0302]
steps:   4%|▍         | 444/10016 [1:35:24<34:16:57, 12.89s/it, avr_loss=0.0302]
steps:   4%|▍         | 445/10016 [1:35:37<34:16:45, 12.89s/it, avr_loss=0.0302]
steps:   4%|▍         | 445/10016 [1:35:37<34:16:45, 12.89s/it, avr_loss=0.0302]
steps:   4%|▍         | 446/10016 [1:35:50<34:16:35, 12.89s/it, avr_loss=0.0302]
steps:   4%|▍         | 446/10016 [1:35:50<34:16:35, 12.89s/it, avr_loss=0.0302]
steps:   4%|▍         | 447/10016 [1:36:03<34:16:22, 12.89s/it, avr_loss=0.0302]
steps:   4%|▍         | 447/10016 [1:36:03<34:16:22, 12.89s/it, avr_loss=0.0302]
steps:   4%|▍         | 448/10016 [1:36:16<34:16:09, 12.89s/it, avr_loss=0.0302]
steps:   4%|▍         | 448/10016 [1:36:16<34:16:09, 12.89s/it, avr_loss=0.0302]
steps:   4%|▍         | 449/10016 [1:36:29<34:15:56, 12.89s/it, avr_loss=0.0302]
steps:   4%|▍         | 449/10016 [1:36:29<34:15:56, 12.89s/it, avr_loss=0.0301]
steps:   4%|▍         | 450/10016 [1:36:42<34:15:44, 12.89s/it, avr_loss=0.0301]
steps:   4%|▍         | 450/10016 [1:36:42<34:15:44, 12.89s/it, avr_loss=0.0301]
steps:   5%|▍         | 451/10016 [1:36:55<34:15:31, 12.89s/it, avr_loss=0.0301]
steps:   5%|▍         | 451/10016 [1:36:55<34:15:31, 12.89s/it, avr_loss=0.0302]
steps:   5%|▍         | 452/10016 [1:37:08<34:15:19, 12.89s/it, avr_loss=0.0302]
steps:   5%|▍         | 452/10016 [1:37:08<34:15:19, 12.89s/it, avr_loss=0.0303]
steps:   5%|▍         | 453/10016 [1:37:21<34:15:06, 12.89s/it, avr_loss=0.0303]
steps:   5%|▍         | 453/10016 [1:37:21<34:15:06, 12.89s/it, avr_loss=0.0302]
steps:   5%|▍         | 454/10016 [1:37:33<34:14:53, 12.89s/it, avr_loss=0.0302]
steps:   5%|▍         | 454/10016 [1:37:33<34:14:53, 12.89s/it, avr_loss=0.0303]
steps:   5%|▍         | 455/10016 [1:37:46<34:14:40, 12.89s/it, avr_loss=0.0303]
steps:   5%|▍         | 455/10016 [1:37:46<34:14:40, 12.89s/it, avr_loss=0.0303]
steps:   5%|▍         | 456/10016 [1:37:59<34:14:28, 12.89s/it, avr_loss=0.0303]
steps:   5%|▍         | 456/10016 [1:37:59<34:14:28, 12.89s/it, avr_loss=0.0303]
steps:   5%|▍         | 457/10016 [1:38:12<34:14:15, 12.89s/it, avr_loss=0.0303]
steps:   5%|▍         | 457/10016 [1:38:12<34:14:15, 12.89s/it, avr_loss=0.0303]
steps:   5%|▍         | 458/10016 [1:38:25<34:14:02, 12.89s/it, avr_loss=0.0303]
steps:   5%|▍         | 458/10016 [1:38:25<34:14:02, 12.89s/it, avr_loss=0.0303]
steps:   5%|▍         | 459/10016 [1:38:38<34:13:49, 12.89s/it, avr_loss=0.0303]
steps:   5%|▍         | 459/10016 [1:38:38<34:13:49, 12.89s/it, avr_loss=0.0303]
steps:   5%|▍         | 460/10016 [1:38:51<34:13:36, 12.89s/it, avr_loss=0.0303]
steps:   5%|▍         | 460/10016 [1:38:51<34:13:36, 12.89s/it, avr_loss=0.0303]
steps:   5%|▍         | 461/10016 [1:39:04<34:13:24, 12.89s/it, avr_loss=0.0303]
steps:   5%|▍         | 461/10016 [1:39:04<34:13:24, 12.89s/it, avr_loss=0.0303]
steps:   5%|▍         | 462/10016 [1:39:17<34:13:11, 12.89s/it, avr_loss=0.0303]
steps:   5%|▍         | 462/10016 [1:39:17<34:13:11, 12.89s/it, avr_loss=0.0303]
steps:   5%|▍         | 463/10016 [1:39:30<34:12:58, 12.89s/it, avr_loss=0.0303]
steps:   5%|▍         | 463/10016 [1:39:30<34:12:58, 12.89s/it, avr_loss=0.0303]
steps:   5%|▍         | 464/10016 [1:39:42<34:12:45, 12.89s/it, avr_loss=0.0303]
steps:   5%|▍         | 464/10016 [1:39:42<34:12:45, 12.89s/it, avr_loss=0.0302]
steps:   5%|▍         | 465/10016 [1:39:55<34:12:32, 12.89s/it, avr_loss=0.0302]
steps:   5%|▍         | 465/10016 [1:39:55<34:12:32, 12.89s/it, avr_loss=0.0302]
steps:   5%|▍         | 466/10016 [1:40:08<34:12:19, 12.89s/it, avr_loss=0.0302]
steps:   5%|▍         | 466/10016 [1:40:08<34:12:19, 12.89s/it, avr_loss=0.0302]
steps:   5%|▍         | 467/10016 [1:40:21<34:12:06, 12.89s/it, avr_loss=0.0302]
steps:   5%|▍         | 467/10016 [1:40:21<34:12:06, 12.89s/it, avr_loss=0.0302]
steps:   5%|▍         | 468/10016 [1:40:34<34:11:53, 12.89s/it, avr_loss=0.0302]
steps:   5%|▍         | 468/10016 [1:40:34<34:11:53, 12.89s/it, avr_loss=0.0302]
steps:   5%|▍         | 469/10016 [1:40:47<34:11:40, 12.89s/it, avr_loss=0.0302]
steps:   5%|▍         | 469/10016 [1:40:47<34:11:40, 12.89s/it, avr_loss=0.0301]
steps:   5%|▍         | 470/10016 [1:41:00<34:11:27, 12.89s/it, avr_loss=0.0301]
steps:   5%|▍         | 470/10016 [1:41:00<34:11:27, 12.89s/it, avr_loss=0.0301]
steps:   5%|▍         | 471/10016 [1:41:13<34:11:15, 12.89s/it, avr_loss=0.0301]
steps:   5%|▍         | 471/10016 [1:41:13<34:11:15, 12.89s/it, avr_loss=0.0301]
steps:   5%|▍         | 472/10016 [1:41:26<34:11:02, 12.89s/it, avr_loss=0.0301]
steps:   5%|▍         | 472/10016 [1:41:26<34:11:02, 12.89s/it, avr_loss=0.0301]
steps:   5%|▍         | 473/10016 [1:41:38<34:10:49, 12.89s/it, avr_loss=0.0301]
steps:   5%|▍         | 473/10016 [1:41:38<34:10:49, 12.89s/it, avr_loss=0.0301]
steps:   5%|▍         | 474/10016 [1:41:51<34:10:36, 12.89s/it, avr_loss=0.0301]
steps:   5%|▍         | 474/10016 [1:41:51<34:10:36, 12.89s/it, avr_loss=0.0301]
steps:   5%|▍         | 475/10016 [1:42:04<34:10:24, 12.89s/it, avr_loss=0.0301]
steps:   5%|▍         | 475/10016 [1:42:04<34:10:24, 12.89s/it, avr_loss=0.03]  
steps:   5%|▍         | 476/10016 [1:42:17<34:10:11, 12.89s/it, avr_loss=0.03]
steps:   5%|▍         | 476/10016 [1:42:17<34:10:11, 12.89s/it, avr_loss=0.0301]
steps:   5%|▍         | 477/10016 [1:42:30<34:09:58, 12.89s/it, avr_loss=0.0301]
steps:   5%|▍         | 477/10016 [1:42:30<34:09:58, 12.89s/it, avr_loss=0.03]  
steps:   5%|▍         | 478/10016 [1:42:43<34:09:45, 12.89s/it, avr_loss=0.03]
steps:   5%|▍         | 478/10016 [1:42:43<34:09:45, 12.89s/it, avr_loss=0.03]
steps:   5%|▍         | 479/10016 [1:42:56<34:09:32, 12.89s/it, avr_loss=0.03]
steps:   5%|▍         | 479/10016 [1:42:56<34:09:32, 12.89s/it, avr_loss=0.03]
steps:   5%|▍         | 480/10016 [1:43:09<34:09:19, 12.89s/it, avr_loss=0.03]
steps:   5%|▍         | 480/10016 [1:43:09<34:09:19, 12.89s/it, avr_loss=0.03]
steps:   5%|▍         | 481/10016 [1:43:22<34:09:06, 12.89s/it, avr_loss=0.03]
steps:   5%|▍         | 481/10016 [1:43:22<34:09:06, 12.89s/it, avr_loss=0.03]
steps:   5%|▍         | 482/10016 [1:43:35<34:08:53, 12.89s/it, avr_loss=0.03]
steps:   5%|▍         | 482/10016 [1:43:35<34:08:53, 12.89s/it, avr_loss=0.03]
steps:   5%|▍         | 483/10016 [1:43:47<34:08:41, 12.89s/it, avr_loss=0.03]
steps:   5%|▍         | 483/10016 [1:43:47<34:08:41, 12.89s/it, avr_loss=0.03]
steps:   5%|▍         | 484/10016 [1:44:00<34:08:28, 12.89s/it, avr_loss=0.03]
steps:   5%|▍         | 484/10016 [1:44:00<34:08:28, 12.89s/it, avr_loss=0.0299]
steps:   5%|▍         | 485/10016 [1:44:13<34:08:15, 12.89s/it, avr_loss=0.0299]
steps:   5%|▍         | 485/10016 [1:44:13<34:08:15, 12.89s/it, avr_loss=0.0299]
steps:   5%|▍         | 486/10016 [1:44:26<34:08:02, 12.89s/it, avr_loss=0.0299]
steps:   5%|▍         | 486/10016 [1:44:26<34:08:02, 12.89s/it, avr_loss=0.0299]
steps:   5%|▍         | 487/10016 [1:44:39<34:07:50, 12.89s/it, avr_loss=0.0299]
steps:   5%|▍         | 487/10016 [1:44:39<34:07:50, 12.89s/it, avr_loss=0.0298]
steps:   5%|▍         | 488/10016 [1:44:52<34:07:37, 12.89s/it, avr_loss=0.0298]
steps:   5%|▍         | 488/10016 [1:44:52<34:07:37, 12.89s/it, avr_loss=0.0298]
steps:   5%|▍         | 489/10016 [1:45:05<34:07:24, 12.89s/it, avr_loss=0.0298]
steps:   5%|▍         | 489/10016 [1:45:05<34:07:24, 12.89s/it, avr_loss=0.0299]
steps:   5%|▍         | 490/10016 [1:45:18<34:07:11, 12.89s/it, avr_loss=0.0299]
steps:   5%|▍         | 490/10016 [1:45:18<34:07:11, 12.89s/it, avr_loss=0.0298]
steps:   5%|▍         | 491/10016 [1:45:31<34:06:59, 12.89s/it, avr_loss=0.0298]
steps:   5%|▍         | 491/10016 [1:45:31<34:06:59, 12.89s/it, avr_loss=0.0298]
steps:   5%|▍         | 492/10016 [1:45:44<34:06:46, 12.89s/it, avr_loss=0.0298]
steps:   5%|▍         | 492/10016 [1:45:44<34:06:46, 12.89s/it, avr_loss=0.0298]
steps:   5%|▍         | 493/10016 [1:45:56<34:06:33, 12.89s/it, avr_loss=0.0298]
steps:   5%|▍         | 493/10016 [1:45:56<34:06:33, 12.89s/it, avr_loss=0.0298]
steps:   5%|▍         | 494/10016 [1:46:09<34:06:20, 12.89s/it, avr_loss=0.0298]
steps:   5%|▍         | 494/10016 [1:46:09<34:06:20, 12.89s/it, avr_loss=0.0298]
steps:   5%|▍         | 495/10016 [1:46:22<34:06:07, 12.89s/it, avr_loss=0.0298]
steps:   5%|▍         | 495/10016 [1:46:22<34:06:07, 12.89s/it, avr_loss=0.0298]
steps:   5%|▍         | 496/10016 [1:46:35<34:05:54, 12.89s/it, avr_loss=0.0298]
steps:   5%|▍         | 496/10016 [1:46:35<34:05:54, 12.89s/it, avr_loss=0.0298]
steps:   5%|▍         | 497/10016 [1:46:48<34:05:41, 12.89s/it, avr_loss=0.0298]
steps:   5%|▍         | 497/10016 [1:46:48<34:05:41, 12.89s/it, avr_loss=0.0298]
steps:   5%|▍         | 498/10016 [1:47:01<34:05:28, 12.89s/it, avr_loss=0.0298]
steps:   5%|▍         | 498/10016 [1:47:01<34:05:28, 12.89s/it, avr_loss=0.0298]
steps:   5%|▍         | 499/10016 [1:47:14<34:05:14, 12.89s/it, avr_loss=0.0298]
steps:   5%|▍         | 499/10016 [1:47:14<34:05:14, 12.89s/it, avr_loss=0.0298]
steps:   5%|▍         | 500/10016 [1:47:27<34:05:01, 12.89s/it, avr_loss=0.0298]
steps:   5%|▍         | 500/10016 [1:47:27<34:05:01, 12.89s/it, avr_loss=0.0298]
steps:   5%|▌         | 501/10016 [1:47:40<34:04:49, 12.89s/it, avr_loss=0.0298]
steps:   5%|▌         | 501/10016 [1:47:40<34:04:49, 12.89s/it, avr_loss=0.0298]
steps:   5%|▌         | 502/10016 [1:47:52<34:04:36, 12.89s/it, avr_loss=0.0298]
steps:   5%|▌         | 502/10016 [1:47:52<34:04:36, 12.89s/it, avr_loss=0.0297]
steps:   5%|▌         | 503/10016 [1:48:05<34:04:23, 12.89s/it, avr_loss=0.0297]
steps:   5%|▌         | 503/10016 [1:48:05<34:04:23, 12.89s/it, avr_loss=0.0297]
steps:   5%|▌         | 504/10016 [1:48:18<34:04:10, 12.89s/it, avr_loss=0.0297]
steps:   5%|▌         | 504/10016 [1:48:18<34:04:10, 12.89s/it, avr_loss=0.0297]
steps:   5%|▌         | 505/10016 [1:48:31<34:03:57, 12.89s/it, avr_loss=0.0297]
steps:   5%|▌         | 505/10016 [1:48:31<34:03:57, 12.89s/it, avr_loss=0.0297]
steps:   5%|▌         | 506/10016 [1:48:44<34:03:44, 12.89s/it, avr_loss=0.0297]
steps:   5%|▌         | 506/10016 [1:48:44<34:03:44, 12.89s/it, avr_loss=0.0297]
steps:   5%|▌         | 507/10016 [1:48:57<34:03:31, 12.89s/it, avr_loss=0.0297]
steps:   5%|▌         | 507/10016 [1:48:57<34:03:31, 12.89s/it, avr_loss=0.0298]
steps:   5%|▌         | 508/10016 [1:49:10<34:03:18, 12.89s/it, avr_loss=0.0298]
steps:   5%|▌         | 508/10016 [1:49:10<34:03:18, 12.89s/it, avr_loss=0.0298]
steps:   5%|▌         | 509/10016 [1:49:23<34:03:05, 12.89s/it, avr_loss=0.0298]
steps:   5%|▌         | 509/10016 [1:49:23<34:03:05, 12.89s/it, avr_loss=0.0297]
steps:   5%|▌         | 510/10016 [1:49:36<34:02:52, 12.89s/it, avr_loss=0.0297]
steps:   5%|▌         | 510/10016 [1:49:36<34:02:52, 12.89s/it, avr_loss=0.0297]
steps:   5%|▌         | 511/10016 [1:49:48<34:02:39, 12.89s/it, avr_loss=0.0297]
steps:   5%|▌         | 511/10016 [1:49:48<34:02:39, 12.89s/it, avr_loss=0.0297]
steps:   5%|▌         | 512/10016 [1:50:01<34:02:27, 12.89s/it, avr_loss=0.0297]
steps:   5%|▌         | 512/10016 [1:50:01<34:02:27, 12.89s/it, avr_loss=0.0297]
steps:   5%|▌         | 513/10016 [1:50:14<34:02:14, 12.89s/it, avr_loss=0.0297]
steps:   5%|▌         | 513/10016 [1:50:14<34:02:14, 12.89s/it, avr_loss=0.0297]
steps:   5%|▌         | 514/10016 [1:50:27<34:02:01, 12.89s/it, avr_loss=0.0297]
steps:   5%|▌         | 514/10016 [1:50:27<34:02:01, 12.89s/it, avr_loss=0.0297]
steps:   5%|▌         | 515/10016 [1:50:40<34:01:48, 12.89s/it, avr_loss=0.0297]
steps:   5%|▌         | 515/10016 [1:50:40<34:01:48, 12.89s/it, avr_loss=0.0297]
steps:   5%|▌         | 516/10016 [1:50:53<34:01:35, 12.89s/it, avr_loss=0.0297]
steps:   5%|▌         | 516/10016 [1:50:53<34:01:35, 12.89s/it, avr_loss=0.0297]
steps:   5%|▌         | 517/10016 [1:51:06<34:01:22, 12.89s/it, avr_loss=0.0297]
steps:   5%|▌         | 517/10016 [1:51:06<34:01:22, 12.89s/it, avr_loss=0.0297]
steps:   5%|▌         | 518/10016 [1:51:19<34:01:09, 12.89s/it, avr_loss=0.0297]
steps:   5%|▌         | 518/10016 [1:51:19<34:01:09, 12.89s/it, avr_loss=0.0297]
steps:   5%|▌         | 519/10016 [1:51:32<34:00:56, 12.89s/it, avr_loss=0.0297]
steps:   5%|▌         | 519/10016 [1:51:32<34:00:56, 12.89s/it, avr_loss=0.0296]
steps:   5%|▌         | 520/10016 [1:51:45<34:00:43, 12.89s/it, avr_loss=0.0296]
steps:   5%|▌         | 520/10016 [1:51:45<34:00:43, 12.89s/it, avr_loss=0.0296]
steps:   5%|▌         | 521/10016 [1:51:57<34:00:30, 12.89s/it, avr_loss=0.0296]
steps:   5%|▌         | 521/10016 [1:51:57<34:00:30, 12.89s/it, avr_loss=0.0296]
steps:   5%|▌         | 522/10016 [1:52:10<34:00:17, 12.89s/it, avr_loss=0.0296]
steps:   5%|▌         | 522/10016 [1:52:10<34:00:17, 12.89s/it, avr_loss=0.0296]
steps:   5%|▌         | 523/10016 [1:52:23<34:00:05, 12.89s/it, avr_loss=0.0296]
steps:   5%|▌         | 523/10016 [1:52:23<34:00:05, 12.89s/it, avr_loss=0.0296]
steps:   5%|▌         | 524/10016 [1:52:36<33:59:52, 12.89s/it, avr_loss=0.0296]
steps:   5%|▌         | 524/10016 [1:52:36<33:59:52, 12.89s/it, avr_loss=0.0295]
steps:   5%|▌         | 525/10016 [1:52:49<33:59:39, 12.89s/it, avr_loss=0.0295]
steps:   5%|▌         | 525/10016 [1:52:49<33:59:39, 12.89s/it, avr_loss=0.0295]
steps:   5%|▌         | 526/10016 [1:53:02<33:59:26, 12.89s/it, avr_loss=0.0295]
steps:   5%|▌         | 526/10016 [1:53:02<33:59:26, 12.89s/it, avr_loss=0.0295]
steps:   5%|▌         | 527/10016 [1:53:15<33:59:14, 12.89s/it, avr_loss=0.0295]
steps:   5%|▌         | 527/10016 [1:53:15<33:59:14, 12.89s/it, avr_loss=0.0295]
steps:   5%|▌         | 528/10016 [1:53:28<33:59:01, 12.89s/it, avr_loss=0.0295]
steps:   5%|▌         | 528/10016 [1:53:28<33:59:01, 12.89s/it, avr_loss=0.0295]
steps:   5%|▌         | 529/10016 [1:53:41<33:58:48, 12.89s/it, avr_loss=0.0295]
steps:   5%|▌         | 529/10016 [1:53:41<33:58:48, 12.89s/it, avr_loss=0.0295]
steps:   5%|▌         | 530/10016 [1:53:53<33:58:35, 12.89s/it, avr_loss=0.0295]
steps:   5%|▌         | 530/10016 [1:53:53<33:58:35, 12.89s/it, avr_loss=0.0294]
steps:   5%|▌         | 531/10016 [1:54:06<33:58:22, 12.89s/it, avr_loss=0.0294]
steps:   5%|▌         | 531/10016 [1:54:06<33:58:22, 12.89s/it, avr_loss=0.0295]
steps:   5%|▌         | 532/10016 [1:54:19<33:58:09, 12.89s/it, avr_loss=0.0295]
steps:   5%|▌         | 532/10016 [1:54:19<33:58:09, 12.89s/it, avr_loss=0.0294]
steps:   5%|▌         | 533/10016 [1:54:32<33:57:57, 12.89s/it, avr_loss=0.0294]
steps:   5%|▌         | 533/10016 [1:54:32<33:57:57, 12.89s/it, avr_loss=0.0294]
steps:   5%|▌         | 534/10016 [1:54:45<33:57:44, 12.89s/it, avr_loss=0.0294]
steps:   5%|▌         | 534/10016 [1:54:45<33:57:44, 12.89s/it, avr_loss=0.0294]
steps:   5%|▌         | 535/10016 [1:54:58<33:57:31, 12.89s/it, avr_loss=0.0294]
steps:   5%|▌         | 535/10016 [1:54:58<33:57:31, 12.89s/it, avr_loss=0.0294]
steps:   5%|▌         | 536/10016 [1:55:11<33:57:18, 12.89s/it, avr_loss=0.0294]
steps:   5%|▌         | 536/10016 [1:55:11<33:57:18, 12.89s/it, avr_loss=0.0294]
steps:   5%|▌         | 537/10016 [1:55:24<33:57:06, 12.89s/it, avr_loss=0.0294]
steps:   5%|▌         | 537/10016 [1:55:24<33:57:06, 12.89s/it, avr_loss=0.0294]
steps:   5%|▌         | 538/10016 [1:55:37<33:56:53, 12.89s/it, avr_loss=0.0294]
steps:   5%|▌         | 538/10016 [1:55:37<33:56:53, 12.89s/it, avr_loss=0.0293]
steps:   5%|▌         | 539/10016 [1:55:50<33:56:40, 12.89s/it, avr_loss=0.0293]
steps:   5%|▌         | 539/10016 [1:55:50<33:56:40, 12.89s/it, avr_loss=0.0294]
steps:   5%|▌         | 540/10016 [1:56:03<33:56:27, 12.89s/it, avr_loss=0.0294]
steps:   5%|▌         | 540/10016 [1:56:03<33:56:27, 12.89s/it, avr_loss=0.0293]
steps:   5%|▌         | 541/10016 [1:56:15<33:56:15, 12.89s/it, avr_loss=0.0293]
steps:   5%|▌         | 541/10016 [1:56:15<33:56:15, 12.89s/it, avr_loss=0.0294]
steps:   5%|▌         | 542/10016 [1:56:28<33:56:02, 12.89s/it, avr_loss=0.0294]
steps:   5%|▌         | 542/10016 [1:56:28<33:56:02, 12.89s/it, avr_loss=0.0295]
steps:   5%|▌         | 543/10016 [1:56:41<33:55:49, 12.89s/it, avr_loss=0.0295]
steps:   5%|▌         | 543/10016 [1:56:41<33:55:49, 12.89s/it, avr_loss=0.0294]
steps:   5%|▌         | 544/10016 [1:56:54<33:55:36, 12.89s/it, avr_loss=0.0294]
steps:   5%|▌         | 544/10016 [1:56:54<33:55:36, 12.89s/it, avr_loss=0.0294]
steps:   5%|▌         | 545/10016 [1:57:07<33:55:24, 12.89s/it, avr_loss=0.0294]
steps:   5%|▌         | 545/10016 [1:57:07<33:55:24, 12.89s/it, avr_loss=0.0294]
steps:   5%|▌         | 546/10016 [1:57:20<33:55:11, 12.89s/it, avr_loss=0.0294]
steps:   5%|▌         | 546/10016 [1:57:20<33:55:11, 12.89s/it, avr_loss=0.0294]
steps:   5%|▌         | 547/10016 [1:57:33<33:54:58, 12.89s/it, avr_loss=0.0294]
steps:   5%|▌         | 547/10016 [1:57:33<33:54:58, 12.89s/it, avr_loss=0.0294]
steps:   5%|▌         | 548/10016 [1:57:46<33:54:46, 12.89s/it, avr_loss=0.0294]
steps:   5%|▌         | 548/10016 [1:57:46<33:54:46, 12.89s/it, avr_loss=0.0294]
steps:   5%|▌         | 549/10016 [1:57:59<33:54:33, 12.89s/it, avr_loss=0.0294]
steps:   5%|▌         | 549/10016 [1:57:59<33:54:33, 12.89s/it, avr_loss=0.0294]
steps:   5%|▌         | 550/10016 [1:58:12<33:54:20, 12.89s/it, avr_loss=0.0294]
steps:   5%|▌         | 550/10016 [1:58:12<33:54:20, 12.89s/it, avr_loss=0.0294]
steps:   6%|▌         | 551/10016 [1:58:24<33:54:07, 12.89s/it, avr_loss=0.0294]
steps:   6%|▌         | 551/10016 [1:58:24<33:54:07, 12.89s/it, avr_loss=0.0294]
steps:   6%|▌         | 552/10016 [1:58:37<33:53:54, 12.89s/it, avr_loss=0.0294]
steps:   6%|▌         | 552/10016 [1:58:37<33:53:54, 12.89s/it, avr_loss=0.0294]
steps:   6%|▌         | 553/10016 [1:58:50<33:53:41, 12.89s/it, avr_loss=0.0294]
steps:   6%|▌         | 553/10016 [1:58:50<33:53:41, 12.89s/it, avr_loss=0.0294]
steps:   6%|▌         | 554/10016 [1:59:03<33:53:28, 12.89s/it, avr_loss=0.0294]
steps:   6%|▌         | 554/10016 [1:59:03<33:53:28, 12.89s/it, avr_loss=0.0294]
steps:   6%|▌         | 555/10016 [1:59:16<33:53:18, 12.89s/it, avr_loss=0.0294]
steps:   6%|▌         | 555/10016 [1:59:16<33:53:18, 12.89s/it, avr_loss=0.0294]
steps:   6%|▌         | 556/10016 [1:59:29<33:53:05, 12.89s/it, avr_loss=0.0294]
steps:   6%|▌         | 556/10016 [1:59:29<33:53:05, 12.89s/it, avr_loss=0.0294]
steps:   6%|▌         | 557/10016 [1:59:42<33:52:52, 12.89s/it, avr_loss=0.0294]
steps:   6%|▌         | 557/10016 [1:59:42<33:52:52, 12.89s/it, avr_loss=0.0293]
steps:   6%|▌         | 558/10016 [1:59:55<33:52:39, 12.89s/it, avr_loss=0.0293]
steps:   6%|▌         | 558/10016 [1:59:55<33:52:39, 12.89s/it, avr_loss=0.0293]
steps:   6%|▌         | 559/10016 [2:00:08<33:52:27, 12.89s/it, avr_loss=0.0293]
steps:   6%|▌         | 559/10016 [2:00:08<33:52:27, 12.89s/it, avr_loss=0.0293]
steps:   6%|▌         | 560/10016 [2:00:21<33:52:14, 12.89s/it, avr_loss=0.0293]
steps:   6%|▌         | 560/10016 [2:00:21<33:52:14, 12.89s/it, avr_loss=0.0293]
steps:   6%|▌         | 561/10016 [2:00:34<33:52:02, 12.89s/it, avr_loss=0.0293]
steps:   6%|▌         | 561/10016 [2:00:34<33:52:02, 12.89s/it, avr_loss=0.0293]
steps:   6%|▌         | 562/10016 [2:00:47<33:51:49, 12.90s/it, avr_loss=0.0293]
steps:   6%|▌         | 562/10016 [2:00:47<33:51:49, 12.90s/it, avr_loss=0.0292]
steps:   6%|▌         | 563/10016 [2:00:59<33:51:36, 12.90s/it, avr_loss=0.0292]
steps:   6%|▌         | 563/10016 [2:00:59<33:51:36, 12.90s/it, avr_loss=0.0292]
steps:   6%|▌         | 564/10016 [2:01:12<33:51:24, 12.90s/it, avr_loss=0.0292]
steps:   6%|▌         | 564/10016 [2:01:12<33:51:24, 12.90s/it, avr_loss=0.0292]
steps:   6%|▌         | 565/10016 [2:01:25<33:51:11, 12.90s/it, avr_loss=0.0292]
steps:   6%|▌         | 565/10016 [2:01:25<33:51:11, 12.90s/it, avr_loss=0.0292]
steps:   6%|▌         | 566/10016 [2:01:38<33:50:58, 12.90s/it, avr_loss=0.0292]
steps:   6%|▌         | 566/10016 [2:01:38<33:50:58, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 567/10016 [2:01:51<33:50:46, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 567/10016 [2:01:51<33:50:46, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 568/10016 [2:02:04<33:50:33, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 568/10016 [2:02:04<33:50:33, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 569/10016 [2:02:17<33:50:20, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 569/10016 [2:02:17<33:50:20, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 570/10016 [2:02:30<33:50:07, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 570/10016 [2:02:30<33:50:07, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 571/10016 [2:02:43<33:49:55, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 571/10016 [2:02:43<33:49:55, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 572/10016 [2:02:56<33:49:42, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 572/10016 [2:02:56<33:49:42, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 573/10016 [2:03:08<33:49:29, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 573/10016 [2:03:08<33:49:29, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 574/10016 [2:03:21<33:49:17, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 574/10016 [2:03:21<33:49:17, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 575/10016 [2:03:34<33:49:04, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 575/10016 [2:03:34<33:49:04, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 576/10016 [2:03:47<33:48:51, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 576/10016 [2:03:47<33:48:51, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 577/10016 [2:04:00<33:48:38, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 577/10016 [2:04:00<33:48:38, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 578/10016 [2:04:13<33:48:26, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 578/10016 [2:04:13<33:48:26, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 579/10016 [2:04:26<33:48:13, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 579/10016 [2:04:26<33:48:13, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 580/10016 [2:04:39<33:48:00, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 580/10016 [2:04:39<33:48:00, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 581/10016 [2:04:52<33:47:47, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 581/10016 [2:04:52<33:47:47, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 582/10016 [2:05:05<33:47:35, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 582/10016 [2:05:05<33:47:35, 12.90s/it, avr_loss=0.029] 
steps:   6%|▌         | 583/10016 [2:05:18<33:47:22, 12.90s/it, avr_loss=0.029]
steps:   6%|▌         | 583/10016 [2:05:18<33:47:22, 12.90s/it, avr_loss=0.029]
steps:   6%|▌         | 584/10016 [2:05:30<33:47:09, 12.90s/it, avr_loss=0.029]
steps:   6%|▌         | 584/10016 [2:05:30<33:47:09, 12.90s/it, avr_loss=0.029]
steps:   6%|▌         | 585/10016 [2:05:43<33:46:56, 12.90s/it, avr_loss=0.029]
steps:   6%|▌         | 585/10016 [2:05:43<33:46:56, 12.90s/it, avr_loss=0.029]
steps:   6%|▌         | 586/10016 [2:05:56<33:46:44, 12.90s/it, avr_loss=0.029]
steps:   6%|▌         | 586/10016 [2:05:56<33:46:44, 12.90s/it, avr_loss=0.0292]
steps:   6%|▌         | 587/10016 [2:06:09<33:46:31, 12.90s/it, avr_loss=0.0292]
steps:   6%|▌         | 587/10016 [2:06:09<33:46:31, 12.90s/it, avr_loss=0.0292]
steps:   6%|▌         | 588/10016 [2:06:22<33:46:18, 12.90s/it, avr_loss=0.0292]
steps:   6%|▌         | 588/10016 [2:06:22<33:46:18, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 589/10016 [2:06:35<33:46:05, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 589/10016 [2:06:35<33:46:05, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 590/10016 [2:06:48<33:45:52, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 590/10016 [2:06:48<33:45:52, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 591/10016 [2:07:01<33:45:39, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 591/10016 [2:07:01<33:45:39, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 592/10016 [2:07:14<33:45:27, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 592/10016 [2:07:14<33:45:27, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 593/10016 [2:07:27<33:45:14, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 593/10016 [2:07:27<33:45:14, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 594/10016 [2:07:39<33:45:01, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 594/10016 [2:07:39<33:45:01, 12.90s/it, avr_loss=0.0292]
steps:   6%|▌         | 595/10016 [2:07:52<33:44:49, 12.90s/it, avr_loss=0.0292]
steps:   6%|▌         | 595/10016 [2:07:52<33:44:49, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 596/10016 [2:08:05<33:44:36, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 596/10016 [2:08:05<33:44:36, 12.90s/it, avr_loss=0.0292]
steps:   6%|▌         | 597/10016 [2:08:18<33:44:23, 12.90s/it, avr_loss=0.0292]
steps:   6%|▌         | 597/10016 [2:08:18<33:44:23, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 598/10016 [2:08:31<33:44:10, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 598/10016 [2:08:31<33:44:10, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 599/10016 [2:08:44<33:43:58, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 599/10016 [2:08:44<33:43:58, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 600/10016 [2:08:57<33:43:45, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 600/10016 [2:08:57<33:43:45, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 601/10016 [2:09:10<33:43:32, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 601/10016 [2:09:10<33:43:32, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 602/10016 [2:09:23<33:43:19, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 602/10016 [2:09:23<33:43:19, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 603/10016 [2:09:36<33:43:06, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 603/10016 [2:09:36<33:43:06, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 604/10016 [2:09:48<33:42:53, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 604/10016 [2:09:48<33:42:53, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 605/10016 [2:10:01<33:42:40, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 605/10016 [2:10:01<33:42:40, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 606/10016 [2:10:14<33:42:28, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 606/10016 [2:10:14<33:42:28, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 607/10016 [2:10:27<33:42:15, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 607/10016 [2:10:27<33:42:15, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 608/10016 [2:10:40<33:42:02, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 608/10016 [2:10:40<33:42:02, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 609/10016 [2:10:53<33:41:49, 12.90s/it, avr_loss=0.0291]
steps:   6%|▌         | 609/10016 [2:10:53<33:41:49, 12.90s/it, avr_loss=0.029] 
steps:   6%|▌         | 610/10016 [2:11:06<33:41:37, 12.90s/it, avr_loss=0.029]
steps:   6%|▌         | 610/10016 [2:11:06<33:41:37, 12.90s/it, avr_loss=0.029]
steps:   6%|▌         | 611/10016 [2:11:19<33:41:24, 12.90s/it, avr_loss=0.029]
steps:   6%|▌         | 611/10016 [2:11:19<33:41:24, 12.90s/it, avr_loss=0.029]
steps:   6%|▌         | 612/10016 [2:11:32<33:41:11, 12.90s/it, avr_loss=0.029]
steps:   6%|▌         | 612/10016 [2:11:32<33:41:11, 12.90s/it, avr_loss=0.029]
steps:   6%|▌         | 613/10016 [2:11:45<33:40:58, 12.90s/it, avr_loss=0.029]
steps:   6%|▌         | 613/10016 [2:11:45<33:40:58, 12.90s/it, avr_loss=0.029]
steps:   6%|▌         | 614/10016 [2:11:58<33:40:46, 12.90s/it, avr_loss=0.029]
steps:   6%|▌         | 614/10016 [2:11:58<33:40:46, 12.90s/it, avr_loss=0.029]
steps:   6%|▌         | 615/10016 [2:12:10<33:40:33, 12.90s/it, avr_loss=0.029]
steps:   6%|▌         | 615/10016 [2:12:10<33:40:33, 12.90s/it, avr_loss=0.0289]
steps:   6%|▌         | 616/10016 [2:12:23<33:40:20, 12.90s/it, avr_loss=0.0289]
steps:   6%|▌         | 616/10016 [2:12:23<33:40:20, 12.90s/it, avr_loss=0.0289]
steps:   6%|▌         | 617/10016 [2:12:36<33:40:07, 12.90s/it, avr_loss=0.0289]
steps:   6%|▌         | 617/10016 [2:12:36<33:40:07, 12.90s/it, avr_loss=0.0289]
steps:   6%|▌         | 618/10016 [2:12:49<33:39:55, 12.90s/it, avr_loss=0.0289]
steps:   6%|▌         | 618/10016 [2:12:49<33:39:55, 12.90s/it, avr_loss=0.0289]
steps:   6%|▌         | 619/10016 [2:13:02<33:39:42, 12.90s/it, avr_loss=0.0289]
steps:   6%|▌         | 619/10016 [2:13:02<33:39:42, 12.90s/it, avr_loss=0.0289]
steps:   6%|▌         | 620/10016 [2:13:15<33:39:29, 12.90s/it, avr_loss=0.0289]
steps:   6%|▌         | 620/10016 [2:13:15<33:39:29, 12.90s/it, avr_loss=0.0289]
steps:   6%|▌         | 621/10016 [2:13:28<33:39:16, 12.90s/it, avr_loss=0.0289]
steps:   6%|▌         | 621/10016 [2:13:28<33:39:16, 12.90s/it, avr_loss=0.0289]
steps:   6%|▌         | 622/10016 [2:13:41<33:39:03, 12.90s/it, avr_loss=0.0289]
steps:   6%|▌         | 622/10016 [2:13:41<33:39:03, 12.90s/it, avr_loss=0.0289]
steps:   6%|▌         | 623/10016 [2:13:54<33:38:50, 12.90s/it, avr_loss=0.0289]
steps:   6%|▌         | 623/10016 [2:13:54<33:38:50, 12.90s/it, avr_loss=0.0289]
steps:   6%|▌         | 624/10016 [2:14:07<33:38:37, 12.90s/it, avr_loss=0.0289]
steps:   6%|▌         | 624/10016 [2:14:07<33:38:37, 12.90s/it, avr_loss=0.029] 
steps:   6%|▌         | 625/10016 [2:14:19<33:38:25, 12.90s/it, avr_loss=0.029]
steps:   6%|▌         | 625/10016 [2:14:19<33:38:25, 12.90s/it, avr_loss=0.029]
steps:   6%|▋         | 626/10016 [2:14:32<33:38:12, 12.90s/it, avr_loss=0.029]
steps:   6%|▋         | 626/10016 [2:14:32<33:38:12, 12.90s/it, avr_loss=0.029]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/loss-curve-retrain/train_lora-000001.safetensors

epoch 2/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2

steps:   6%|▋         | 627/10016 [2:14:47<33:38:25, 12.90s/it, avr_loss=0.029]
steps:   6%|▋         | 627/10016 [2:14:47<33:38:25, 12.90s/it, avr_loss=0.029]
steps:   6%|▋         | 628/10016 [2:15:00<33:38:14, 12.90s/it, avr_loss=0.029]
steps:   6%|▋         | 628/10016 [2:15:00<33:38:14, 12.90s/it, avr_loss=0.029]
steps:   6%|▋         | 629/10016 [2:15:13<33:38:03, 12.90s/it, avr_loss=0.029]
steps:   6%|▋         | 629/10016 [2:15:13<33:38:03, 12.90s/it, avr_loss=0.029]
steps:   6%|▋         | 630/10016 [2:15:26<33:37:50, 12.90s/it, avr_loss=0.029]
steps:   6%|▋         | 630/10016 [2:15:26<33:37:50, 12.90s/it, avr_loss=0.029]
steps:   6%|▋         | 631/10016 [2:15:39<33:37:37, 12.90s/it, avr_loss=0.029]
steps:   6%|▋         | 631/10016 [2:15:39<33:37:37, 12.90s/it, avr_loss=0.0289]
steps:   6%|▋         | 632/10016 [2:15:52<33:37:24, 12.90s/it, avr_loss=0.0289]
steps:   6%|▋         | 632/10016 [2:15:52<33:37:24, 12.90s/it, avr_loss=0.0289]
steps:   6%|▋         | 633/10016 [2:16:05<33:37:11, 12.90s/it, avr_loss=0.0289]
steps:   6%|▋         | 633/10016 [2:16:05<33:37:11, 12.90s/it, avr_loss=0.0289]
steps:   6%|▋         | 634/10016 [2:16:17<33:36:58, 12.90s/it, avr_loss=0.0289]
steps:   6%|▋         | 634/10016 [2:16:17<33:36:58, 12.90s/it, avr_loss=0.0289]
steps:   6%|▋         | 635/10016 [2:16:30<33:36:45, 12.90s/it, avr_loss=0.0289]
steps:   6%|▋         | 635/10016 [2:16:30<33:36:45, 12.90s/it, avr_loss=0.029] 
steps:   6%|▋         | 636/10016 [2:16:43<33:36:32, 12.90s/it, avr_loss=0.029]
steps:   6%|▋         | 636/10016 [2:16:43<33:36:32, 12.90s/it, avr_loss=0.0289]
steps:   6%|▋         | 637/10016 [2:16:56<33:36:19, 12.90s/it, avr_loss=0.0289]
steps:   6%|▋         | 637/10016 [2:16:56<33:36:19, 12.90s/it, avr_loss=0.0289]
steps:   6%|▋         | 638/10016 [2:17:09<33:36:06, 12.90s/it, avr_loss=0.0289]
steps:   6%|▋         | 638/10016 [2:17:09<33:36:06, 12.90s/it, avr_loss=0.0289]
steps:   6%|▋         | 639/10016 [2:17:22<33:35:52, 12.90s/it, avr_loss=0.0289]
steps:   6%|▋         | 639/10016 [2:17:22<33:35:52, 12.90s/it, avr_loss=0.0289]
steps:   6%|▋         | 640/10016 [2:17:35<33:35:39, 12.90s/it, avr_loss=0.0289]
steps:   6%|▋         | 640/10016 [2:17:35<33:35:39, 12.90s/it, avr_loss=0.0289]
steps:   6%|▋         | 641/10016 [2:17:48<33:35:26, 12.90s/it, avr_loss=0.0289]
steps:   6%|▋         | 641/10016 [2:17:48<33:35:26, 12.90s/it, avr_loss=0.0289]
steps:   6%|▋         | 642/10016 [2:18:01<33:35:13, 12.90s/it, avr_loss=0.0289]
steps:   6%|▋         | 642/10016 [2:18:01<33:35:13, 12.90s/it, avr_loss=0.0288]
steps:   6%|▋         | 643/10016 [2:18:13<33:35:00, 12.90s/it, avr_loss=0.0288]
steps:   6%|▋         | 643/10016 [2:18:13<33:35:00, 12.90s/it, avr_loss=0.0288]
steps:   6%|▋         | 644/10016 [2:18:26<33:34:47, 12.90s/it, avr_loss=0.0288]
steps:   6%|▋         | 644/10016 [2:18:26<33:34:47, 12.90s/it, avr_loss=0.0288]
steps:   6%|▋         | 645/10016 [2:18:39<33:34:34, 12.90s/it, avr_loss=0.0288]
steps:   6%|▋         | 645/10016 [2:18:39<33:34:34, 12.90s/it, avr_loss=0.0288]
steps:   6%|▋         | 646/10016 [2:18:52<33:34:21, 12.90s/it, avr_loss=0.0288]
steps:   6%|▋         | 646/10016 [2:18:52<33:34:21, 12.90s/it, avr_loss=0.0288]
steps:   6%|▋         | 647/10016 [2:19:05<33:34:08, 12.90s/it, avr_loss=0.0288]
steps:   6%|▋         | 647/10016 [2:19:05<33:34:08, 12.90s/it, avr_loss=0.0287]
steps:   6%|▋         | 648/10016 [2:19:18<33:33:55, 12.90s/it, avr_loss=0.0287]
steps:   6%|▋         | 648/10016 [2:19:18<33:33:55, 12.90s/it, avr_loss=0.0287]
steps:   6%|▋         | 649/10016 [2:19:31<33:33:42, 12.90s/it, avr_loss=0.0287]
steps:   6%|▋         | 649/10016 [2:19:31<33:33:42, 12.90s/it, avr_loss=0.0286]
steps:   6%|▋         | 650/10016 [2:19:44<33:33:29, 12.90s/it, avr_loss=0.0286]
steps:   6%|▋         | 650/10016 [2:19:44<33:33:29, 12.90s/it, avr_loss=0.0286]
steps:   6%|▋         | 651/10016 [2:19:57<33:33:16, 12.90s/it, avr_loss=0.0286]
steps:   6%|▋         | 651/10016 [2:19:57<33:33:16, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 652/10016 [2:20:09<33:33:03, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 652/10016 [2:20:09<33:33:03, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 653/10016 [2:20:22<33:32:50, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 653/10016 [2:20:22<33:32:50, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 654/10016 [2:20:35<33:32:37, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 654/10016 [2:20:35<33:32:37, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 655/10016 [2:20:48<33:32:24, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 655/10016 [2:20:48<33:32:24, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 656/10016 [2:21:01<33:32:11, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 656/10016 [2:21:01<33:32:11, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 657/10016 [2:21:14<33:31:58, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 657/10016 [2:21:14<33:31:58, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 658/10016 [2:21:27<33:31:45, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 658/10016 [2:21:27<33:31:45, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 659/10016 [2:21:40<33:31:32, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 659/10016 [2:21:40<33:31:32, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 660/10016 [2:21:53<33:31:18, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 660/10016 [2:21:53<33:31:18, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 661/10016 [2:22:05<33:31:05, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 661/10016 [2:22:05<33:31:05, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 662/10016 [2:22:18<33:30:52, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 662/10016 [2:22:18<33:30:52, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 663/10016 [2:22:31<33:30:39, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 663/10016 [2:22:31<33:30:39, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 664/10016 [2:22:44<33:30:28, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 664/10016 [2:22:44<33:30:28, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 665/10016 [2:22:57<33:30:14, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 665/10016 [2:22:57<33:30:14, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 666/10016 [2:23:10<33:30:01, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 666/10016 [2:23:10<33:30:01, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 667/10016 [2:23:23<33:29:48, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 667/10016 [2:23:23<33:29:48, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 668/10016 [2:23:36<33:29:34, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 668/10016 [2:23:36<33:29:34, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 669/10016 [2:23:49<33:29:21, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 669/10016 [2:23:49<33:29:21, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 670/10016 [2:24:01<33:29:08, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 670/10016 [2:24:01<33:29:08, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 671/10016 [2:24:14<33:28:55, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 671/10016 [2:24:14<33:28:55, 12.90s/it, avr_loss=0.0287]
steps:   7%|▋         | 672/10016 [2:24:27<33:28:41, 12.90s/it, avr_loss=0.0287]
steps:   7%|▋         | 672/10016 [2:24:27<33:28:41, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 673/10016 [2:24:40<33:28:28, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 673/10016 [2:24:40<33:28:28, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 674/10016 [2:24:53<33:28:15, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 674/10016 [2:24:53<33:28:15, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 675/10016 [2:25:06<33:28:02, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 675/10016 [2:25:06<33:28:02, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 676/10016 [2:25:19<33:27:48, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 676/10016 [2:25:19<33:27:48, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 677/10016 [2:25:32<33:27:35, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 677/10016 [2:25:32<33:27:35, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 678/10016 [2:25:44<33:27:22, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 678/10016 [2:25:44<33:27:22, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 679/10016 [2:25:57<33:27:09, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 679/10016 [2:25:57<33:27:09, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 680/10016 [2:26:10<33:26:55, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 680/10016 [2:26:10<33:26:55, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 681/10016 [2:26:23<33:26:42, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 681/10016 [2:26:23<33:26:42, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 682/10016 [2:26:36<33:26:29, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 682/10016 [2:26:36<33:26:29, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 683/10016 [2:26:49<33:26:15, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 683/10016 [2:26:49<33:26:15, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 684/10016 [2:27:02<33:26:02, 12.90s/it, avr_loss=0.0286]
steps:   7%|▋         | 684/10016 [2:27:02<33:26:02, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 685/10016 [2:27:15<33:25:49, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 685/10016 [2:27:15<33:25:49, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 686/10016 [2:27:27<33:25:36, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 686/10016 [2:27:27<33:25:36, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 687/10016 [2:27:40<33:25:23, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 687/10016 [2:27:40<33:25:23, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 688/10016 [2:27:53<33:25:10, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 688/10016 [2:27:53<33:25:10, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 689/10016 [2:28:06<33:24:57, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 689/10016 [2:28:06<33:24:57, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 690/10016 [2:28:19<33:24:44, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 690/10016 [2:28:19<33:24:44, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 691/10016 [2:28:32<33:24:31, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 691/10016 [2:28:32<33:24:31, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 692/10016 [2:28:45<33:24:18, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 692/10016 [2:28:45<33:24:18, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 693/10016 [2:28:58<33:24:04, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 693/10016 [2:28:58<33:24:04, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 694/10016 [2:29:10<33:23:51, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 694/10016 [2:29:10<33:23:51, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 695/10016 [2:29:23<33:23:38, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 695/10016 [2:29:23<33:23:38, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 696/10016 [2:29:36<33:23:25, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 696/10016 [2:29:36<33:23:25, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 697/10016 [2:29:49<33:23:12, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 697/10016 [2:29:49<33:23:12, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 698/10016 [2:30:02<33:22:58, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 698/10016 [2:30:02<33:22:58, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 699/10016 [2:30:15<33:22:45, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 699/10016 [2:30:15<33:22:45, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 700/10016 [2:30:28<33:22:32, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 700/10016 [2:30:28<33:22:32, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 701/10016 [2:30:41<33:22:19, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 701/10016 [2:30:41<33:22:19, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 702/10016 [2:30:53<33:22:06, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 702/10016 [2:30:53<33:22:06, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 703/10016 [2:31:06<33:21:53, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 703/10016 [2:31:06<33:21:53, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 704/10016 [2:31:19<33:21:40, 12.90s/it, avr_loss=0.0285]
steps:   7%|▋         | 704/10016 [2:31:19<33:21:40, 12.90s/it, avr_loss=0.0284]
steps:   7%|▋         | 705/10016 [2:31:32<33:21:27, 12.90s/it, avr_loss=0.0284]
steps:   7%|▋         | 705/10016 [2:31:32<33:21:27, 12.90s/it, avr_loss=0.0284]
steps:   7%|▋         | 706/10016 [2:31:45<33:21:14, 12.90s/it, avr_loss=0.0284]
steps:   7%|▋         | 706/10016 [2:31:45<33:21:14, 12.90s/it, avr_loss=0.0283]
steps:   7%|▋         | 707/10016 [2:31:58<33:21:01, 12.90s/it, avr_loss=0.0283]
steps:   7%|▋         | 707/10016 [2:31:58<33:21:01, 12.90s/it, avr_loss=0.0282]
steps:   7%|▋         | 708/10016 [2:32:11<33:20:48, 12.90s/it, avr_loss=0.0282]
steps:   7%|▋         | 708/10016 [2:32:11<33:20:48, 12.90s/it, avr_loss=0.0282]
steps:   7%|▋         | 709/10016 [2:32:24<33:20:35, 12.90s/it, avr_loss=0.0282]
steps:   7%|▋         | 709/10016 [2:32:24<33:20:35, 12.90s/it, avr_loss=0.0282]
steps:   7%|▋         | 710/10016 [2:32:37<33:20:22, 12.90s/it, avr_loss=0.0282]
steps:   7%|▋         | 710/10016 [2:32:37<33:20:22, 12.90s/it, avr_loss=0.028] 
steps:   7%|▋         | 711/10016 [2:32:49<33:20:09, 12.90s/it, avr_loss=0.028]
steps:   7%|▋         | 711/10016 [2:32:49<33:20:09, 12.90s/it, avr_loss=0.028]
steps:   7%|▋         | 712/10016 [2:33:02<33:19:56, 12.90s/it, avr_loss=0.028]
steps:   7%|▋         | 712/10016 [2:33:02<33:19:56, 12.90s/it, avr_loss=0.028]
steps:   7%|▋         | 713/10016 [2:33:15<33:19:43, 12.90s/it, avr_loss=0.028]
steps:   7%|▋         | 713/10016 [2:33:15<33:19:43, 12.90s/it, avr_loss=0.028]
steps:   7%|▋         | 714/10016 [2:33:28<33:19:30, 12.90s/it, avr_loss=0.028]
steps:   7%|▋         | 714/10016 [2:33:28<33:19:30, 12.90s/it, avr_loss=0.028]
steps:   7%|▋         | 715/10016 [2:33:41<33:19:17, 12.90s/it, avr_loss=0.028]
steps:   7%|▋         | 715/10016 [2:33:41<33:19:17, 12.90s/it, avr_loss=0.028]
steps:   7%|▋         | 716/10016 [2:33:54<33:19:04, 12.90s/it, avr_loss=0.028]
steps:   7%|▋         | 716/10016 [2:33:54<33:19:04, 12.90s/it, avr_loss=0.0279]
steps:   7%|▋         | 717/10016 [2:34:07<33:18:51, 12.90s/it, avr_loss=0.0279]
steps:   7%|▋         | 717/10016 [2:34:07<33:18:51, 12.90s/it, avr_loss=0.0279]
steps:   7%|▋         | 718/10016 [2:34:20<33:18:38, 12.90s/it, avr_loss=0.0279]
steps:   7%|▋         | 718/10016 [2:34:20<33:18:38, 12.90s/it, avr_loss=0.0279]
steps:   7%|▋         | 719/10016 [2:34:33<33:18:25, 12.90s/it, avr_loss=0.0279]
steps:   7%|▋         | 719/10016 [2:34:33<33:18:25, 12.90s/it, avr_loss=0.0279]
steps:   7%|▋         | 720/10016 [2:34:45<33:18:12, 12.90s/it, avr_loss=0.0279]
steps:   7%|▋         | 720/10016 [2:34:45<33:18:12, 12.90s/it, avr_loss=0.0278]
steps:   7%|▋         | 721/10016 [2:34:58<33:17:59, 12.90s/it, avr_loss=0.0278]
steps:   7%|▋         | 721/10016 [2:34:58<33:17:59, 12.90s/it, avr_loss=0.0278]
steps:   7%|▋         | 722/10016 [2:35:11<33:17:46, 12.90s/it, avr_loss=0.0278]
steps:   7%|▋         | 722/10016 [2:35:11<33:17:46, 12.90s/it, avr_loss=0.0278]
steps:   7%|▋         | 723/10016 [2:35:24<33:17:33, 12.90s/it, avr_loss=0.0278]
steps:   7%|▋         | 723/10016 [2:35:24<33:17:33, 12.90s/it, avr_loss=0.0278]
steps:   7%|▋         | 724/10016 [2:35:37<33:17:20, 12.90s/it, avr_loss=0.0278]
steps:   7%|▋         | 724/10016 [2:35:37<33:17:20, 12.90s/it, avr_loss=0.0278]
steps:   7%|▋         | 725/10016 [2:35:50<33:17:07, 12.90s/it, avr_loss=0.0278]
steps:   7%|▋         | 725/10016 [2:35:50<33:17:07, 12.90s/it, avr_loss=0.0278]
steps:   7%|▋         | 726/10016 [2:36:03<33:16:54, 12.90s/it, avr_loss=0.0278]
steps:   7%|▋         | 726/10016 [2:36:03<33:16:54, 12.90s/it, avr_loss=0.0278]
steps:   7%|▋         | 727/10016 [2:36:16<33:16:41, 12.90s/it, avr_loss=0.0278]
steps:   7%|▋         | 727/10016 [2:36:16<33:16:41, 12.90s/it, avr_loss=0.0278]
steps:   7%|▋         | 728/10016 [2:36:29<33:16:28, 12.90s/it, avr_loss=0.0278]
steps:   7%|▋         | 728/10016 [2:36:29<33:16:28, 12.90s/it, avr_loss=0.0278]
steps:   7%|▋         | 729/10016 [2:36:42<33:16:15, 12.90s/it, avr_loss=0.0278]
steps:   7%|▋         | 729/10016 [2:36:42<33:16:15, 12.90s/it, avr_loss=0.0278]
steps:   7%|▋         | 730/10016 [2:36:54<33:16:02, 12.90s/it, avr_loss=0.0278]
steps:   7%|▋         | 730/10016 [2:36:54<33:16:02, 12.90s/it, avr_loss=0.0278]
steps:   7%|▋         | 731/10016 [2:37:07<33:15:49, 12.90s/it, avr_loss=0.0278]
steps:   7%|▋         | 731/10016 [2:37:07<33:15:49, 12.90s/it, avr_loss=0.0277]
steps:   7%|▋         | 732/10016 [2:37:20<33:15:36, 12.90s/it, avr_loss=0.0277]
steps:   7%|▋         | 732/10016 [2:37:20<33:15:36, 12.90s/it, avr_loss=0.0278]
steps:   7%|▋         | 733/10016 [2:37:33<33:15:23, 12.90s/it, avr_loss=0.0278]
steps:   7%|▋         | 733/10016 [2:37:33<33:15:23, 12.90s/it, avr_loss=0.0277]
steps:   7%|▋         | 734/10016 [2:37:46<33:15:10, 12.90s/it, avr_loss=0.0277]
steps:   7%|▋         | 734/10016 [2:37:46<33:15:10, 12.90s/it, avr_loss=0.0277]
steps:   7%|▋         | 735/10016 [2:37:59<33:14:57, 12.90s/it, avr_loss=0.0277]
steps:   7%|▋         | 735/10016 [2:37:59<33:14:57, 12.90s/it, avr_loss=0.0277]
steps:   7%|▋         | 736/10016 [2:38:12<33:14:44, 12.90s/it, avr_loss=0.0277]
steps:   7%|▋         | 736/10016 [2:38:12<33:14:44, 12.90s/it, avr_loss=0.0277]
steps:   7%|▋         | 737/10016 [2:38:25<33:14:31, 12.90s/it, avr_loss=0.0277]
steps:   7%|▋         | 737/10016 [2:38:25<33:14:31, 12.90s/it, avr_loss=0.0277]
steps:   7%|▋         | 738/10016 [2:38:38<33:14:18, 12.90s/it, avr_loss=0.0277]
steps:   7%|▋         | 738/10016 [2:38:38<33:14:18, 12.90s/it, avr_loss=0.0277]
steps:   7%|▋         | 739/10016 [2:38:50<33:14:05, 12.90s/it, avr_loss=0.0277]
steps:   7%|▋         | 739/10016 [2:38:50<33:14:05, 12.90s/it, avr_loss=0.0276]
steps:   7%|▋         | 740/10016 [2:39:03<33:13:52, 12.90s/it, avr_loss=0.0276]
steps:   7%|▋         | 740/10016 [2:39:03<33:13:52, 12.90s/it, avr_loss=0.0276]
steps:   7%|▋         | 741/10016 [2:39:16<33:13:40, 12.90s/it, avr_loss=0.0276]
steps:   7%|▋         | 741/10016 [2:39:16<33:13:40, 12.90s/it, avr_loss=0.0276]
steps:   7%|▋         | 742/10016 [2:39:29<33:13:27, 12.90s/it, avr_loss=0.0276]
steps:   7%|▋         | 742/10016 [2:39:29<33:13:27, 12.90s/it, avr_loss=0.0276]
steps:   7%|▋         | 743/10016 [2:39:42<33:13:14, 12.90s/it, avr_loss=0.0276]
steps:   7%|▋         | 743/10016 [2:39:42<33:13:14, 12.90s/it, avr_loss=0.0276]
steps:   7%|▋         | 744/10016 [2:39:55<33:13:01, 12.90s/it, avr_loss=0.0276]
steps:   7%|▋         | 744/10016 [2:39:55<33:13:01, 12.90s/it, avr_loss=0.0276]
steps:   7%|▋         | 745/10016 [2:40:08<33:12:48, 12.90s/it, avr_loss=0.0276]
steps:   7%|▋         | 745/10016 [2:40:08<33:12:48, 12.90s/it, avr_loss=0.0276]
steps:   7%|▋         | 746/10016 [2:40:21<33:12:35, 12.90s/it, avr_loss=0.0276]
steps:   7%|▋         | 746/10016 [2:40:21<33:12:35, 12.90s/it, avr_loss=0.0276]
steps:   7%|▋         | 747/10016 [2:40:34<33:12:22, 12.90s/it, avr_loss=0.0276]
steps:   7%|▋         | 747/10016 [2:40:34<33:12:22, 12.90s/it, avr_loss=0.0275]
steps:   7%|▋         | 748/10016 [2:40:47<33:12:11, 12.90s/it, avr_loss=0.0275]
steps:   7%|▋         | 748/10016 [2:40:47<33:12:11, 12.90s/it, avr_loss=0.0276]
steps:   7%|▋         | 749/10016 [2:41:00<33:11:58, 12.90s/it, avr_loss=0.0276]
steps:   7%|▋         | 749/10016 [2:41:00<33:11:58, 12.90s/it, avr_loss=0.0276]
steps:   7%|▋         | 750/10016 [2:41:12<33:11:45, 12.90s/it, avr_loss=0.0276]
steps:   7%|▋         | 750/10016 [2:41:12<33:11:45, 12.90s/it, avr_loss=0.0276]
steps:   7%|▋         | 751/10016 [2:41:25<33:11:32, 12.90s/it, avr_loss=0.0276]
steps:   7%|▋         | 751/10016 [2:41:25<33:11:32, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 752/10016 [2:41:38<33:11:19, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 752/10016 [2:41:38<33:11:19, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 753/10016 [2:41:51<33:11:06, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 753/10016 [2:41:51<33:11:06, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 754/10016 [2:42:04<33:10:54, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 754/10016 [2:42:04<33:10:54, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 755/10016 [2:42:17<33:10:41, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 755/10016 [2:42:17<33:10:41, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 756/10016 [2:42:30<33:10:28, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 756/10016 [2:42:30<33:10:28, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 757/10016 [2:42:43<33:10:15, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 757/10016 [2:42:43<33:10:15, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 758/10016 [2:42:56<33:10:02, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 758/10016 [2:42:56<33:10:02, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 759/10016 [2:43:08<33:09:49, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 759/10016 [2:43:08<33:09:49, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 760/10016 [2:43:21<33:09:36, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 760/10016 [2:43:21<33:09:36, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 761/10016 [2:43:34<33:09:23, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 761/10016 [2:43:34<33:09:23, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 762/10016 [2:43:47<33:09:10, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 762/10016 [2:43:47<33:09:10, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 763/10016 [2:44:00<33:08:57, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 763/10016 [2:44:00<33:08:57, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 764/10016 [2:44:13<33:08:44, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 764/10016 [2:44:13<33:08:44, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 765/10016 [2:44:26<33:08:31, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 765/10016 [2:44:26<33:08:31, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 766/10016 [2:44:39<33:08:18, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 766/10016 [2:44:39<33:08:18, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 767/10016 [2:44:52<33:08:05, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 767/10016 [2:44:52<33:08:05, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 768/10016 [2:45:05<33:07:52, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 768/10016 [2:45:05<33:07:52, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 769/10016 [2:45:17<33:07:39, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 769/10016 [2:45:17<33:07:39, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 770/10016 [2:45:30<33:07:26, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 770/10016 [2:45:30<33:07:26, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 771/10016 [2:45:43<33:07:14, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 771/10016 [2:45:43<33:07:14, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 772/10016 [2:45:56<33:07:01, 12.90s/it, avr_loss=0.0276]
steps:   8%|▊         | 772/10016 [2:45:56<33:07:01, 12.90s/it, avr_loss=0.0275]
steps:   8%|▊         | 773/10016 [2:46:09<33:06:49, 12.90s/it, avr_loss=0.0275]
steps:   8%|▊         | 773/10016 [2:46:09<33:06:49, 12.90s/it, avr_loss=0.0275]
steps:   8%|▊         | 774/10016 [2:46:22<33:06:36, 12.90s/it, avr_loss=0.0275]
steps:   8%|▊         | 774/10016 [2:46:22<33:06:36, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 775/10016 [2:46:35<33:06:23, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 775/10016 [2:46:35<33:06:23, 12.90s/it, avr_loss=0.0275]
steps:   8%|▊         | 776/10016 [2:46:48<33:06:10, 12.90s/it, avr_loss=0.0275]
steps:   8%|▊         | 776/10016 [2:46:48<33:06:10, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 777/10016 [2:47:01<33:05:57, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 777/10016 [2:47:01<33:05:57, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 778/10016 [2:47:13<33:05:43, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 778/10016 [2:47:13<33:05:43, 12.90s/it, avr_loss=0.0275]
steps:   8%|▊         | 779/10016 [2:47:26<33:05:30, 12.90s/it, avr_loss=0.0275]
steps:   8%|▊         | 779/10016 [2:47:26<33:05:30, 12.90s/it, avr_loss=0.0275]
steps:   8%|▊         | 780/10016 [2:47:39<33:05:17, 12.90s/it, avr_loss=0.0275]
steps:   8%|▊         | 780/10016 [2:47:39<33:05:17, 12.90s/it, avr_loss=0.0275]
steps:   8%|▊         | 781/10016 [2:47:52<33:05:04, 12.90s/it, avr_loss=0.0275]
steps:   8%|▊         | 781/10016 [2:47:52<33:05:04, 12.90s/it, avr_loss=0.0275]
steps:   8%|▊         | 782/10016 [2:48:05<33:04:51, 12.90s/it, avr_loss=0.0275]
steps:   8%|▊         | 782/10016 [2:48:05<33:04:51, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 783/10016 [2:48:18<33:04:38, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 783/10016 [2:48:18<33:04:38, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 784/10016 [2:48:31<33:04:25, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 784/10016 [2:48:31<33:04:25, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 785/10016 [2:48:44<33:04:12, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 785/10016 [2:48:44<33:04:12, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 786/10016 [2:48:57<33:03:58, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 786/10016 [2:48:57<33:03:58, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 787/10016 [2:49:09<33:03:45, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 787/10016 [2:49:09<33:03:45, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 788/10016 [2:49:22<33:03:32, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 788/10016 [2:49:22<33:03:32, 12.90s/it, avr_loss=0.0273]
steps:   8%|▊         | 789/10016 [2:49:35<33:03:19, 12.90s/it, avr_loss=0.0273]
steps:   8%|▊         | 789/10016 [2:49:35<33:03:19, 12.90s/it, avr_loss=0.0273]
steps:   8%|▊         | 790/10016 [2:49:48<33:03:06, 12.90s/it, avr_loss=0.0273]
steps:   8%|▊         | 790/10016 [2:49:48<33:03:06, 12.90s/it, avr_loss=0.0273]
steps:   8%|▊         | 791/10016 [2:50:01<33:02:52, 12.90s/it, avr_loss=0.0273]
steps:   8%|▊         | 791/10016 [2:50:01<33:02:52, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 792/10016 [2:50:14<33:02:39, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 792/10016 [2:50:14<33:02:39, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 793/10016 [2:50:27<33:02:26, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 793/10016 [2:50:27<33:02:26, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 794/10016 [2:50:39<33:02:13, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 794/10016 [2:50:39<33:02:13, 12.90s/it, avr_loss=0.0275]
steps:   8%|▊         | 795/10016 [2:50:52<33:02:00, 12.90s/it, avr_loss=0.0275]
steps:   8%|▊         | 795/10016 [2:50:52<33:02:00, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 796/10016 [2:51:05<33:01:47, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 796/10016 [2:51:05<33:01:47, 12.90s/it, avr_loss=0.0275]
steps:   8%|▊         | 797/10016 [2:51:18<33:01:34, 12.90s/it, avr_loss=0.0275]
steps:   8%|▊         | 797/10016 [2:51:18<33:01:34, 12.90s/it, avr_loss=0.0275]
steps:   8%|▊         | 798/10016 [2:51:31<33:01:21, 12.90s/it, avr_loss=0.0275]
steps:   8%|▊         | 798/10016 [2:51:31<33:01:21, 12.90s/it, avr_loss=0.0275]
steps:   8%|▊         | 799/10016 [2:51:44<33:01:07, 12.90s/it, avr_loss=0.0275]
steps:   8%|▊         | 799/10016 [2:51:44<33:01:07, 12.90s/it, avr_loss=0.0275]
steps:   8%|▊         | 800/10016 [2:51:57<33:00:54, 12.90s/it, avr_loss=0.0275]
steps:   8%|▊         | 800/10016 [2:51:57<33:00:54, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 801/10016 [2:52:10<33:00:41, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 801/10016 [2:52:10<33:00:41, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 802/10016 [2:52:23<33:00:28, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 802/10016 [2:52:23<33:00:28, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 803/10016 [2:52:35<33:00:15, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 803/10016 [2:52:35<33:00:15, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 804/10016 [2:52:48<33:00:02, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 804/10016 [2:52:48<33:00:02, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 805/10016 [2:53:01<32:59:49, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 805/10016 [2:53:01<32:59:49, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 806/10016 [2:53:14<32:59:36, 12.90s/it, avr_loss=0.0274]
steps:   8%|▊         | 806/10016 [2:53:14<32:59:36, 12.90s/it, avr_loss=0.0273]
steps:   8%|▊         | 807/10016 [2:53:27<32:59:22, 12.90s/it, avr_loss=0.0273]
steps:   8%|▊         | 807/10016 [2:53:27<32:59:22, 12.90s/it, avr_loss=0.0273]
steps:   8%|▊         | 808/10016 [2:53:40<32:59:09, 12.90s/it, avr_loss=0.0273]
steps:   8%|▊         | 808/10016 [2:53:40<32:59:09, 12.90s/it, avr_loss=0.0272]
steps:   8%|▊         | 809/10016 [2:53:53<32:58:56, 12.90s/it, avr_loss=0.0272]
steps:   8%|▊         | 809/10016 [2:53:53<32:58:56, 12.90s/it, avr_loss=0.0271]
steps:   8%|▊         | 810/10016 [2:54:06<32:58:43, 12.90s/it, avr_loss=0.0271]
steps:   8%|▊         | 810/10016 [2:54:06<32:58:43, 12.90s/it, avr_loss=0.0271]
steps:   8%|▊         | 811/10016 [2:54:18<32:58:30, 12.90s/it, avr_loss=0.0271]
steps:   8%|▊         | 811/10016 [2:54:18<32:58:30, 12.90s/it, avr_loss=0.0268]
steps:   8%|▊         | 812/10016 [2:54:31<32:58:16, 12.90s/it, avr_loss=0.0268]
steps:   8%|▊         | 812/10016 [2:54:31<32:58:16, 12.90s/it, avr_loss=0.0268]
steps:   8%|▊         | 813/10016 [2:54:44<32:58:03, 12.90s/it, avr_loss=0.0268]
steps:   8%|▊         | 813/10016 [2:54:44<32:58:03, 12.90s/it, avr_loss=0.0268]
steps:   8%|▊         | 814/10016 [2:54:57<32:57:50, 12.90s/it, avr_loss=0.0268]
steps:   8%|▊         | 814/10016 [2:54:57<32:57:50, 12.90s/it, avr_loss=0.0267]
steps:   8%|▊         | 815/10016 [2:55:10<32:57:37, 12.90s/it, avr_loss=0.0267]
steps:   8%|▊         | 815/10016 [2:55:10<32:57:37, 12.90s/it, avr_loss=0.0267]
steps:   8%|▊         | 816/10016 [2:55:23<32:57:24, 12.90s/it, avr_loss=0.0267]
steps:   8%|▊         | 816/10016 [2:55:23<32:57:24, 12.90s/it, avr_loss=0.0267]
steps:   8%|▊         | 817/10016 [2:55:36<32:57:11, 12.90s/it, avr_loss=0.0267]
steps:   8%|▊         | 817/10016 [2:55:36<32:57:11, 12.90s/it, avr_loss=0.0267]
steps:   8%|▊         | 818/10016 [2:55:49<32:56:58, 12.90s/it, avr_loss=0.0267]
steps:   8%|▊         | 818/10016 [2:55:49<32:56:58, 12.90s/it, avr_loss=0.0267]
steps:   8%|▊         | 819/10016 [2:56:01<32:56:45, 12.90s/it, avr_loss=0.0267]
steps:   8%|▊         | 819/10016 [2:56:01<32:56:45, 12.90s/it, avr_loss=0.0267]
steps:   8%|▊         | 820/10016 [2:56:14<32:56:32, 12.90s/it, avr_loss=0.0267]
steps:   8%|▊         | 820/10016 [2:56:14<32:56:32, 12.90s/it, avr_loss=0.0267]
steps:   8%|▊         | 821/10016 [2:56:27<32:56:18, 12.90s/it, avr_loss=0.0267]
steps:   8%|▊         | 821/10016 [2:56:27<32:56:18, 12.90s/it, avr_loss=0.0267]
steps:   8%|▊         | 822/10016 [2:56:40<32:56:05, 12.90s/it, avr_loss=0.0267]
steps:   8%|▊         | 822/10016 [2:56:40<32:56:05, 12.90s/it, avr_loss=0.0268]
steps:   8%|▊         | 823/10016 [2:56:53<32:55:52, 12.90s/it, avr_loss=0.0268]
steps:   8%|▊         | 823/10016 [2:56:53<32:55:52, 12.90s/it, avr_loss=0.0268]
steps:   8%|▊         | 824/10016 [2:57:06<32:55:39, 12.90s/it, avr_loss=0.0268]
steps:   8%|▊         | 824/10016 [2:57:06<32:55:39, 12.90s/it, avr_loss=0.0268]
steps:   8%|▊         | 825/10016 [2:57:19<32:55:26, 12.90s/it, avr_loss=0.0268]
steps:   8%|▊         | 825/10016 [2:57:19<32:55:26, 12.90s/it, avr_loss=0.0268]
steps:   8%|▊         | 826/10016 [2:57:31<32:55:13, 12.90s/it, avr_loss=0.0268]
steps:   8%|▊         | 826/10016 [2:57:31<32:55:13, 12.90s/it, avr_loss=0.0268]
steps:   8%|▊         | 827/10016 [2:57:44<32:54:59, 12.90s/it, avr_loss=0.0268]
steps:   8%|▊         | 827/10016 [2:57:44<32:54:59, 12.90s/it, avr_loss=0.0268]
steps:   8%|▊         | 828/10016 [2:57:57<32:54:46, 12.90s/it, avr_loss=0.0268]
steps:   8%|▊         | 828/10016 [2:57:57<32:54:46, 12.90s/it, avr_loss=0.0268]
steps:   8%|▊         | 829/10016 [2:58:10<32:54:33, 12.90s/it, avr_loss=0.0268]
steps:   8%|▊         | 829/10016 [2:58:10<32:54:33, 12.90s/it, avr_loss=0.0266]
steps:   8%|▊         | 830/10016 [2:58:23<32:54:20, 12.90s/it, avr_loss=0.0266]
steps:   8%|▊         | 830/10016 [2:58:23<32:54:20, 12.90s/it, avr_loss=0.0266]
steps:   8%|▊         | 831/10016 [2:58:36<32:54:07, 12.90s/it, avr_loss=0.0266]
steps:   8%|▊         | 831/10016 [2:58:36<32:54:07, 12.90s/it, avr_loss=0.0266]
steps:   8%|▊         | 832/10016 [2:58:49<32:53:54, 12.90s/it, avr_loss=0.0266]
steps:   8%|▊         | 832/10016 [2:58:49<32:53:54, 12.90s/it, avr_loss=0.0266]
steps:   8%|▊         | 833/10016 [2:59:02<32:53:41, 12.90s/it, avr_loss=0.0266]
steps:   8%|▊         | 833/10016 [2:59:02<32:53:41, 12.90s/it, avr_loss=0.0266]
steps:   8%|▊         | 834/10016 [2:59:15<32:53:28, 12.90s/it, avr_loss=0.0266]
steps:   8%|▊         | 834/10016 [2:59:15<32:53:28, 12.90s/it, avr_loss=0.0266]
steps:   8%|▊         | 835/10016 [2:59:27<32:53:15, 12.90s/it, avr_loss=0.0266]
steps:   8%|▊         | 835/10016 [2:59:27<32:53:15, 12.90s/it, avr_loss=0.0266]
steps:   8%|▊         | 836/10016 [2:59:40<32:53:02, 12.90s/it, avr_loss=0.0266]
steps:   8%|▊         | 836/10016 [2:59:40<32:53:02, 12.90s/it, avr_loss=0.0266]
steps:   8%|▊         | 837/10016 [2:59:53<32:52:48, 12.90s/it, avr_loss=0.0266]
steps:   8%|▊         | 837/10016 [2:59:53<32:52:49, 12.90s/it, avr_loss=0.0266]
steps:   8%|▊         | 838/10016 [3:00:06<32:52:35, 12.90s/it, avr_loss=0.0266]
steps:   8%|▊         | 838/10016 [3:00:06<32:52:35, 12.90s/it, avr_loss=0.0264]
steps:   8%|▊         | 839/10016 [3:00:19<32:52:22, 12.90s/it, avr_loss=0.0264]
steps:   8%|▊         | 839/10016 [3:00:19<32:52:22, 12.90s/it, avr_loss=0.0264]
steps:   8%|▊         | 840/10016 [3:00:32<32:52:09, 12.90s/it, avr_loss=0.0264]
steps:   8%|▊         | 840/10016 [3:00:32<32:52:09, 12.90s/it, avr_loss=0.0264]
steps:   8%|▊         | 841/10016 [3:00:45<32:51:56, 12.90s/it, avr_loss=0.0264]
steps:   8%|▊         | 841/10016 [3:00:45<32:51:56, 12.90s/it, avr_loss=0.0264]
steps:   8%|▊         | 842/10016 [3:00:58<32:51:43, 12.90s/it, avr_loss=0.0264]
steps:   8%|▊         | 842/10016 [3:00:58<32:51:43, 12.90s/it, avr_loss=0.0264]
steps:   8%|▊         | 843/10016 [3:01:10<32:51:30, 12.90s/it, avr_loss=0.0264]
steps:   8%|▊         | 843/10016 [3:01:10<32:51:30, 12.90s/it, avr_loss=0.0264]
steps:   8%|▊         | 844/10016 [3:01:23<32:51:16, 12.90s/it, avr_loss=0.0264]
steps:   8%|▊         | 844/10016 [3:01:23<32:51:16, 12.90s/it, avr_loss=0.0264]
steps:   8%|▊         | 845/10016 [3:01:36<32:51:03, 12.90s/it, avr_loss=0.0264]
steps:   8%|▊         | 845/10016 [3:01:36<32:51:03, 12.90s/it, avr_loss=0.0264]
steps:   8%|▊         | 846/10016 [3:01:49<32:50:50, 12.90s/it, avr_loss=0.0264]
steps:   8%|▊         | 846/10016 [3:01:49<32:50:50, 12.90s/it, avr_loss=0.0264]
steps:   8%|▊         | 847/10016 [3:02:02<32:50:37, 12.90s/it, avr_loss=0.0264]
steps:   8%|▊         | 847/10016 [3:02:02<32:50:37, 12.90s/it, avr_loss=0.0264]
steps:   8%|▊         | 848/10016 [3:02:15<32:50:24, 12.90s/it, avr_loss=0.0264]
steps:   8%|▊         | 848/10016 [3:02:15<32:50:24, 12.90s/it, avr_loss=0.0263]
steps:   8%|▊         | 849/10016 [3:02:28<32:50:11, 12.90s/it, avr_loss=0.0263]
steps:   8%|▊         | 849/10016 [3:02:28<32:50:11, 12.90s/it, avr_loss=0.0265]
steps:   8%|▊         | 850/10016 [3:02:40<32:49:58, 12.90s/it, avr_loss=0.0265]
steps:   8%|▊         | 850/10016 [3:02:40<32:49:58, 12.90s/it, avr_loss=0.0265]
steps:   8%|▊         | 851/10016 [3:02:53<32:49:44, 12.90s/it, avr_loss=0.0265]
steps:   8%|▊         | 851/10016 [3:02:53<32:49:44, 12.90s/it, avr_loss=0.0265]
steps:   9%|▊         | 852/10016 [3:03:06<32:49:31, 12.90s/it, avr_loss=0.0265]
steps:   9%|▊         | 852/10016 [3:03:06<32:49:31, 12.90s/it, avr_loss=0.0264]
steps:   9%|▊         | 853/10016 [3:03:19<32:49:18, 12.90s/it, avr_loss=0.0264]
steps:   9%|▊         | 853/10016 [3:03:19<32:49:18, 12.90s/it, avr_loss=0.0264]
steps:   9%|▊         | 854/10016 [3:03:32<32:49:05, 12.90s/it, avr_loss=0.0264]
steps:   9%|▊         | 854/10016 [3:03:32<32:49:05, 12.90s/it, avr_loss=0.0264]
steps:   9%|▊         | 855/10016 [3:03:45<32:48:52, 12.90s/it, avr_loss=0.0264]
steps:   9%|▊         | 855/10016 [3:03:45<32:48:52, 12.90s/it, avr_loss=0.0264]
steps:   9%|▊         | 856/10016 [3:03:58<32:48:39, 12.90s/it, avr_loss=0.0264]
steps:   9%|▊         | 856/10016 [3:03:58<32:48:39, 12.90s/it, avr_loss=0.0264]
steps:   9%|▊         | 857/10016 [3:04:11<32:48:28, 12.90s/it, avr_loss=0.0264]
steps:   9%|▊         | 857/10016 [3:04:11<32:48:28, 12.90s/it, avr_loss=0.0264]
steps:   9%|▊         | 858/10016 [3:04:24<32:48:15, 12.90s/it, avr_loss=0.0264]
steps:   9%|▊         | 858/10016 [3:04:24<32:48:15, 12.90s/it, avr_loss=0.0265]
steps:   9%|▊         | 859/10016 [3:04:37<32:48:02, 12.90s/it, avr_loss=0.0265]
steps:   9%|▊         | 859/10016 [3:04:37<32:48:02, 12.90s/it, avr_loss=0.0264]
steps:   9%|▊         | 860/10016 [3:04:49<32:47:49, 12.90s/it, avr_loss=0.0264]
steps:   9%|▊         | 860/10016 [3:04:49<32:47:49, 12.90s/it, avr_loss=0.0265]
steps:   9%|▊         | 861/10016 [3:05:02<32:47:36, 12.90s/it, avr_loss=0.0265]
steps:   9%|▊         | 861/10016 [3:05:02<32:47:36, 12.90s/it, avr_loss=0.0265]
steps:   9%|▊         | 862/10016 [3:05:15<32:47:22, 12.90s/it, avr_loss=0.0265]
steps:   9%|▊         | 862/10016 [3:05:15<32:47:22, 12.90s/it, avr_loss=0.0267]
steps:   9%|▊         | 863/10016 [3:05:28<32:47:09, 12.90s/it, avr_loss=0.0267]
steps:   9%|▊         | 863/10016 [3:05:28<32:47:09, 12.90s/it, avr_loss=0.0267]
steps:   9%|▊         | 864/10016 [3:05:41<32:46:57, 12.90s/it, avr_loss=0.0267]
steps:   9%|▊         | 864/10016 [3:05:41<32:46:57, 12.90s/it, avr_loss=0.0267]
steps:   9%|▊         | 865/10016 [3:05:54<32:46:44, 12.90s/it, avr_loss=0.0267]
steps:   9%|▊         | 865/10016 [3:05:54<32:46:44, 12.90s/it, avr_loss=0.0267]
steps:   9%|▊         | 866/10016 [3:06:07<32:46:31, 12.90s/it, avr_loss=0.0267]
steps:   9%|▊         | 866/10016 [3:06:07<32:46:31, 12.90s/it, avr_loss=0.0267]
steps:   9%|▊         | 867/10016 [3:06:20<32:46:18, 12.90s/it, avr_loss=0.0267]
steps:   9%|▊         | 867/10016 [3:06:20<32:46:18, 12.90s/it, avr_loss=0.0267]
steps:   9%|▊         | 868/10016 [3:06:33<32:46:05, 12.90s/it, avr_loss=0.0267]
steps:   9%|▊         | 868/10016 [3:06:33<32:46:05, 12.90s/it, avr_loss=0.0267]
steps:   9%|▊         | 869/10016 [3:06:45<32:45:52, 12.90s/it, avr_loss=0.0267]
steps:   9%|▊         | 869/10016 [3:06:45<32:45:52, 12.90s/it, avr_loss=0.0267]
steps:   9%|▊         | 870/10016 [3:06:58<32:45:39, 12.90s/it, avr_loss=0.0267]
steps:   9%|▊         | 870/10016 [3:06:58<32:45:39, 12.90s/it, avr_loss=0.0267]
steps:   9%|▊         | 871/10016 [3:07:11<32:45:26, 12.90s/it, avr_loss=0.0267]
steps:   9%|▊         | 871/10016 [3:07:11<32:45:26, 12.90s/it, avr_loss=0.0267]
steps:   9%|▊         | 872/10016 [3:07:24<32:45:13, 12.90s/it, avr_loss=0.0267]
steps:   9%|▊         | 872/10016 [3:07:24<32:45:13, 12.90s/it, avr_loss=0.0267]
steps:   9%|▊         | 873/10016 [3:07:37<32:45:00, 12.90s/it, avr_loss=0.0267]
steps:   9%|▊         | 873/10016 [3:07:37<32:45:00, 12.90s/it, avr_loss=0.0267]
steps:   9%|▊         | 874/10016 [3:07:50<32:44:47, 12.90s/it, avr_loss=0.0267]
steps:   9%|▊         | 874/10016 [3:07:50<32:44:47, 12.90s/it, avr_loss=0.0267]
steps:   9%|▊         | 875/10016 [3:08:03<32:44:34, 12.90s/it, avr_loss=0.0267]
steps:   9%|▊         | 875/10016 [3:08:03<32:44:34, 12.90s/it, avr_loss=0.0267]
steps:   9%|▊         | 876/10016 [3:08:16<32:44:22, 12.90s/it, avr_loss=0.0267]
steps:   9%|▊         | 876/10016 [3:08:16<32:44:22, 12.90s/it, avr_loss=0.0267]
steps:   9%|▉         | 877/10016 [3:08:29<32:44:09, 12.90s/it, avr_loss=0.0267]
steps:   9%|▉         | 877/10016 [3:08:29<32:44:09, 12.90s/it, avr_loss=0.0267]
steps:   9%|▉         | 878/10016 [3:08:41<32:43:56, 12.90s/it, avr_loss=0.0267]
steps:   9%|▉         | 878/10016 [3:08:41<32:43:56, 12.90s/it, avr_loss=0.0267]
steps:   9%|▉         | 879/10016 [3:08:54<32:43:43, 12.90s/it, avr_loss=0.0267]
steps:   9%|▉         | 879/10016 [3:08:54<32:43:43, 12.90s/it, avr_loss=0.0267]
steps:   9%|▉         | 880/10016 [3:09:07<32:43:30, 12.90s/it, avr_loss=0.0267]
steps:   9%|▉         | 880/10016 [3:09:07<32:43:30, 12.90s/it, avr_loss=0.0267]
steps:   9%|▉         | 881/10016 [3:09:20<32:43:17, 12.90s/it, avr_loss=0.0267]
steps:   9%|▉         | 881/10016 [3:09:20<32:43:17, 12.90s/it, avr_loss=0.0267]
steps:   9%|▉         | 882/10016 [3:09:33<32:43:06, 12.90s/it, avr_loss=0.0267]
steps:   9%|▉         | 882/10016 [3:09:33<32:43:06, 12.90s/it, avr_loss=0.0267]
steps:   9%|▉         | 883/10016 [3:09:46<32:42:53, 12.90s/it, avr_loss=0.0267]
steps:   9%|▉         | 883/10016 [3:09:46<32:42:53, 12.90s/it, avr_loss=0.0267]
steps:   9%|▉         | 884/10016 [3:09:59<32:42:40, 12.90s/it, avr_loss=0.0267]
steps:   9%|▉         | 884/10016 [3:09:59<32:42:40, 12.90s/it, avr_loss=0.0267]
steps:   9%|▉         | 885/10016 [3:10:12<32:42:27, 12.90s/it, avr_loss=0.0267]
steps:   9%|▉         | 885/10016 [3:10:12<32:42:27, 12.90s/it, avr_loss=0.0267]
steps:   9%|▉         | 886/10016 [3:10:25<32:42:14, 12.90s/it, avr_loss=0.0267]
steps:   9%|▉         | 886/10016 [3:10:25<32:42:14, 12.90s/it, avr_loss=0.0267]
steps:   9%|▉         | 887/10016 [3:10:38<32:42:01, 12.90s/it, avr_loss=0.0267]
steps:   9%|▉         | 887/10016 [3:10:38<32:42:01, 12.90s/it, avr_loss=0.0267]
steps:   9%|▉         | 888/10016 [3:10:50<32:41:48, 12.90s/it, avr_loss=0.0267]
steps:   9%|▉         | 888/10016 [3:10:50<32:41:48, 12.90s/it, avr_loss=0.0267]
steps:   9%|▉         | 889/10016 [3:11:03<32:41:34, 12.90s/it, avr_loss=0.0267]
steps:   9%|▉         | 889/10016 [3:11:03<32:41:34, 12.90s/it, avr_loss=0.0266]
steps:   9%|▉         | 890/10016 [3:11:16<32:41:22, 12.90s/it, avr_loss=0.0266]
steps:   9%|▉         | 890/10016 [3:11:16<32:41:22, 12.90s/it, avr_loss=0.0266]
steps:   9%|▉         | 891/10016 [3:11:29<32:41:09, 12.90s/it, avr_loss=0.0266]
steps:   9%|▉         | 891/10016 [3:11:29<32:41:09, 12.90s/it, avr_loss=0.0266]
steps:   9%|▉         | 892/10016 [3:11:42<32:40:56, 12.90s/it, avr_loss=0.0266]
steps:   9%|▉         | 892/10016 [3:11:42<32:40:56, 12.90s/it, avr_loss=0.0266]
steps:   9%|▉         | 893/10016 [3:11:55<32:40:43, 12.90s/it, avr_loss=0.0266]
steps:   9%|▉         | 893/10016 [3:11:55<32:40:43, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 894/10016 [3:12:08<32:40:30, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 894/10016 [3:12:08<32:40:30, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 895/10016 [3:12:21<32:40:17, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 895/10016 [3:12:21<32:40:17, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 896/10016 [3:12:34<32:40:04, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 896/10016 [3:12:34<32:40:04, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 897/10016 [3:12:47<32:39:51, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 897/10016 [3:12:47<32:39:51, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 898/10016 [3:12:59<32:39:38, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 898/10016 [3:12:59<32:39:38, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 899/10016 [3:13:12<32:39:25, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 899/10016 [3:13:12<32:39:25, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 900/10016 [3:13:25<32:39:12, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 900/10016 [3:13:25<32:39:12, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 901/10016 [3:13:38<32:38:59, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 901/10016 [3:13:38<32:38:59, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 902/10016 [3:13:51<32:38:46, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 902/10016 [3:13:51<32:38:46, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 903/10016 [3:14:04<32:38:33, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 903/10016 [3:14:04<32:38:33, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 904/10016 [3:14:17<32:38:20, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 904/10016 [3:14:17<32:38:20, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 905/10016 [3:14:30<32:38:08, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 905/10016 [3:14:30<32:38:08, 12.90s/it, avr_loss=0.0263]
steps:   9%|▉         | 906/10016 [3:14:43<32:37:55, 12.90s/it, avr_loss=0.0263]
steps:   9%|▉         | 906/10016 [3:14:43<32:37:55, 12.90s/it, avr_loss=0.0263]
steps:   9%|▉         | 907/10016 [3:14:55<32:37:42, 12.90s/it, avr_loss=0.0263]
steps:   9%|▉         | 907/10016 [3:14:55<32:37:42, 12.90s/it, avr_loss=0.0263]
steps:   9%|▉         | 908/10016 [3:15:08<32:37:29, 12.90s/it, avr_loss=0.0263]
steps:   9%|▉         | 908/10016 [3:15:08<32:37:29, 12.90s/it, avr_loss=0.0262]
steps:   9%|▉         | 909/10016 [3:15:21<32:37:16, 12.90s/it, avr_loss=0.0262]
steps:   9%|▉         | 909/10016 [3:15:21<32:37:16, 12.90s/it, avr_loss=0.0262]
steps:   9%|▉         | 910/10016 [3:15:34<32:37:03, 12.90s/it, avr_loss=0.0262]
steps:   9%|▉         | 910/10016 [3:15:34<32:37:03, 12.90s/it, avr_loss=0.0262]
steps:   9%|▉         | 911/10016 [3:15:47<32:36:51, 12.90s/it, avr_loss=0.0262]
steps:   9%|▉         | 911/10016 [3:15:47<32:36:51, 12.90s/it, avr_loss=0.0262]
steps:   9%|▉         | 912/10016 [3:16:00<32:36:38, 12.90s/it, avr_loss=0.0262]
steps:   9%|▉         | 912/10016 [3:16:00<32:36:38, 12.90s/it, avr_loss=0.0262]
steps:   9%|▉         | 913/10016 [3:16:13<32:36:25, 12.90s/it, avr_loss=0.0262]
steps:   9%|▉         | 913/10016 [3:16:13<32:36:25, 12.90s/it, avr_loss=0.0261]
steps:   9%|▉         | 914/10016 [3:16:26<32:36:12, 12.90s/it, avr_loss=0.0261]
steps:   9%|▉         | 914/10016 [3:16:26<32:36:12, 12.90s/it, avr_loss=0.0261]
steps:   9%|▉         | 915/10016 [3:16:39<32:35:59, 12.90s/it, avr_loss=0.0261]
steps:   9%|▉         | 915/10016 [3:16:39<32:35:59, 12.90s/it, avr_loss=0.0261]
steps:   9%|▉         | 916/10016 [3:16:52<32:35:46, 12.90s/it, avr_loss=0.0261]
steps:   9%|▉         | 916/10016 [3:16:52<32:35:46, 12.90s/it, avr_loss=0.0261]
steps:   9%|▉         | 917/10016 [3:17:04<32:35:33, 12.90s/it, avr_loss=0.0261]
steps:   9%|▉         | 917/10016 [3:17:04<32:35:33, 12.90s/it, avr_loss=0.0261]
steps:   9%|▉         | 918/10016 [3:17:17<32:35:20, 12.90s/it, avr_loss=0.0261]
steps:   9%|▉         | 918/10016 [3:17:17<32:35:20, 12.90s/it, avr_loss=0.0261]
steps:   9%|▉         | 919/10016 [3:17:30<32:35:07, 12.90s/it, avr_loss=0.0261]
steps:   9%|▉         | 919/10016 [3:17:30<32:35:07, 12.90s/it, avr_loss=0.0261]
steps:   9%|▉         | 920/10016 [3:17:43<32:34:54, 12.90s/it, avr_loss=0.0261]
steps:   9%|▉         | 920/10016 [3:17:43<32:34:54, 12.90s/it, avr_loss=0.0261]
steps:   9%|▉         | 921/10016 [3:17:56<32:34:41, 12.90s/it, avr_loss=0.0261]
steps:   9%|▉         | 921/10016 [3:17:56<32:34:41, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 922/10016 [3:18:09<32:34:28, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 922/10016 [3:18:09<32:34:28, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 923/10016 [3:18:22<32:34:15, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 923/10016 [3:18:22<32:34:15, 12.90s/it, avr_loss=0.0264]
steps:   9%|▉         | 924/10016 [3:18:35<32:34:02, 12.90s/it, avr_loss=0.0264]
steps:   9%|▉         | 924/10016 [3:18:35<32:34:02, 12.90s/it, avr_loss=0.0264]
steps:   9%|▉         | 925/10016 [3:18:47<32:33:49, 12.90s/it, avr_loss=0.0264]
steps:   9%|▉         | 925/10016 [3:18:47<32:33:49, 12.90s/it, avr_loss=0.0264]
steps:   9%|▉         | 926/10016 [3:19:00<32:33:36, 12.90s/it, avr_loss=0.0264]
steps:   9%|▉         | 926/10016 [3:19:00<32:33:36, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 927/10016 [3:19:13<32:33:23, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 927/10016 [3:19:13<32:33:23, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 928/10016 [3:19:26<32:33:11, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 928/10016 [3:19:26<32:33:11, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 929/10016 [3:19:39<32:32:58, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 929/10016 [3:19:39<32:32:58, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 930/10016 [3:19:52<32:32:45, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 930/10016 [3:19:52<32:32:45, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 931/10016 [3:20:05<32:32:32, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 931/10016 [3:20:05<32:32:32, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 932/10016 [3:20:18<32:32:19, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 932/10016 [3:20:18<32:32:19, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 933/10016 [3:20:31<32:32:06, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 933/10016 [3:20:31<32:32:06, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 934/10016 [3:20:44<32:31:53, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 934/10016 [3:20:44<32:31:53, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 935/10016 [3:20:56<32:31:40, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 935/10016 [3:20:56<32:31:40, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 936/10016 [3:21:09<32:31:27, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 936/10016 [3:21:09<32:31:27, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 937/10016 [3:21:22<32:31:14, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 937/10016 [3:21:22<32:31:14, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 938/10016 [3:21:35<32:31:01, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 938/10016 [3:21:35<32:31:01, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 939/10016 [3:21:48<32:30:48, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 939/10016 [3:21:48<32:30:48, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 940/10016 [3:22:01<32:30:35, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 940/10016 [3:22:01<32:30:35, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 941/10016 [3:22:14<32:30:23, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 941/10016 [3:22:14<32:30:23, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 942/10016 [3:22:27<32:30:10, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 942/10016 [3:22:27<32:30:10, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 943/10016 [3:22:40<32:29:57, 12.90s/it, avr_loss=0.0265]
steps:   9%|▉         | 943/10016 [3:22:40<32:29:57, 12.90s/it, avr_loss=0.0264]
steps:   9%|▉         | 944/10016 [3:22:52<32:29:44, 12.90s/it, avr_loss=0.0264]
steps:   9%|▉         | 944/10016 [3:22:52<32:29:44, 12.90s/it, avr_loss=0.0264]
steps:   9%|▉         | 945/10016 [3:23:05<32:29:31, 12.90s/it, avr_loss=0.0264]
steps:   9%|▉         | 945/10016 [3:23:05<32:29:31, 12.90s/it, avr_loss=0.0264]
steps:   9%|▉         | 946/10016 [3:23:18<32:29:18, 12.90s/it, avr_loss=0.0264]
steps:   9%|▉         | 946/10016 [3:23:18<32:29:18, 12.90s/it, avr_loss=0.0264]
steps:   9%|▉         | 947/10016 [3:23:31<32:29:05, 12.90s/it, avr_loss=0.0264]
steps:   9%|▉         | 947/10016 [3:23:31<32:29:05, 12.90s/it, avr_loss=0.0264]
steps:   9%|▉         | 948/10016 [3:23:44<32:28:52, 12.90s/it, avr_loss=0.0264]
steps:   9%|▉         | 948/10016 [3:23:44<32:28:52, 12.90s/it, avr_loss=0.0264]
steps:   9%|▉         | 949/10016 [3:23:57<32:28:39, 12.90s/it, avr_loss=0.0264]
steps:   9%|▉         | 949/10016 [3:23:57<32:28:39, 12.90s/it, avr_loss=0.0264]
steps:   9%|▉         | 950/10016 [3:24:10<32:28:26, 12.90s/it, avr_loss=0.0264]
steps:   9%|▉         | 950/10016 [3:24:10<32:28:26, 12.90s/it, avr_loss=0.0263]
steps:   9%|▉         | 951/10016 [3:24:23<32:28:13, 12.90s/it, avr_loss=0.0263]
steps:   9%|▉         | 951/10016 [3:24:23<32:28:13, 12.90s/it, avr_loss=0.0263]
steps:  10%|▉         | 952/10016 [3:24:36<32:28:00, 12.90s/it, avr_loss=0.0263]
steps:  10%|▉         | 952/10016 [3:24:36<32:28:00, 12.90s/it, avr_loss=0.0262]
steps:  10%|▉         | 953/10016 [3:24:48<32:27:47, 12.90s/it, avr_loss=0.0262]
steps:  10%|▉         | 953/10016 [3:24:48<32:27:47, 12.90s/it, avr_loss=0.0262]
steps:  10%|▉         | 954/10016 [3:25:01<32:27:34, 12.90s/it, avr_loss=0.0262]
steps:  10%|▉         | 954/10016 [3:25:01<32:27:34, 12.90s/it, avr_loss=0.0261]
steps:  10%|▉         | 955/10016 [3:25:14<32:27:21, 12.90s/it, avr_loss=0.0261]
steps:  10%|▉         | 955/10016 [3:25:14<32:27:21, 12.90s/it, avr_loss=0.0261]
steps:  10%|▉         | 956/10016 [3:25:27<32:27:08, 12.90s/it, avr_loss=0.0261]
steps:  10%|▉         | 956/10016 [3:25:27<32:27:08, 12.90s/it, avr_loss=0.0261]
steps:  10%|▉         | 957/10016 [3:25:40<32:26:56, 12.90s/it, avr_loss=0.0261]
steps:  10%|▉         | 957/10016 [3:25:40<32:26:56, 12.90s/it, avr_loss=0.0261]
steps:  10%|▉         | 958/10016 [3:25:53<32:26:43, 12.90s/it, avr_loss=0.0261]
steps:  10%|▉         | 958/10016 [3:25:53<32:26:43, 12.90s/it, avr_loss=0.026] 
steps:  10%|▉         | 959/10016 [3:26:06<32:26:30, 12.90s/it, avr_loss=0.026]
steps:  10%|▉         | 959/10016 [3:26:06<32:26:30, 12.90s/it, avr_loss=0.026]
steps:  10%|▉         | 960/10016 [3:26:19<32:26:17, 12.90s/it, avr_loss=0.026]
steps:  10%|▉         | 960/10016 [3:26:19<32:26:17, 12.90s/it, avr_loss=0.026]
steps:  10%|▉         | 961/10016 [3:26:32<32:26:04, 12.90s/it, avr_loss=0.026]
steps:  10%|▉         | 961/10016 [3:26:32<32:26:04, 12.90s/it, avr_loss=0.026]
steps:  10%|▉         | 962/10016 [3:26:44<32:25:51, 12.89s/it, avr_loss=0.026]
steps:  10%|▉         | 962/10016 [3:26:44<32:25:51, 12.89s/it, avr_loss=0.026]
steps:  10%|▉         | 963/10016 [3:26:57<32:25:38, 12.89s/it, avr_loss=0.026]
steps:  10%|▉         | 963/10016 [3:26:57<32:25:38, 12.89s/it, avr_loss=0.026]
steps:  10%|▉         | 964/10016 [3:27:10<32:25:25, 12.89s/it, avr_loss=0.026]
steps:  10%|▉         | 964/10016 [3:27:10<32:25:25, 12.89s/it, avr_loss=0.026]
steps:  10%|▉         | 965/10016 [3:27:23<32:25:12, 12.89s/it, avr_loss=0.026]
steps:  10%|▉         | 965/10016 [3:27:23<32:25:12, 12.89s/it, avr_loss=0.026]
steps:  10%|▉         | 966/10016 [3:27:36<32:25:00, 12.90s/it, avr_loss=0.026]
steps:  10%|▉         | 966/10016 [3:27:36<32:25:00, 12.90s/it, avr_loss=0.026]
steps:  10%|▉         | 967/10016 [3:27:49<32:24:47, 12.90s/it, avr_loss=0.026]
steps:  10%|▉         | 967/10016 [3:27:49<32:24:47, 12.90s/it, avr_loss=0.026]
steps:  10%|▉         | 968/10016 [3:28:02<32:24:34, 12.90s/it, avr_loss=0.026]
steps:  10%|▉         | 968/10016 [3:28:02<32:24:34, 12.90s/it, avr_loss=0.026]
steps:  10%|▉         | 969/10016 [3:28:15<32:24:21, 12.90s/it, avr_loss=0.026]
steps:  10%|▉         | 969/10016 [3:28:15<32:24:21, 12.90s/it, avr_loss=0.026]
steps:  10%|▉         | 970/10016 [3:28:28<32:24:09, 12.90s/it, avr_loss=0.026]
steps:  10%|▉         | 970/10016 [3:28:28<32:24:09, 12.90s/it, avr_loss=0.0259]
steps:  10%|▉         | 971/10016 [3:28:41<32:23:56, 12.90s/it, avr_loss=0.0259]
steps:  10%|▉         | 971/10016 [3:28:41<32:23:56, 12.90s/it, avr_loss=0.0259]
steps:  10%|▉         | 972/10016 [3:28:54<32:23:44, 12.90s/it, avr_loss=0.0259]
steps:  10%|▉         | 972/10016 [3:28:54<32:23:44, 12.90s/it, avr_loss=0.026] 
steps:  10%|▉         | 973/10016 [3:29:07<32:23:31, 12.90s/it, avr_loss=0.026]
steps:  10%|▉         | 973/10016 [3:29:07<32:23:31, 12.90s/it, avr_loss=0.0259]
steps:  10%|▉         | 974/10016 [3:29:19<32:23:18, 12.90s/it, avr_loss=0.0259]
steps:  10%|▉         | 974/10016 [3:29:19<32:23:18, 12.90s/it, avr_loss=0.026] 
steps:  10%|▉         | 975/10016 [3:29:32<32:23:05, 12.90s/it, avr_loss=0.026]
steps:  10%|▉         | 975/10016 [3:29:32<32:23:05, 12.90s/it, avr_loss=0.026]
steps:  10%|▉         | 976/10016 [3:29:45<32:22:52, 12.90s/it, avr_loss=0.026]
steps:  10%|▉         | 976/10016 [3:29:45<32:22:52, 12.90s/it, avr_loss=0.0259]
steps:  10%|▉         | 977/10016 [3:29:58<32:22:39, 12.90s/it, avr_loss=0.0259]
steps:  10%|▉         | 977/10016 [3:29:58<32:22:39, 12.90s/it, avr_loss=0.0259]
steps:  10%|▉         | 978/10016 [3:30:11<32:22:27, 12.90s/it, avr_loss=0.0259]
steps:  10%|▉         | 978/10016 [3:30:11<32:22:27, 12.90s/it, avr_loss=0.0259]
steps:  10%|▉         | 979/10016 [3:30:24<32:22:14, 12.90s/it, avr_loss=0.0259]
steps:  10%|▉         | 979/10016 [3:30:24<32:22:14, 12.90s/it, avr_loss=0.0259]
steps:  10%|▉         | 980/10016 [3:30:37<32:22:01, 12.90s/it, avr_loss=0.0259]
steps:  10%|▉         | 980/10016 [3:30:37<32:22:01, 12.90s/it, avr_loss=0.0259]
steps:  10%|▉         | 981/10016 [3:30:50<32:21:48, 12.90s/it, avr_loss=0.0259]
steps:  10%|▉         | 981/10016 [3:30:50<32:21:48, 12.90s/it, avr_loss=0.0259]
steps:  10%|▉         | 982/10016 [3:31:03<32:21:35, 12.90s/it, avr_loss=0.0259]
steps:  10%|▉         | 982/10016 [3:31:03<32:21:35, 12.90s/it, avr_loss=0.0259]
steps:  10%|▉         | 983/10016 [3:31:15<32:21:22, 12.90s/it, avr_loss=0.0259]
steps:  10%|▉         | 983/10016 [3:31:15<32:21:22, 12.90s/it, avr_loss=0.0259]
steps:  10%|▉         | 984/10016 [3:31:28<32:21:09, 12.90s/it, avr_loss=0.0259]
steps:  10%|▉         | 984/10016 [3:31:28<32:21:09, 12.90s/it, avr_loss=0.026] 
steps:  10%|▉         | 985/10016 [3:31:41<32:20:56, 12.90s/it, avr_loss=0.026]
steps:  10%|▉         | 985/10016 [3:31:41<32:20:56, 12.90s/it, avr_loss=0.026]
steps:  10%|▉         | 986/10016 [3:31:54<32:20:43, 12.90s/it, avr_loss=0.026]
steps:  10%|▉         | 986/10016 [3:31:54<32:20:43, 12.90s/it, avr_loss=0.026]
steps:  10%|▉         | 987/10016 [3:32:07<32:20:31, 12.90s/it, avr_loss=0.026]
steps:  10%|▉         | 987/10016 [3:32:07<32:20:31, 12.90s/it, avr_loss=0.0257]
steps:  10%|▉         | 988/10016 [3:32:20<32:20:18, 12.90s/it, avr_loss=0.0257]
steps:  10%|▉         | 988/10016 [3:32:20<32:20:18, 12.90s/it, avr_loss=0.0257]
steps:  10%|▉         | 989/10016 [3:32:33<32:20:05, 12.90s/it, avr_loss=0.0257]
steps:  10%|▉         | 989/10016 [3:32:33<32:20:05, 12.90s/it, avr_loss=0.0257]
steps:  10%|▉         | 990/10016 [3:32:46<32:19:52, 12.90s/it, avr_loss=0.0257]
steps:  10%|▉         | 990/10016 [3:32:46<32:19:52, 12.90s/it, avr_loss=0.0258]
steps:  10%|▉         | 991/10016 [3:32:59<32:19:41, 12.90s/it, avr_loss=0.0258]
steps:  10%|▉         | 991/10016 [3:32:59<32:19:41, 12.90s/it, avr_loss=0.0258]
steps:  10%|▉         | 992/10016 [3:33:12<32:19:28, 12.90s/it, avr_loss=0.0258]
steps:  10%|▉         | 992/10016 [3:33:12<32:19:28, 12.90s/it, avr_loss=0.0258]
steps:  10%|▉         | 993/10016 [3:33:25<32:19:15, 12.90s/it, avr_loss=0.0258]
steps:  10%|▉         | 993/10016 [3:33:25<32:19:15, 12.90s/it, avr_loss=0.0259]
steps:  10%|▉         | 994/10016 [3:33:38<32:19:02, 12.90s/it, avr_loss=0.0259]
steps:  10%|▉         | 994/10016 [3:33:38<32:19:02, 12.90s/it, avr_loss=0.0258]
steps:  10%|▉         | 995/10016 [3:33:50<32:18:49, 12.90s/it, avr_loss=0.0258]
steps:  10%|▉         | 995/10016 [3:33:50<32:18:49, 12.90s/it, avr_loss=0.0258]
steps:  10%|▉         | 996/10016 [3:34:03<32:18:36, 12.90s/it, avr_loss=0.0258]
steps:  10%|▉         | 996/10016 [3:34:03<32:18:36, 12.90s/it, avr_loss=0.0258]
steps:  10%|▉         | 997/10016 [3:34:16<32:18:24, 12.90s/it, avr_loss=0.0258]
steps:  10%|▉         | 997/10016 [3:34:16<32:18:24, 12.90s/it, avr_loss=0.0258]
steps:  10%|▉         | 998/10016 [3:34:29<32:18:11, 12.90s/it, avr_loss=0.0258]
steps:  10%|▉         | 998/10016 [3:34:29<32:18:11, 12.90s/it, avr_loss=0.0258]
steps:  10%|▉         | 999/10016 [3:34:42<32:17:58, 12.90s/it, avr_loss=0.0258]
steps:  10%|▉         | 999/10016 [3:34:42<32:17:58, 12.90s/it, avr_loss=0.0259]
steps:  10%|▉         | 1000/10016 [3:34:55<32:17:45, 12.90s/it, avr_loss=0.0259]
steps:  10%|▉         | 1000/10016 [3:34:55<32:17:45, 12.90s/it, avr_loss=0.0259]
steps:  10%|▉         | 1001/10016 [3:35:08<32:17:32, 12.90s/it, avr_loss=0.0259]
steps:  10%|▉         | 1001/10016 [3:35:08<32:17:32, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1002/10016 [3:35:21<32:17:19, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1002/10016 [3:35:21<32:17:19, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1003/10016 [3:35:34<32:17:07, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1003/10016 [3:35:34<32:17:07, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1004/10016 [3:35:47<32:16:54, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1004/10016 [3:35:47<32:16:54, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1005/10016 [3:36:00<32:16:41, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1005/10016 [3:36:00<32:16:41, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1006/10016 [3:36:12<32:16:28, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1006/10016 [3:36:12<32:16:28, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1007/10016 [3:36:25<32:16:16, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1007/10016 [3:36:25<32:16:16, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1008/10016 [3:36:38<32:16:03, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1008/10016 [3:36:38<32:16:03, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1009/10016 [3:36:51<32:15:50, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1009/10016 [3:36:51<32:15:50, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1010/10016 [3:37:04<32:15:37, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1010/10016 [3:37:04<32:15:37, 12.90s/it, avr_loss=0.0258]
steps:  10%|█         | 1011/10016 [3:37:17<32:15:24, 12.90s/it, avr_loss=0.0258]
steps:  10%|█         | 1011/10016 [3:37:17<32:15:24, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1012/10016 [3:37:30<32:15:11, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1012/10016 [3:37:30<32:15:11, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1013/10016 [3:37:43<32:14:59, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1013/10016 [3:37:43<32:14:59, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1014/10016 [3:37:56<32:14:46, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1014/10016 [3:37:56<32:14:46, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1015/10016 [3:38:09<32:14:33, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1015/10016 [3:38:09<32:14:33, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1016/10016 [3:38:21<32:14:20, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1016/10016 [3:38:21<32:14:20, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1017/10016 [3:38:34<32:14:07, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1017/10016 [3:38:34<32:14:07, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1018/10016 [3:38:47<32:13:54, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1018/10016 [3:38:47<32:13:54, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1019/10016 [3:39:00<32:13:42, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1019/10016 [3:39:00<32:13:42, 12.90s/it, avr_loss=0.026] 
steps:  10%|█         | 1020/10016 [3:39:13<32:13:29, 12.90s/it, avr_loss=0.026]
steps:  10%|█         | 1020/10016 [3:39:13<32:13:29, 12.90s/it, avr_loss=0.026]
steps:  10%|█         | 1021/10016 [3:39:26<32:13:16, 12.90s/it, avr_loss=0.026]
steps:  10%|█         | 1021/10016 [3:39:26<32:13:16, 12.90s/it, avr_loss=0.0258]
steps:  10%|█         | 1022/10016 [3:39:39<32:13:03, 12.90s/it, avr_loss=0.0258]
steps:  10%|█         | 1022/10016 [3:39:39<32:13:03, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1023/10016 [3:39:52<32:12:50, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1023/10016 [3:39:52<32:12:50, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1024/10016 [3:40:05<32:12:37, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1024/10016 [3:40:05<32:12:37, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1025/10016 [3:40:18<32:12:24, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1025/10016 [3:40:18<32:12:24, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1026/10016 [3:40:30<32:12:12, 12.90s/it, avr_loss=0.0259]
steps:  10%|█         | 1026/10016 [3:40:30<32:12:12, 12.90s/it, avr_loss=0.0258]
steps:  10%|█         | 1027/10016 [3:40:43<32:11:59, 12.90s/it, avr_loss=0.0258]
steps:  10%|█         | 1027/10016 [3:40:43<32:11:59, 12.90s/it, avr_loss=0.0258]
steps:  10%|█         | 1028/10016 [3:40:56<32:11:46, 12.90s/it, avr_loss=0.0258]
steps:  10%|█         | 1028/10016 [3:40:56<32:11:46, 12.90s/it, avr_loss=0.0258]
steps:  10%|█         | 1029/10016 [3:41:09<32:11:33, 12.90s/it, avr_loss=0.0258]
steps:  10%|█         | 1029/10016 [3:41:09<32:11:33, 12.90s/it, avr_loss=0.0258]
steps:  10%|█         | 1030/10016 [3:41:22<32:11:20, 12.90s/it, avr_loss=0.0258]
steps:  10%|█         | 1030/10016 [3:41:22<32:11:20, 12.90s/it, avr_loss=0.0258]
steps:  10%|█         | 1031/10016 [3:41:35<32:11:08, 12.90s/it, avr_loss=0.0258]
steps:  10%|█         | 1031/10016 [3:41:35<32:11:08, 12.90s/it, avr_loss=0.0258]
steps:  10%|█         | 1032/10016 [3:41:48<32:10:55, 12.90s/it, avr_loss=0.0258]
steps:  10%|█         | 1032/10016 [3:41:48<32:10:55, 12.90s/it, avr_loss=0.0258]
steps:  10%|█         | 1033/10016 [3:42:01<32:10:42, 12.90s/it, avr_loss=0.0258]
steps:  10%|█         | 1033/10016 [3:42:01<32:10:42, 12.90s/it, avr_loss=0.0258]
steps:  10%|█         | 1034/10016 [3:42:14<32:10:29, 12.90s/it, avr_loss=0.0258]
steps:  10%|█         | 1034/10016 [3:42:14<32:10:29, 12.90s/it, avr_loss=0.0258]
steps:  10%|█         | 1035/10016 [3:42:27<32:10:16, 12.90s/it, avr_loss=0.0258]
steps:  10%|█         | 1035/10016 [3:42:27<32:10:16, 12.90s/it, avr_loss=0.0258]
steps:  10%|█         | 1036/10016 [3:42:40<32:10:04, 12.90s/it, avr_loss=0.0258]
steps:  10%|█         | 1036/10016 [3:42:40<32:10:04, 12.90s/it, avr_loss=0.0258]
steps:  10%|█         | 1037/10016 [3:42:52<32:09:51, 12.90s/it, avr_loss=0.0258]
steps:  10%|█         | 1037/10016 [3:42:52<32:09:51, 12.90s/it, avr_loss=0.0258]
steps:  10%|█         | 1038/10016 [3:43:05<32:09:38, 12.90s/it, avr_loss=0.0258]
steps:  10%|█         | 1038/10016 [3:43:05<32:09:38, 12.90s/it, avr_loss=0.0258]
steps:  10%|█         | 1039/10016 [3:43:18<32:09:25, 12.90s/it, avr_loss=0.0258]
steps:  10%|█         | 1039/10016 [3:43:18<32:09:25, 12.90s/it, avr_loss=0.0257]
steps:  10%|█         | 1040/10016 [3:43:31<32:09:12, 12.90s/it, avr_loss=0.0257]
steps:  10%|█         | 1040/10016 [3:43:31<32:09:12, 12.90s/it, avr_loss=0.0257]
steps:  10%|█         | 1041/10016 [3:43:44<32:08:59, 12.90s/it, avr_loss=0.0257]
steps:  10%|█         | 1041/10016 [3:43:44<32:08:59, 12.90s/it, avr_loss=0.0257]
steps:  10%|█         | 1042/10016 [3:43:57<32:08:47, 12.90s/it, avr_loss=0.0257]
steps:  10%|█         | 1042/10016 [3:43:57<32:08:47, 12.90s/it, avr_loss=0.0257]
steps:  10%|█         | 1043/10016 [3:44:10<32:08:34, 12.90s/it, avr_loss=0.0257]
steps:  10%|█         | 1043/10016 [3:44:10<32:08:34, 12.90s/it, avr_loss=0.0257]
steps:  10%|█         | 1044/10016 [3:44:23<32:08:21, 12.90s/it, avr_loss=0.0257]
steps:  10%|█         | 1044/10016 [3:44:23<32:08:21, 12.90s/it, avr_loss=0.0257]
steps:  10%|█         | 1045/10016 [3:44:36<32:08:08, 12.90s/it, avr_loss=0.0257]
steps:  10%|█         | 1045/10016 [3:44:36<32:08:08, 12.90s/it, avr_loss=0.0257]
steps:  10%|█         | 1046/10016 [3:44:49<32:07:55, 12.90s/it, avr_loss=0.0257]
steps:  10%|█         | 1046/10016 [3:44:49<32:07:55, 12.90s/it, avr_loss=0.0257]
steps:  10%|█         | 1047/10016 [3:45:01<32:07:43, 12.90s/it, avr_loss=0.0257]
steps:  10%|█         | 1047/10016 [3:45:01<32:07:43, 12.90s/it, avr_loss=0.0257]
steps:  10%|█         | 1048/10016 [3:45:14<32:07:30, 12.90s/it, avr_loss=0.0257]
steps:  10%|█         | 1048/10016 [3:45:14<32:07:30, 12.90s/it, avr_loss=0.0257]
steps:  10%|█         | 1049/10016 [3:45:27<32:07:17, 12.90s/it, avr_loss=0.0257]
steps:  10%|█         | 1049/10016 [3:45:27<32:07:17, 12.90s/it, avr_loss=0.0257]
steps:  10%|█         | 1050/10016 [3:45:40<32:07:04, 12.90s/it, avr_loss=0.0257]
steps:  10%|█         | 1050/10016 [3:45:40<32:07:04, 12.90s/it, avr_loss=0.0258]
steps:  10%|█         | 1051/10016 [3:45:53<32:06:52, 12.90s/it, avr_loss=0.0258]
steps:  10%|█         | 1051/10016 [3:45:53<32:06:52, 12.90s/it, avr_loss=0.0258]
steps:  11%|█         | 1052/10016 [3:46:06<32:06:39, 12.90s/it, avr_loss=0.0258]
steps:  11%|█         | 1052/10016 [3:46:06<32:06:39, 12.90s/it, avr_loss=0.0258]
steps:  11%|█         | 1053/10016 [3:46:19<32:06:26, 12.90s/it, avr_loss=0.0258]
steps:  11%|█         | 1053/10016 [3:46:19<32:06:26, 12.90s/it, avr_loss=0.0257]
steps:  11%|█         | 1054/10016 [3:46:32<32:06:13, 12.90s/it, avr_loss=0.0257]
steps:  11%|█         | 1054/10016 [3:46:32<32:06:13, 12.90s/it, avr_loss=0.0257]
steps:  11%|█         | 1055/10016 [3:46:45<32:06:00, 12.90s/it, avr_loss=0.0257]
steps:  11%|█         | 1055/10016 [3:46:45<32:06:00, 12.90s/it, avr_loss=0.0257]
steps:  11%|█         | 1056/10016 [3:46:58<32:05:48, 12.90s/it, avr_loss=0.0257]
steps:  11%|█         | 1056/10016 [3:46:58<32:05:48, 12.90s/it, avr_loss=0.0258]
steps:  11%|█         | 1057/10016 [3:47:11<32:05:35, 12.90s/it, avr_loss=0.0258]
steps:  11%|█         | 1057/10016 [3:47:11<32:05:35, 12.90s/it, avr_loss=0.026] 
steps:  11%|█         | 1058/10016 [3:47:23<32:05:22, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1058/10016 [3:47:23<32:05:22, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1059/10016 [3:47:36<32:05:09, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1059/10016 [3:47:36<32:05:09, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1060/10016 [3:47:49<32:04:56, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1060/10016 [3:47:49<32:04:56, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1061/10016 [3:48:02<32:04:43, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1061/10016 [3:48:02<32:04:43, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1062/10016 [3:48:15<32:04:30, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1062/10016 [3:48:15<32:04:30, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1063/10016 [3:48:28<32:04:18, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1063/10016 [3:48:28<32:04:18, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1064/10016 [3:48:41<32:04:05, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1064/10016 [3:48:41<32:04:05, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1065/10016 [3:48:54<32:03:52, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1065/10016 [3:48:54<32:03:52, 12.90s/it, avr_loss=0.0259]
steps:  11%|█         | 1066/10016 [3:49:07<32:03:39, 12.90s/it, avr_loss=0.0259]
steps:  11%|█         | 1066/10016 [3:49:07<32:03:39, 12.90s/it, avr_loss=0.0259]
steps:  11%|█         | 1067/10016 [3:49:20<32:03:26, 12.90s/it, avr_loss=0.0259]
steps:  11%|█         | 1067/10016 [3:49:20<32:03:26, 12.90s/it, avr_loss=0.026] 
steps:  11%|█         | 1068/10016 [3:49:32<32:03:14, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1068/10016 [3:49:32<32:03:14, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1069/10016 [3:49:45<32:03:01, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1069/10016 [3:49:45<32:03:01, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1070/10016 [3:49:58<32:02:48, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1070/10016 [3:49:58<32:02:48, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1071/10016 [3:50:11<32:02:35, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1071/10016 [3:50:11<32:02:35, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1072/10016 [3:50:24<32:02:22, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1072/10016 [3:50:24<32:02:22, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1073/10016 [3:50:37<32:02:09, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1073/10016 [3:50:37<32:02:09, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1074/10016 [3:50:50<32:01:57, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1074/10016 [3:50:50<32:01:57, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1075/10016 [3:51:03<32:01:44, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1075/10016 [3:51:03<32:01:44, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1076/10016 [3:51:16<32:01:31, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1076/10016 [3:51:16<32:01:31, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1077/10016 [3:51:29<32:01:18, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1077/10016 [3:51:29<32:01:18, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1078/10016 [3:51:42<32:01:05, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1078/10016 [3:51:42<32:01:05, 12.90s/it, avr_loss=0.0259]
steps:  11%|█         | 1079/10016 [3:51:54<32:00:52, 12.90s/it, avr_loss=0.0259]
steps:  11%|█         | 1079/10016 [3:51:54<32:00:52, 12.90s/it, avr_loss=0.026] 
steps:  11%|█         | 1080/10016 [3:52:07<32:00:40, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1080/10016 [3:52:07<32:00:40, 12.90s/it, avr_loss=0.0259]
steps:  11%|█         | 1081/10016 [3:52:20<32:00:27, 12.90s/it, avr_loss=0.0259]
steps:  11%|█         | 1081/10016 [3:52:20<32:00:27, 12.90s/it, avr_loss=0.0258]
steps:  11%|█         | 1082/10016 [3:52:33<32:00:14, 12.90s/it, avr_loss=0.0258]
steps:  11%|█         | 1082/10016 [3:52:33<32:00:14, 12.90s/it, avr_loss=0.0259]
steps:  11%|█         | 1083/10016 [3:52:46<32:00:01, 12.90s/it, avr_loss=0.0259]
steps:  11%|█         | 1083/10016 [3:52:46<32:00:01, 12.90s/it, avr_loss=0.0259]
steps:  11%|█         | 1084/10016 [3:52:59<31:59:48, 12.90s/it, avr_loss=0.0259]
steps:  11%|█         | 1084/10016 [3:52:59<31:59:48, 12.90s/it, avr_loss=0.0259]
steps:  11%|█         | 1085/10016 [3:53:12<31:59:36, 12.90s/it, avr_loss=0.0259]
steps:  11%|█         | 1085/10016 [3:53:12<31:59:36, 12.90s/it, avr_loss=0.0259]
steps:  11%|█         | 1086/10016 [3:53:25<31:59:23, 12.90s/it, avr_loss=0.0259]
steps:  11%|█         | 1086/10016 [3:53:25<31:59:23, 12.90s/it, avr_loss=0.0258]
steps:  11%|█         | 1087/10016 [3:53:38<31:59:10, 12.90s/it, avr_loss=0.0258]
steps:  11%|█         | 1087/10016 [3:53:38<31:59:10, 12.90s/it, avr_loss=0.0258]
steps:  11%|█         | 1088/10016 [3:53:51<31:58:57, 12.90s/it, avr_loss=0.0258]
steps:  11%|█         | 1088/10016 [3:53:51<31:58:57, 12.90s/it, avr_loss=0.0258]
steps:  11%|█         | 1089/10016 [3:54:04<31:58:44, 12.90s/it, avr_loss=0.0258]
steps:  11%|█         | 1089/10016 [3:54:04<31:58:44, 12.90s/it, avr_loss=0.0258]
steps:  11%|█         | 1090/10016 [3:54:16<31:58:31, 12.90s/it, avr_loss=0.0258]
steps:  11%|█         | 1090/10016 [3:54:16<31:58:31, 12.90s/it, avr_loss=0.0259]
steps:  11%|█         | 1091/10016 [3:54:29<31:58:19, 12.90s/it, avr_loss=0.0259]
steps:  11%|█         | 1091/10016 [3:54:29<31:58:19, 12.90s/it, avr_loss=0.0259]
steps:  11%|█         | 1092/10016 [3:54:42<31:58:06, 12.90s/it, avr_loss=0.0259]
steps:  11%|█         | 1092/10016 [3:54:42<31:58:06, 12.90s/it, avr_loss=0.0259]
steps:  11%|█         | 1093/10016 [3:54:55<31:57:53, 12.90s/it, avr_loss=0.0259]
steps:  11%|█         | 1093/10016 [3:54:55<31:57:53, 12.90s/it, avr_loss=0.0259]
steps:  11%|█         | 1094/10016 [3:55:08<31:57:41, 12.90s/it, avr_loss=0.0259]
steps:  11%|█         | 1094/10016 [3:55:08<31:57:41, 12.90s/it, avr_loss=0.0259]
steps:  11%|█         | 1095/10016 [3:55:21<31:57:28, 12.90s/it, avr_loss=0.0259]
steps:  11%|█         | 1095/10016 [3:55:21<31:57:28, 12.90s/it, avr_loss=0.0259]
steps:  11%|█         | 1096/10016 [3:55:34<31:57:15, 12.90s/it, avr_loss=0.0259]
steps:  11%|█         | 1096/10016 [3:55:34<31:57:15, 12.90s/it, avr_loss=0.0259]
steps:  11%|█         | 1097/10016 [3:55:47<31:57:02, 12.90s/it, avr_loss=0.0259]
steps:  11%|█         | 1097/10016 [3:55:47<31:57:02, 12.90s/it, avr_loss=0.0259]
steps:  11%|█         | 1098/10016 [3:56:00<31:56:49, 12.90s/it, avr_loss=0.0259]
steps:  11%|█         | 1098/10016 [3:56:00<31:56:49, 12.90s/it, avr_loss=0.026] 
steps:  11%|█         | 1099/10016 [3:56:13<31:56:37, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1099/10016 [3:56:13<31:56:37, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1100/10016 [3:56:26<31:56:25, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1100/10016 [3:56:26<31:56:25, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1101/10016 [3:56:39<31:56:12, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1101/10016 [3:56:39<31:56:12, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1102/10016 [3:56:51<31:55:59, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1102/10016 [3:56:51<31:55:59, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1103/10016 [3:57:04<31:55:46, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1103/10016 [3:57:04<31:55:46, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1104/10016 [3:57:17<31:55:34, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1104/10016 [3:57:17<31:55:34, 12.90s/it, avr_loss=0.0261]
steps:  11%|█         | 1105/10016 [3:57:30<31:55:21, 12.90s/it, avr_loss=0.0261]
steps:  11%|█         | 1105/10016 [3:57:30<31:55:21, 12.90s/it, avr_loss=0.0261]
steps:  11%|█         | 1106/10016 [3:57:43<31:55:08, 12.90s/it, avr_loss=0.0261]
steps:  11%|█         | 1106/10016 [3:57:43<31:55:08, 12.90s/it, avr_loss=0.026] 
steps:  11%|█         | 1107/10016 [3:57:56<31:54:55, 12.90s/it, avr_loss=0.026]
steps:  11%|█         | 1107/10016 [3:57:56<31:54:55, 12.90s/it, avr_loss=0.0262]
steps:  11%|█         | 1108/10016 [3:58:09<31:54:43, 12.90s/it, avr_loss=0.0262]
steps:  11%|█         | 1108/10016 [3:58:09<31:54:43, 12.90s/it, avr_loss=0.0262]
steps:  11%|█         | 1109/10016 [3:58:22<31:54:30, 12.90s/it, avr_loss=0.0262]
steps:  11%|█         | 1109/10016 [3:58:22<31:54:30, 12.90s/it, avr_loss=0.0262]
steps:  11%|█         | 1110/10016 [3:58:35<31:54:17, 12.90s/it, avr_loss=0.0262]
steps:  11%|█         | 1110/10016 [3:58:35<31:54:17, 12.90s/it, avr_loss=0.0262]
steps:  11%|█         | 1111/10016 [3:58:48<31:54:04, 12.90s/it, avr_loss=0.0262]
steps:  11%|█         | 1111/10016 [3:58:48<31:54:04, 12.90s/it, avr_loss=0.0262]
steps:  11%|█         | 1112/10016 [3:59:01<31:53:51, 12.90s/it, avr_loss=0.0262]
steps:  11%|█         | 1112/10016 [3:59:01<31:53:51, 12.90s/it, avr_loss=0.0262]
steps:  11%|█         | 1113/10016 [3:59:13<31:53:38, 12.90s/it, avr_loss=0.0262]
steps:  11%|█         | 1113/10016 [3:59:13<31:53:38, 12.90s/it, avr_loss=0.0262]
steps:  11%|█         | 1114/10016 [3:59:26<31:53:25, 12.90s/it, avr_loss=0.0262]
steps:  11%|█         | 1114/10016 [3:59:26<31:53:25, 12.90s/it, avr_loss=0.0262]
steps:  11%|█         | 1115/10016 [3:59:39<31:53:13, 12.90s/it, avr_loss=0.0262]
steps:  11%|█         | 1115/10016 [3:59:39<31:53:13, 12.90s/it, avr_loss=0.0261]
steps:  11%|█         | 1116/10016 [3:59:52<31:53:00, 12.90s/it, avr_loss=0.0261]
steps:  11%|█         | 1116/10016 [3:59:52<31:53:00, 12.90s/it, avr_loss=0.0262]
steps:  11%|█         | 1117/10016 [4:00:05<31:52:47, 12.90s/it, avr_loss=0.0262]
steps:  11%|█         | 1117/10016 [4:00:05<31:52:47, 12.90s/it, avr_loss=0.0262]
steps:  11%|█         | 1118/10016 [4:00:18<31:52:34, 12.90s/it, avr_loss=0.0262]
steps:  11%|█         | 1118/10016 [4:00:18<31:52:34, 12.90s/it, avr_loss=0.0262]
steps:  11%|█         | 1119/10016 [4:00:31<31:52:21, 12.90s/it, avr_loss=0.0262]
steps:  11%|█         | 1119/10016 [4:00:31<31:52:21, 12.90s/it, avr_loss=0.0263]
steps:  11%|█         | 1120/10016 [4:00:44<31:52:08, 12.90s/it, avr_loss=0.0263]
steps:  11%|█         | 1120/10016 [4:00:44<31:52:08, 12.90s/it, avr_loss=0.0263]
steps:  11%|█         | 1121/10016 [4:00:57<31:51:55, 12.90s/it, avr_loss=0.0263]
steps:  11%|█         | 1121/10016 [4:00:57<31:51:55, 12.90s/it, avr_loss=0.0263]
steps:  11%|█         | 1122/10016 [4:01:10<31:51:42, 12.90s/it, avr_loss=0.0263]
steps:  11%|█         | 1122/10016 [4:01:10<31:51:42, 12.90s/it, avr_loss=0.0263]
steps:  11%|█         | 1123/10016 [4:01:22<31:51:29, 12.90s/it, avr_loss=0.0263]
steps:  11%|█         | 1123/10016 [4:01:22<31:51:29, 12.90s/it, avr_loss=0.0263]
steps:  11%|█         | 1124/10016 [4:01:35<31:51:17, 12.90s/it, avr_loss=0.0263]
steps:  11%|█         | 1124/10016 [4:01:35<31:51:17, 12.90s/it, avr_loss=0.0263]
steps:  11%|█         | 1125/10016 [4:01:48<31:51:04, 12.90s/it, avr_loss=0.0263]
steps:  11%|█         | 1125/10016 [4:01:48<31:51:04, 12.90s/it, avr_loss=0.0263]
steps:  11%|█         | 1126/10016 [4:02:01<31:50:51, 12.90s/it, avr_loss=0.0263]
steps:  11%|█         | 1126/10016 [4:02:01<31:50:51, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1127/10016 [4:02:14<31:50:38, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1127/10016 [4:02:14<31:50:38, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1128/10016 [4:02:27<31:50:25, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1128/10016 [4:02:27<31:50:25, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1129/10016 [4:02:40<31:50:12, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1129/10016 [4:02:40<31:50:12, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1130/10016 [4:02:53<31:50:00, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1130/10016 [4:02:53<31:50:00, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1131/10016 [4:03:06<31:49:47, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1131/10016 [4:03:06<31:49:47, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1132/10016 [4:03:19<31:49:34, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1132/10016 [4:03:19<31:49:34, 12.90s/it, avr_loss=0.0265]
steps:  11%|█▏        | 1133/10016 [4:03:31<31:49:21, 12.90s/it, avr_loss=0.0265]
steps:  11%|█▏        | 1133/10016 [4:03:31<31:49:21, 12.90s/it, avr_loss=0.0264]
steps:  11%|█▏        | 1134/10016 [4:03:44<31:49:08, 12.90s/it, avr_loss=0.0264]
steps:  11%|█▏        | 1134/10016 [4:03:44<31:49:08, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1135/10016 [4:03:57<31:48:55, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1135/10016 [4:03:57<31:48:55, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1136/10016 [4:04:10<31:48:42, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1136/10016 [4:04:10<31:48:42, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1137/10016 [4:04:23<31:48:29, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1137/10016 [4:04:23<31:48:29, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1138/10016 [4:04:36<31:48:17, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1138/10016 [4:04:36<31:48:17, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1139/10016 [4:04:49<31:48:04, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1139/10016 [4:04:49<31:48:04, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1140/10016 [4:05:02<31:47:51, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1140/10016 [4:05:02<31:47:51, 12.90s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1141/10016 [4:05:15<31:47:38, 12.90s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1141/10016 [4:05:15<31:47:38, 12.90s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1142/10016 [4:05:28<31:47:25, 12.90s/it, avr_loss=0.0262]
steps:  11%|█▏        | 1142/10016 [4:05:28<31:47:25, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1143/10016 [4:05:40<31:47:12, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1143/10016 [4:05:40<31:47:12, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1144/10016 [4:05:53<31:47:00, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1144/10016 [4:05:53<31:47:00, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1145/10016 [4:06:06<31:46:47, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1145/10016 [4:06:06<31:46:47, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1146/10016 [4:06:19<31:46:34, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1146/10016 [4:06:19<31:46:34, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1147/10016 [4:06:32<31:46:21, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1147/10016 [4:06:32<31:46:21, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1148/10016 [4:06:45<31:46:08, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1148/10016 [4:06:45<31:46:08, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1149/10016 [4:06:58<31:45:55, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1149/10016 [4:06:58<31:45:55, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1150/10016 [4:07:11<31:45:42, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1150/10016 [4:07:11<31:45:42, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1151/10016 [4:07:24<31:45:29, 12.90s/it, avr_loss=0.0263]
steps:  11%|█▏        | 1151/10016 [4:07:24<31:45:29, 12.90s/it, avr_loss=0.0263]
steps:  12%|█▏        | 1152/10016 [4:07:37<31:45:17, 12.90s/it, avr_loss=0.0263]
steps:  12%|█▏        | 1152/10016 [4:07:37<31:45:17, 12.90s/it, avr_loss=0.0263]
steps:  12%|█▏        | 1153/10016 [4:07:49<31:45:04, 12.90s/it, avr_loss=0.0263]
steps:  12%|█▏        | 1153/10016 [4:07:49<31:45:04, 12.90s/it, avr_loss=0.0263]
steps:  12%|█▏        | 1154/10016 [4:08:02<31:44:51, 12.90s/it, avr_loss=0.0263]
steps:  12%|█▏        | 1154/10016 [4:08:02<31:44:51, 12.90s/it, avr_loss=0.0263]
steps:  12%|█▏        | 1155/10016 [4:08:15<31:44:38, 12.90s/it, avr_loss=0.0263]
steps:  12%|█▏        | 1155/10016 [4:08:15<31:44:38, 12.90s/it, avr_loss=0.0263]
steps:  12%|█▏        | 1156/10016 [4:08:28<31:44:25, 12.90s/it, avr_loss=0.0263]
steps:  12%|█▏        | 1156/10016 [4:08:28<31:44:25, 12.90s/it, avr_loss=0.0263]
steps:  12%|█▏        | 1157/10016 [4:08:41<31:44:12, 12.90s/it, avr_loss=0.0263]
steps:  12%|█▏        | 1157/10016 [4:08:41<31:44:12, 12.90s/it, avr_loss=0.0263]
steps:  12%|█▏        | 1158/10016 [4:08:54<31:43:59, 12.90s/it, avr_loss=0.0263]
steps:  12%|█▏        | 1158/10016 [4:08:54<31:43:59, 12.90s/it, avr_loss=0.0263]
steps:  12%|█▏        | 1159/10016 [4:09:07<31:43:46, 12.90s/it, avr_loss=0.0263]
steps:  12%|█▏        | 1159/10016 [4:09:07<31:43:46, 12.90s/it, avr_loss=0.0263]
steps:  12%|█▏        | 1160/10016 [4:09:20<31:43:34, 12.90s/it, avr_loss=0.0263]
steps:  12%|█▏        | 1160/10016 [4:09:20<31:43:34, 12.90s/it, avr_loss=0.0263]
steps:  12%|█▏        | 1161/10016 [4:09:33<31:43:21, 12.90s/it, avr_loss=0.0263]
steps:  12%|█▏        | 1161/10016 [4:09:33<31:43:21, 12.90s/it, avr_loss=0.0263]
steps:  12%|█▏        | 1162/10016 [4:09:46<31:43:08, 12.90s/it, avr_loss=0.0263]
steps:  12%|█▏        | 1162/10016 [4:09:46<31:43:08, 12.90s/it, avr_loss=0.0263]
steps:  12%|█▏        | 1163/10016 [4:09:58<31:42:55, 12.90s/it, avr_loss=0.0263]
steps:  12%|█▏        | 1163/10016 [4:09:58<31:42:55, 12.90s/it, avr_loss=0.0262]
steps:  12%|█▏        | 1164/10016 [4:10:11<31:42:42, 12.90s/it, avr_loss=0.0262]
steps:  12%|█▏        | 1164/10016 [4:10:11<31:42:42, 12.90s/it, avr_loss=0.0263]
steps:  12%|█▏        | 1165/10016 [4:10:24<31:42:29, 12.90s/it, avr_loss=0.0263]
steps:  12%|█▏        | 1165/10016 [4:10:24<31:42:29, 12.90s/it, avr_loss=0.0262]
steps:  12%|█▏        | 1166/10016 [4:10:37<31:42:16, 12.90s/it, avr_loss=0.0262]
steps:  12%|█▏        | 1166/10016 [4:10:37<31:42:16, 12.90s/it, avr_loss=0.0262]
steps:  12%|█▏        | 1167/10016 [4:10:50<31:42:03, 12.90s/it, avr_loss=0.0262]
steps:  12%|█▏        | 1167/10016 [4:10:50<31:42:03, 12.90s/it, avr_loss=0.0262]
steps:  12%|█▏        | 1168/10016 [4:11:03<31:41:51, 12.90s/it, avr_loss=0.0262]
steps:  12%|█▏        | 1168/10016 [4:11:03<31:41:51, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1169/10016 [4:11:16<31:41:38, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1169/10016 [4:11:16<31:41:38, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1170/10016 [4:11:29<31:41:25, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1170/10016 [4:11:29<31:41:25, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1171/10016 [4:11:42<31:41:12, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1171/10016 [4:11:42<31:41:12, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1172/10016 [4:11:55<31:40:59, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1172/10016 [4:11:55<31:40:59, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1173/10016 [4:12:08<31:40:46, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1173/10016 [4:12:08<31:40:46, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1174/10016 [4:12:20<31:40:34, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1174/10016 [4:12:20<31:40:34, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1175/10016 [4:12:33<31:40:21, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1175/10016 [4:12:33<31:40:21, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1176/10016 [4:12:46<31:40:08, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1176/10016 [4:12:46<31:40:08, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1177/10016 [4:12:59<31:39:55, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1177/10016 [4:12:59<31:39:55, 12.90s/it, avr_loss=0.026] 
steps:  12%|█▏        | 1178/10016 [4:13:12<31:39:42, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1178/10016 [4:13:12<31:39:42, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1179/10016 [4:13:25<31:39:29, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1179/10016 [4:13:25<31:39:29, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1180/10016 [4:13:38<31:39:16, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1180/10016 [4:13:38<31:39:16, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1181/10016 [4:13:51<31:39:03, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1181/10016 [4:13:51<31:39:03, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1182/10016 [4:14:04<31:38:51, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1182/10016 [4:14:04<31:38:51, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1183/10016 [4:14:17<31:38:38, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1183/10016 [4:14:17<31:38:38, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1184/10016 [4:14:29<31:38:25, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1184/10016 [4:14:29<31:38:25, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1185/10016 [4:14:42<31:38:12, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1185/10016 [4:14:42<31:38:12, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1186/10016 [4:14:55<31:37:59, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1186/10016 [4:14:55<31:37:59, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1187/10016 [4:15:08<31:37:46, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1187/10016 [4:15:08<31:37:46, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1188/10016 [4:15:21<31:37:33, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1188/10016 [4:15:21<31:37:33, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1189/10016 [4:15:34<31:37:21, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1189/10016 [4:15:34<31:37:21, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1190/10016 [4:15:47<31:37:08, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1190/10016 [4:15:47<31:37:08, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1191/10016 [4:16:00<31:36:55, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1191/10016 [4:16:00<31:36:55, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1192/10016 [4:16:13<31:36:42, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1192/10016 [4:16:13<31:36:42, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1193/10016 [4:16:26<31:36:29, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1193/10016 [4:16:26<31:36:29, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1194/10016 [4:16:38<31:36:16, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1194/10016 [4:16:38<31:36:16, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1195/10016 [4:16:51<31:36:04, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1195/10016 [4:16:51<31:36:04, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1196/10016 [4:17:04<31:35:51, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1196/10016 [4:17:04<31:35:51, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1197/10016 [4:17:17<31:35:38, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1197/10016 [4:17:17<31:35:38, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1198/10016 [4:17:30<31:35:25, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1198/10016 [4:17:30<31:35:25, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1199/10016 [4:17:43<31:35:12, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1199/10016 [4:17:43<31:35:12, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1200/10016 [4:17:56<31:34:59, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1200/10016 [4:17:56<31:34:59, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1201/10016 [4:18:09<31:34:46, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1201/10016 [4:18:09<31:34:46, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1202/10016 [4:18:22<31:34:33, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1202/10016 [4:18:22<31:34:33, 12.90s/it, avr_loss=0.026] 
steps:  12%|█▏        | 1203/10016 [4:18:35<31:34:20, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1203/10016 [4:18:35<31:34:20, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1204/10016 [4:18:47<31:34:08, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1204/10016 [4:18:47<31:34:08, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1205/10016 [4:19:00<31:33:55, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1205/10016 [4:19:00<31:33:55, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1206/10016 [4:19:13<31:33:42, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1206/10016 [4:19:13<31:33:42, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1207/10016 [4:19:26<31:33:29, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1207/10016 [4:19:26<31:33:29, 12.90s/it, avr_loss=0.026] 
steps:  12%|█▏        | 1208/10016 [4:19:39<31:33:16, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1208/10016 [4:19:39<31:33:16, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1209/10016 [4:19:52<31:33:05, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1209/10016 [4:19:52<31:33:05, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1210/10016 [4:20:05<31:32:52, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1210/10016 [4:20:05<31:32:52, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1211/10016 [4:20:18<31:32:39, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1211/10016 [4:20:18<31:32:39, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1212/10016 [4:20:31<31:32:25, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1212/10016 [4:20:31<31:32:25, 12.90s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1213/10016 [4:20:44<31:32:12, 12.90s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1213/10016 [4:20:44<31:32:12, 12.90s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1214/10016 [4:20:57<31:31:59, 12.90s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1214/10016 [4:20:57<31:31:59, 12.90s/it, avr_loss=0.026] 
steps:  12%|█▏        | 1215/10016 [4:21:09<31:31:46, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1215/10016 [4:21:09<31:31:46, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1216/10016 [4:21:22<31:31:33, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1216/10016 [4:21:22<31:31:33, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1217/10016 [4:21:35<31:31:20, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1217/10016 [4:21:35<31:31:20, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1218/10016 [4:21:48<31:31:08, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1218/10016 [4:21:48<31:31:08, 12.90s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1219/10016 [4:22:01<31:30:55, 12.90s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1219/10016 [4:22:01<31:30:55, 12.90s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1220/10016 [4:22:14<31:30:42, 12.90s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1220/10016 [4:22:14<31:30:42, 12.90s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1221/10016 [4:22:27<31:30:29, 12.90s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1221/10016 [4:22:27<31:30:29, 12.90s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1222/10016 [4:22:40<31:30:16, 12.90s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1222/10016 [4:22:40<31:30:16, 12.90s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1223/10016 [4:22:53<31:30:04, 12.90s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1223/10016 [4:22:53<31:30:04, 12.90s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1224/10016 [4:23:06<31:29:51, 12.90s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1224/10016 [4:23:06<31:29:51, 12.90s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1225/10016 [4:23:18<31:29:38, 12.90s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1225/10016 [4:23:18<31:29:38, 12.90s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1226/10016 [4:23:31<31:29:25, 12.90s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1226/10016 [4:23:31<31:29:25, 12.90s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1227/10016 [4:23:44<31:29:12, 12.90s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1227/10016 [4:23:44<31:29:12, 12.90s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1228/10016 [4:23:57<31:28:59, 12.90s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1228/10016 [4:23:57<31:28:59, 12.90s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1229/10016 [4:24:10<31:28:46, 12.90s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1229/10016 [4:24:10<31:28:46, 12.90s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1230/10016 [4:24:23<31:28:33, 12.90s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1230/10016 [4:24:23<31:28:33, 12.90s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1231/10016 [4:24:36<31:28:20, 12.90s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1231/10016 [4:24:36<31:28:20, 12.90s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1232/10016 [4:24:49<31:28:08, 12.90s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1232/10016 [4:24:49<31:28:08, 12.90s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1233/10016 [4:25:02<31:27:55, 12.90s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1233/10016 [4:25:02<31:27:55, 12.90s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1234/10016 [4:25:14<31:27:42, 12.90s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1234/10016 [4:25:14<31:27:42, 12.90s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1235/10016 [4:25:27<31:27:29, 12.90s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1235/10016 [4:25:27<31:27:29, 12.90s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1236/10016 [4:25:40<31:27:16, 12.90s/it, avr_loss=0.0258]
steps:  12%|█▏        | 1236/10016 [4:25:40<31:27:16, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1237/10016 [4:25:53<31:27:03, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1237/10016 [4:25:53<31:27:03, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1238/10016 [4:26:06<31:26:50, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1238/10016 [4:26:06<31:26:50, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1239/10016 [4:26:19<31:26:37, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1239/10016 [4:26:19<31:26:37, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1240/10016 [4:26:32<31:26:24, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1240/10016 [4:26:32<31:26:24, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1241/10016 [4:26:45<31:26:11, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1241/10016 [4:26:45<31:26:11, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1242/10016 [4:26:58<31:25:58, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1242/10016 [4:26:58<31:25:58, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1243/10016 [4:27:11<31:25:45, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1243/10016 [4:27:11<31:25:45, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1244/10016 [4:27:23<31:25:32, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1244/10016 [4:27:23<31:25:32, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1245/10016 [4:27:36<31:25:19, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1245/10016 [4:27:36<31:25:19, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1246/10016 [4:27:49<31:25:07, 12.90s/it, avr_loss=0.0261]
steps:  12%|█▏        | 1246/10016 [4:27:49<31:25:07, 12.90s/it, avr_loss=0.026] 
steps:  12%|█▏        | 1247/10016 [4:28:02<31:24:54, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1247/10016 [4:28:02<31:24:54, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1248/10016 [4:28:15<31:24:41, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1248/10016 [4:28:15<31:24:41, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1249/10016 [4:28:28<31:24:28, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1249/10016 [4:28:28<31:24:28, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1250/10016 [4:28:41<31:24:15, 12.90s/it, avr_loss=0.026]
steps:  12%|█▏        | 1250/10016 [4:28:41<31:24:15, 12.90s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1251/10016 [4:28:54<31:24:02, 12.90s/it, avr_loss=0.0259]
steps:  12%|█▏        | 1251/10016 [4:28:54<31:24:02, 12.90s/it, avr_loss=0.0259]
steps:  12%|█▎        | 1252/10016 [4:29:07<31:23:49, 12.90s/it, avr_loss=0.0259]
steps:  12%|█▎        | 1252/10016 [4:29:07<31:23:49, 12.90s/it, avr_loss=0.0259]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_robotwin/loss-curve-retrain/train_lora-000002.safetensors

epoch 3/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3

steps:  13%|█▎        | 1253/10016 [4:29:21<31:23:50, 12.90s/it, avr_loss=0.0259]
steps:  13%|█▎        | 1253/10016 [4:29:21<31:23:50, 12.90s/it, avr_loss=0.0259]
steps:  13%|█▎        | 1254/10016 [4:29:34<31:23:37, 12.90s/it, avr_loss=0.0259]
steps:  13%|█▎        | 1254/10016 [4:29:34<31:23:37, 12.90s/it, avr_loss=0.0259]
steps:  13%|█▎        | 1255/10016 [4:29:47<31:23:24, 12.90s/it, avr_loss=0.0259]
steps:  13%|█▎        | 1255/10016 [4:29:47<31:23:24, 12.90s/it, avr_loss=0.0259]
steps:  13%|█▎        | 1256/10016 [4:30:00<31:23:11, 12.90s/it, avr_loss=0.0259]
steps:  13%|█▎        | 1256/10016 [4:30:00<31:23:11, 12.90s/it, avr_loss=0.0259]
steps:  13%|█▎        | 1257/10016 [4:30:13<31:22:58, 12.90s/it, avr_loss=0.0259]
steps:  13%|█▎        | 1257/10016 [4:30:13<31:22:58, 12.90s/it, avr_loss=0.0259]
steps:  13%|█▎        | 1258/10016 [4:30:26<31:22:45, 12.90s/it, avr_loss=0.0259]
steps:  13%|█▎        | 1258/10016 [4:30:26<31:22:45, 12.90s/it, avr_loss=0.0259]
steps:  13%|█▎        | 1259/10016 [4:30:39<31:22:32, 12.90s/it, avr_loss=0.0259]
steps:  13%|█▎        | 1259/10016 [4:30:39<31:22:32, 12.90s/it, avr_loss=0.0259]
steps:  13%|█▎        | 1260/10016 [4:30:52<31:22:19, 12.90s/it, avr_loss=0.0259]
steps:  13%|█▎        | 1260/10016 [4:30:52<31:22:19, 12.90s/it, avr_loss=0.0259]
steps:  13%|█▎        | 1261/10016 [4:31:05<31:22:06, 12.90s/it, avr_loss=0.0259]
steps:  13%|█▎        | 1261/10016 [4:31:05<31:22:06, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1262/10016 [4:31:17<31:21:53, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1262/10016 [4:31:17<31:21:53, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1263/10016 [4:31:30<31:21:40, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1263/10016 [4:31:30<31:21:40, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1264/10016 [4:31:43<31:21:27, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1264/10016 [4:31:43<31:21:27, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1265/10016 [4:31:56<31:21:14, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1265/10016 [4:31:56<31:21:14, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1266/10016 [4:32:09<31:21:01, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1266/10016 [4:32:09<31:21:01, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1267/10016 [4:32:22<31:20:48, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1267/10016 [4:32:22<31:20:48, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1268/10016 [4:32:35<31:20:35, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1268/10016 [4:32:35<31:20:35, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1269/10016 [4:32:48<31:20:22, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1269/10016 [4:32:48<31:20:22, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1270/10016 [4:33:01<31:20:09, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1270/10016 [4:33:01<31:20:09, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1271/10016 [4:33:13<31:19:56, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1271/10016 [4:33:13<31:19:56, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1272/10016 [4:33:26<31:19:43, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1272/10016 [4:33:26<31:19:43, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1273/10016 [4:33:39<31:19:30, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1273/10016 [4:33:39<31:19:30, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1274/10016 [4:33:52<31:19:17, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1274/10016 [4:33:52<31:19:17, 12.90s/it, avr_loss=0.0259]
steps:  13%|█▎        | 1275/10016 [4:34:05<31:19:04, 12.90s/it, avr_loss=0.0259]
steps:  13%|█▎        | 1275/10016 [4:34:05<31:19:04, 12.90s/it, avr_loss=0.0259]
steps:  13%|█▎        | 1276/10016 [4:34:18<31:18:51, 12.90s/it, avr_loss=0.0259]
steps:  13%|█▎        | 1276/10016 [4:34:18<31:18:51, 12.90s/it, avr_loss=0.026] 
steps:  13%|█▎        | 1277/10016 [4:34:31<31:18:38, 12.90s/it, avr_loss=0.026]
steps:  13%|█▎        | 1277/10016 [4:34:31<31:18:38, 12.90s/it, avr_loss=0.0259]
steps:  13%|█▎        | 1278/10016 [4:34:44<31:18:25, 12.90s/it, avr_loss=0.0259]
steps:  13%|█▎        | 1278/10016 [4:34:44<31:18:25, 12.90s/it, avr_loss=0.0259]
steps:  13%|█▎        | 1279/10016 [4:34:56<31:18:12, 12.90s/it, avr_loss=0.0259]
steps:  13%|█▎        | 1279/10016 [4:34:56<31:18:12, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1280/10016 [4:35:09<31:17:59, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1280/10016 [4:35:09<31:17:59, 12.90s/it, avr_loss=0.0259]
steps:  13%|█▎        | 1281/10016 [4:35:22<31:17:46, 12.90s/it, avr_loss=0.0259]
steps:  13%|█▎        | 1281/10016 [4:35:22<31:17:46, 12.90s/it, avr_loss=0.0259]
steps:  13%|█▎        | 1282/10016 [4:35:35<31:17:33, 12.90s/it, avr_loss=0.0259]
steps:  13%|█▎        | 1282/10016 [4:35:35<31:17:33, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1283/10016 [4:35:48<31:17:20, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1283/10016 [4:35:48<31:17:20, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1284/10016 [4:36:01<31:17:07, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1284/10016 [4:36:01<31:17:07, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1285/10016 [4:36:14<31:16:54, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1285/10016 [4:36:14<31:16:54, 12.90s/it, avr_loss=0.0259]
steps:  13%|█▎        | 1286/10016 [4:36:27<31:16:41, 12.90s/it, avr_loss=0.0259]
steps:  13%|█▎        | 1286/10016 [4:36:27<31:16:41, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1287/10016 [4:36:40<31:16:28, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1287/10016 [4:36:40<31:16:28, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1288/10016 [4:36:52<31:16:15, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1288/10016 [4:36:52<31:16:15, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1289/10016 [4:37:05<31:16:02, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1289/10016 [4:37:05<31:16:02, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1290/10016 [4:37:18<31:15:49, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1290/10016 [4:37:18<31:15:49, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1291/10016 [4:37:31<31:15:36, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1291/10016 [4:37:31<31:15:36, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1292/10016 [4:37:44<31:15:23, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1292/10016 [4:37:44<31:15:23, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1293/10016 [4:37:57<31:15:11, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1293/10016 [4:37:57<31:15:11, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1294/10016 [4:38:10<31:14:58, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1294/10016 [4:38:10<31:14:58, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1295/10016 [4:38:23<31:14:45, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1295/10016 [4:38:23<31:14:45, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1296/10016 [4:38:36<31:14:32, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1296/10016 [4:38:36<31:14:32, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1297/10016 [4:38:48<31:14:19, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1297/10016 [4:38:48<31:14:19, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1298/10016 [4:39:01<31:14:06, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1298/10016 [4:39:01<31:14:06, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1299/10016 [4:39:14<31:13:53, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1299/10016 [4:39:14<31:13:53, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1300/10016 [4:39:27<31:13:40, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1300/10016 [4:39:27<31:13:40, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1301/10016 [4:39:40<31:13:27, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1301/10016 [4:39:40<31:13:27, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1302/10016 [4:39:53<31:13:14, 12.90s/it, avr_loss=0.0258]
steps:  13%|█▎        | 1302/10016 [4:39:53<31:13:14, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1303/10016 [4:40:06<31:13:01, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1303/10016 [4:40:06<31:13:01, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1304/10016 [4:40:19<31:12:48, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1304/10016 [4:40:19<31:12:48, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1305/10016 [4:40:32<31:12:35, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1305/10016 [4:40:32<31:12:35, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1306/10016 [4:40:44<31:12:22, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1306/10016 [4:40:44<31:12:22, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1307/10016 [4:40:57<31:12:09, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1307/10016 [4:40:57<31:12:09, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1308/10016 [4:41:10<31:11:56, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1308/10016 [4:41:10<31:11:56, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1309/10016 [4:41:23<31:11:43, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1309/10016 [4:41:23<31:11:43, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1310/10016 [4:41:36<31:11:30, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1310/10016 [4:41:36<31:11:30, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1311/10016 [4:41:49<31:11:17, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1311/10016 [4:41:49<31:11:17, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1312/10016 [4:42:02<31:11:04, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1312/10016 [4:42:02<31:11:04, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1313/10016 [4:42:15<31:10:51, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1313/10016 [4:42:15<31:10:51, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1314/10016 [4:42:28<31:10:38, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1314/10016 [4:42:28<31:10:38, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1315/10016 [4:42:40<31:10:25, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1315/10016 [4:42:40<31:10:25, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1316/10016 [4:42:53<31:10:12, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1316/10016 [4:42:53<31:10:12, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1317/10016 [4:43:06<31:09:59, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1317/10016 [4:43:06<31:09:59, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1318/10016 [4:43:19<31:09:47, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1318/10016 [4:43:19<31:09:47, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1319/10016 [4:43:32<31:09:34, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1319/10016 [4:43:32<31:09:34, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1320/10016 [4:43:45<31:09:21, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1320/10016 [4:43:45<31:09:21, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1321/10016 [4:43:58<31:09:08, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1321/10016 [4:43:58<31:09:08, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1322/10016 [4:44:11<31:08:55, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1322/10016 [4:44:11<31:08:55, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1323/10016 [4:44:24<31:08:42, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1323/10016 [4:44:24<31:08:42, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1324/10016 [4:44:36<31:08:29, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1324/10016 [4:44:36<31:08:29, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1325/10016 [4:44:49<31:08:16, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1325/10016 [4:44:49<31:08:16, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1326/10016 [4:45:02<31:08:03, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1326/10016 [4:45:02<31:08:03, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1327/10016 [4:45:15<31:07:50, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1327/10016 [4:45:15<31:07:50, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1328/10016 [4:45:28<31:07:38, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1328/10016 [4:45:28<31:07:38, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1329/10016 [4:45:41<31:07:25, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1329/10016 [4:45:41<31:07:25, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1330/10016 [4:45:54<31:07:12, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1330/10016 [4:45:54<31:07:12, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1331/10016 [4:46:07<31:06:59, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1331/10016 [4:46:07<31:06:59, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1332/10016 [4:46:20<31:06:46, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1332/10016 [4:46:20<31:06:46, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1333/10016 [4:46:33<31:06:34, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1333/10016 [4:46:33<31:06:34, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1334/10016 [4:46:46<31:06:21, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1334/10016 [4:46:46<31:06:21, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1335/10016 [4:46:58<31:06:08, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1335/10016 [4:46:58<31:06:08, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1336/10016 [4:47:11<31:05:55, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1336/10016 [4:47:11<31:05:55, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1337/10016 [4:47:24<31:05:42, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1337/10016 [4:47:24<31:05:42, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1338/10016 [4:47:37<31:05:29, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1338/10016 [4:47:37<31:05:29, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1339/10016 [4:47:50<31:05:16, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1339/10016 [4:47:50<31:05:16, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1340/10016 [4:48:03<31:05:03, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1340/10016 [4:48:03<31:05:03, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1341/10016 [4:48:16<31:04:50, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1341/10016 [4:48:16<31:04:50, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1342/10016 [4:48:29<31:04:37, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1342/10016 [4:48:29<31:04:37, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1343/10016 [4:48:42<31:04:24, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1343/10016 [4:48:42<31:04:24, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1344/10016 [4:48:54<31:04:11, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1344/10016 [4:48:54<31:04:11, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1345/10016 [4:49:07<31:03:59, 12.90s/it, avr_loss=0.0257]
steps:  13%|█▎        | 1345/10016 [4:49:07<31:03:59, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1346/10016 [4:49:20<31:03:46, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1346/10016 [4:49:20<31:03:46, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1347/10016 [4:49:33<31:03:33, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1347/10016 [4:49:33<31:03:33, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1348/10016 [4:49:46<31:03:20, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1348/10016 [4:49:46<31:03:20, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1349/10016 [4:49:59<31:03:07, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1349/10016 [4:49:59<31:03:07, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1350/10016 [4:50:12<31:02:54, 12.90s/it, avr_loss=0.0256]
steps:  13%|█▎        | 1350/10016 [4:50:12<31:02:54, 12.90s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1351/10016 [4:50:25<31:02:41, 12.90s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1351/10016 [4:50:25<31:02:41, 12.90s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1352/10016 [4:50:38<31:02:28, 12.90s/it, avr_loss=0.0255]
steps:  13%|█▎        | 1352/10016 [4:50:38<31:02:28, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1353/10016 [4:50:51<31:02:15, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1353/10016 [4:50:51<31:02:15, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1354/10016 [4:51:03<31:02:02, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1354/10016 [4:51:03<31:02:02, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1355/10016 [4:51:16<31:01:49, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1355/10016 [4:51:16<31:01:49, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1356/10016 [4:51:29<31:01:36, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1356/10016 [4:51:29<31:01:36, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1357/10016 [4:51:42<31:01:23, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1357/10016 [4:51:42<31:01:23, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1358/10016 [4:51:55<31:01:10, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1358/10016 [4:51:55<31:01:10, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1359/10016 [4:52:08<31:00:58, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1359/10016 [4:52:08<31:00:58, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1360/10016 [4:52:21<31:00:45, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1360/10016 [4:52:21<31:00:45, 12.90s/it, avr_loss=0.0256]
steps:  14%|█▎        | 1361/10016 [4:52:34<31:00:32, 12.90s/it, avr_loss=0.0256]
steps:  14%|█▎        | 1361/10016 [4:52:34<31:00:32, 12.90s/it, avr_loss=0.0256]
steps:  14%|█▎        | 1362/10016 [4:52:47<31:00:19, 12.90s/it, avr_loss=0.0256]
steps:  14%|█▎        | 1362/10016 [4:52:47<31:00:19, 12.90s/it, avr_loss=0.0256]
steps:  14%|█▎        | 1363/10016 [4:52:59<31:00:06, 12.90s/it, avr_loss=0.0256]
steps:  14%|█▎        | 1363/10016 [4:52:59<31:00:06, 12.90s/it, avr_loss=0.0256]
steps:  14%|█▎        | 1364/10016 [4:53:12<30:59:53, 12.90s/it, avr_loss=0.0256]
steps:  14%|█▎        | 1364/10016 [4:53:12<30:59:53, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1365/10016 [4:53:25<30:59:40, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1365/10016 [4:53:25<30:59:40, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1366/10016 [4:53:38<30:59:27, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1366/10016 [4:53:38<30:59:27, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1367/10016 [4:53:51<30:59:14, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1367/10016 [4:53:51<30:59:14, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1368/10016 [4:54:04<30:59:01, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1368/10016 [4:54:04<30:59:01, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1369/10016 [4:54:17<30:58:48, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1369/10016 [4:54:17<30:58:48, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1370/10016 [4:54:30<30:58:35, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1370/10016 [4:54:30<30:58:35, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1371/10016 [4:54:43<30:58:22, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1371/10016 [4:54:43<30:58:22, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1372/10016 [4:54:56<30:58:10, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1372/10016 [4:54:56<30:58:10, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1373/10016 [4:55:08<30:57:57, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1373/10016 [4:55:08<30:57:57, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1374/10016 [4:55:21<30:57:44, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▎        | 1374/10016 [4:55:21<30:57:44, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1375/10016 [4:55:34<30:57:31, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1375/10016 [4:55:34<30:57:31, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1376/10016 [4:55:47<30:57:18, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▎        | 1376/10016 [4:55:47<30:57:18, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▎        | 1377/10016 [4:56:00<30:57:05, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▎        | 1377/10016 [4:56:00<30:57:05, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1378/10016 [4:56:13<30:56:52, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1378/10016 [4:56:13<30:56:52, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1379/10016 [4:56:26<30:56:39, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1379/10016 [4:56:26<30:56:39, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1380/10016 [4:56:39<30:56:26, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1380/10016 [4:56:39<30:56:26, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1381/10016 [4:56:52<30:56:13, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1381/10016 [4:56:52<30:56:13, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1382/10016 [4:57:04<30:56:00, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1382/10016 [4:57:04<30:56:00, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1383/10016 [4:57:17<30:55:47, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1383/10016 [4:57:17<30:55:47, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1384/10016 [4:57:30<30:55:34, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1384/10016 [4:57:30<30:55:34, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1385/10016 [4:57:43<30:55:22, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1385/10016 [4:57:43<30:55:22, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1386/10016 [4:57:56<30:55:09, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1386/10016 [4:57:56<30:55:09, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1387/10016 [4:58:09<30:54:56, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1387/10016 [4:58:09<30:54:56, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1388/10016 [4:58:22<30:54:43, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1388/10016 [4:58:22<30:54:43, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1389/10016 [4:58:35<30:54:30, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1389/10016 [4:58:35<30:54:30, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1390/10016 [4:58:48<30:54:17, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1390/10016 [4:58:48<30:54:17, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1391/10016 [4:59:00<30:54:04, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1391/10016 [4:59:00<30:54:04, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1392/10016 [4:59:13<30:53:51, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1392/10016 [4:59:13<30:53:51, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1393/10016 [4:59:26<30:53:38, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1393/10016 [4:59:26<30:53:38, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1394/10016 [4:59:39<30:53:25, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1394/10016 [4:59:39<30:53:25, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1395/10016 [4:59:52<30:53:12, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1395/10016 [4:59:52<30:53:12, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1396/10016 [5:00:05<30:52:59, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1396/10016 [5:00:05<30:52:59, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1397/10016 [5:00:18<30:52:46, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1397/10016 [5:00:18<30:52:46, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1398/10016 [5:00:31<30:52:33, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1398/10016 [5:00:31<30:52:33, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1399/10016 [5:00:44<30:52:20, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1399/10016 [5:00:44<30:52:20, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1400/10016 [5:00:56<30:52:07, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1400/10016 [5:00:56<30:52:07, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1401/10016 [5:01:09<30:51:54, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1401/10016 [5:01:09<30:51:54, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1402/10016 [5:01:22<30:51:41, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1402/10016 [5:01:22<30:51:41, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1403/10016 [5:01:35<30:51:28, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1403/10016 [5:01:35<30:51:28, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1404/10016 [5:01:48<30:51:16, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1404/10016 [5:01:48<30:51:16, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1405/10016 [5:02:01<30:51:03, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1405/10016 [5:02:01<30:51:03, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1406/10016 [5:02:14<30:50:50, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1406/10016 [5:02:14<30:50:50, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1407/10016 [5:02:27<30:50:37, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1407/10016 [5:02:27<30:50:37, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1408/10016 [5:02:40<30:50:24, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1408/10016 [5:02:40<30:50:24, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1409/10016 [5:02:52<30:50:11, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1409/10016 [5:02:52<30:50:11, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1410/10016 [5:03:05<30:49:58, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1410/10016 [5:03:05<30:49:58, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1411/10016 [5:03:18<30:49:45, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1411/10016 [5:03:18<30:49:45, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1412/10016 [5:03:31<30:49:32, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1412/10016 [5:03:31<30:49:32, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1413/10016 [5:03:44<30:49:19, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1413/10016 [5:03:44<30:49:19, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1414/10016 [5:03:57<30:49:06, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1414/10016 [5:03:57<30:49:06, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1415/10016 [5:04:10<30:48:53, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1415/10016 [5:04:10<30:48:53, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1416/10016 [5:04:23<30:48:40, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1416/10016 [5:04:23<30:48:40, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1417/10016 [5:04:36<30:48:27, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1417/10016 [5:04:36<30:48:27, 12.90s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1418/10016 [5:04:48<30:48:14, 12.90s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1418/10016 [5:04:48<30:48:14, 12.90s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1419/10016 [5:05:01<30:48:01, 12.90s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1419/10016 [5:05:01<30:48:01, 12.90s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1420/10016 [5:05:14<30:47:48, 12.90s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1420/10016 [5:05:14<30:47:48, 12.90s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1421/10016 [5:05:27<30:47:36, 12.90s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1421/10016 [5:05:27<30:47:36, 12.90s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1422/10016 [5:05:40<30:47:23, 12.90s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1422/10016 [5:05:40<30:47:23, 12.90s/it, avr_loss=0.0251]
steps:  14%|█▍        | 1423/10016 [5:05:53<30:47:10, 12.90s/it, avr_loss=0.0251]
steps:  14%|█▍        | 1423/10016 [5:05:53<30:47:10, 12.90s/it, avr_loss=0.0251]
steps:  14%|█▍        | 1424/10016 [5:06:06<30:46:57, 12.90s/it, avr_loss=0.0251]
steps:  14%|█▍        | 1424/10016 [5:06:06<30:46:57, 12.90s/it, avr_loss=0.0251]
steps:  14%|█▍        | 1425/10016 [5:06:19<30:46:44, 12.90s/it, avr_loss=0.0251]
steps:  14%|█▍        | 1425/10016 [5:06:19<30:46:44, 12.90s/it, avr_loss=0.0251]
steps:  14%|█▍        | 1426/10016 [5:06:32<30:46:31, 12.90s/it, avr_loss=0.0251]
steps:  14%|█▍        | 1426/10016 [5:06:32<30:46:31, 12.90s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1427/10016 [5:06:45<30:46:19, 12.90s/it, avr_loss=0.0252]
steps:  14%|█▍        | 1427/10016 [5:06:45<30:46:19, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1428/10016 [5:06:58<30:46:06, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1428/10016 [5:06:58<30:46:06, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1429/10016 [5:07:10<30:45:53, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1429/10016 [5:07:10<30:45:53, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1430/10016 [5:07:23<30:45:40, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1430/10016 [5:07:23<30:45:40, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1431/10016 [5:07:36<30:45:27, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1431/10016 [5:07:36<30:45:27, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1432/10016 [5:07:49<30:45:14, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1432/10016 [5:07:49<30:45:14, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1433/10016 [5:08:02<30:45:01, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1433/10016 [5:08:02<30:45:01, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1434/10016 [5:08:15<30:44:48, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1434/10016 [5:08:15<30:44:48, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1435/10016 [5:08:28<30:44:35, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1435/10016 [5:08:28<30:44:35, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1436/10016 [5:08:41<30:44:22, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1436/10016 [5:08:41<30:44:22, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1437/10016 [5:08:54<30:44:09, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1437/10016 [5:08:54<30:44:09, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1438/10016 [5:09:06<30:43:56, 12.90s/it, avr_loss=0.0254]
steps:  14%|█▍        | 1438/10016 [5:09:06<30:43:56, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▍        | 1439/10016 [5:09:19<30:43:43, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▍        | 1439/10016 [5:09:19<30:43:43, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▍        | 1440/10016 [5:09:32<30:43:31, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▍        | 1440/10016 [5:09:32<30:43:31, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▍        | 1441/10016 [5:09:45<30:43:18, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▍        | 1441/10016 [5:09:45<30:43:18, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▍        | 1442/10016 [5:09:58<30:43:05, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▍        | 1442/10016 [5:09:58<30:43:05, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▍        | 1443/10016 [5:10:11<30:42:52, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▍        | 1443/10016 [5:10:11<30:42:52, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▍        | 1444/10016 [5:10:24<30:42:39, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▍        | 1444/10016 [5:10:24<30:42:39, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▍        | 1445/10016 [5:10:37<30:42:26, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▍        | 1445/10016 [5:10:37<30:42:26, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▍        | 1446/10016 [5:10:50<30:42:13, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▍        | 1446/10016 [5:10:50<30:42:13, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▍        | 1447/10016 [5:11:03<30:42:00, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▍        | 1447/10016 [5:11:03<30:42:00, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▍        | 1448/10016 [5:11:15<30:41:47, 12.90s/it, avr_loss=0.0255]
steps:  14%|█▍        | 1448/10016 [5:11:15<30:41:47, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1449/10016 [5:11:28<30:41:34, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1449/10016 [5:11:28<30:41:34, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1450/10016 [5:11:41<30:41:22, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1450/10016 [5:11:41<30:41:22, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1451/10016 [5:11:54<30:41:09, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1451/10016 [5:11:54<30:41:09, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1452/10016 [5:12:07<30:40:56, 12.90s/it, avr_loss=0.0253]
steps:  14%|█▍        | 1452/10016 [5:12:07<30:40:56, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1453/10016 [5:12:20<30:40:43, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1453/10016 [5:12:20<30:40:43, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1454/10016 [5:12:33<30:40:31, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1454/10016 [5:12:33<30:40:31, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1455/10016 [5:12:46<30:40:18, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1455/10016 [5:12:46<30:40:18, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1456/10016 [5:12:59<30:40:05, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1456/10016 [5:12:59<30:40:05, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1457/10016 [5:13:12<30:39:52, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1457/10016 [5:13:12<30:39:52, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1458/10016 [5:13:25<30:39:39, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1458/10016 [5:13:25<30:39:39, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1459/10016 [5:13:37<30:39:26, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1459/10016 [5:13:37<30:39:26, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1460/10016 [5:13:50<30:39:14, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1460/10016 [5:13:50<30:39:14, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1461/10016 [5:14:03<30:39:01, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1461/10016 [5:14:03<30:39:01, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1462/10016 [5:14:16<30:38:48, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1462/10016 [5:14:16<30:38:48, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1463/10016 [5:14:29<30:38:35, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1463/10016 [5:14:29<30:38:35, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1464/10016 [5:14:42<30:38:22, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1464/10016 [5:14:42<30:38:22, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1465/10016 [5:14:55<30:38:09, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1465/10016 [5:14:55<30:38:09, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1466/10016 [5:15:08<30:37:56, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1466/10016 [5:15:08<30:37:56, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1467/10016 [5:15:21<30:37:43, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1467/10016 [5:15:21<30:37:43, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1468/10016 [5:15:34<30:37:31, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1468/10016 [5:15:34<30:37:31, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1469/10016 [5:15:46<30:37:18, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1469/10016 [5:15:46<30:37:18, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1470/10016 [5:15:59<30:37:05, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1470/10016 [5:15:59<30:37:05, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1471/10016 [5:16:12<30:36:52, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1471/10016 [5:16:12<30:36:52, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1472/10016 [5:16:25<30:36:39, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1472/10016 [5:16:25<30:36:39, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1473/10016 [5:16:38<30:36:26, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1473/10016 [5:16:38<30:36:26, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1474/10016 [5:16:51<30:36:13, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1474/10016 [5:16:51<30:36:13, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1475/10016 [5:17:04<30:36:01, 12.90s/it, avr_loss=0.0253]
steps:  15%|█▍        | 1475/10016 [5:17:04<30:36:01, 12.90s/it, avr_loss=0.0251]
steps:  15%|█▍        | 1476/10016 [5:17:17<30:35:48, 12.90s/it, avr_loss=0.0251]
steps:  15%|█▍        | 1476/10016 [5:17:17<30:35:48, 12.90s/it, avr_loss=0.0251]
steps:  15%|█▍        | 1477/10016 [5:17:30<30:35:35, 12.90s/it, avr_loss=0.0251]
steps:  15%|█▍        | 1477/10016 [5:17:30<30:35:35, 12.90s/it, avr_loss=0.025] 
steps:  15%|█▍        | 1478/10016 [5:17:43<30:35:22, 12.90s/it, avr_loss=0.025]
steps:  15%|█▍        | 1478/10016 [5:17:43<30:35:22, 12.90s/it, avr_loss=0.025]
steps:  15%|█▍        | 1479/10016 [5:17:56<30:35:09, 12.90s/it, avr_loss=0.025]
steps:  15%|█▍        | 1479/10016 [5:17:56<30:35:09, 12.90s/it, avr_loss=0.025]
steps:  15%|█▍        | 1480/10016 [5:18:08<30:34:56, 12.90s/it, avr_loss=0.025]
steps:  15%|█▍        | 1480/10016 [5:18:08<30:34:56, 12.90s/it, avr_loss=0.025]
steps:  15%|█▍        | 1481/10016 [5:18:21<30:34:44, 12.90s/it, avr_loss=0.025]
steps:  15%|█▍        | 1481/10016 [5:18:21<30:34:44, 12.90s/it, avr_loss=0.025]
steps:  15%|█▍        | 1482/10016 [5:18:34<30:34:31, 12.90s/it, avr_loss=0.025]
steps:  15%|█▍        | 1482/10016 [5:18:34<30:34:31, 12.90s/it, avr_loss=0.025]
steps:  15%|█▍        | 1483/10016 [5:18:47<30:34:18, 12.90s/it, avr_loss=0.025]
steps:  15%|█▍        | 1483/10016 [5:18:47<30:34:18, 12.90s/it, avr_loss=0.025]
steps:  15%|█▍        | 1484/10016 [5:19:00<30:34:05, 12.90s/it, avr_loss=0.025]
steps:  15%|█▍        | 1484/10016 [5:19:00<30:34:05, 12.90s/it, avr_loss=0.0249]
steps:  15%|█▍        | 1485/10016 [5:19:13<30:33:52, 12.90s/it, avr_loss=0.0249]
steps:  15%|█▍        | 1485/10016 [5:19:13<30:33:52, 12.90s/it, avr_loss=0.025] 
steps:  15%|█▍        | 1486/10016 [5:19:26<30:33:39, 12.90s/it, avr_loss=0.025]
steps:  15%|█▍        | 1486/10016 [5:19:26<30:33:39, 12.90s/it, avr_loss=0.0251]
steps:  15%|█▍        | 1487/10016 [5:19:39<30:33:26, 12.90s/it, avr_loss=0.0251]
steps:  15%|█▍        | 1487/10016 [5:19:39<30:33:26, 12.90s/it, avr_loss=0.025] 
steps:  15%|█▍        | 1488/10016 [5:19:52<30:33:13, 12.90s/it, avr_loss=0.025]
steps:  15%|█▍        | 1488/10016 [5:19:52<30:33:13, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▍        | 1489/10016 [5:20:05<30:33:01, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▍        | 1489/10016 [5:20:05<30:33:01, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▍        | 1490/10016 [5:20:17<30:32:48, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▍        | 1490/10016 [5:20:17<30:32:48, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▍        | 1491/10016 [5:20:30<30:32:35, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▍        | 1491/10016 [5:20:30<30:32:35, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▍        | 1492/10016 [5:20:43<30:32:22, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▍        | 1492/10016 [5:20:43<30:32:22, 12.90s/it, avr_loss=0.0249]
steps:  15%|█▍        | 1493/10016 [5:20:56<30:32:09, 12.90s/it, avr_loss=0.0249]
steps:  15%|█▍        | 1493/10016 [5:20:56<30:32:09, 12.90s/it, avr_loss=0.0249]
steps:  15%|█▍        | 1494/10016 [5:21:09<30:31:56, 12.90s/it, avr_loss=0.0249]
steps:  15%|█▍        | 1494/10016 [5:21:09<30:31:56, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▍        | 1495/10016 [5:21:22<30:31:43, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▍        | 1495/10016 [5:21:22<30:31:43, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▍        | 1496/10016 [5:21:35<30:31:30, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▍        | 1496/10016 [5:21:35<30:31:30, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▍        | 1497/10016 [5:21:48<30:31:17, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▍        | 1497/10016 [5:21:48<30:31:17, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▍        | 1498/10016 [5:22:01<30:31:04, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▍        | 1498/10016 [5:22:01<30:31:04, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▍        | 1499/10016 [5:22:14<30:30:51, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▍        | 1499/10016 [5:22:14<30:30:51, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▍        | 1500/10016 [5:22:26<30:30:38, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▍        | 1500/10016 [5:22:26<30:30:38, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▍        | 1501/10016 [5:22:39<30:30:26, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▍        | 1501/10016 [5:22:39<30:30:26, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▍        | 1502/10016 [5:22:52<30:30:13, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▍        | 1502/10016 [5:22:52<30:30:13, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1503/10016 [5:23:05<30:30:00, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1503/10016 [5:23:05<30:30:00, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1504/10016 [5:23:18<30:29:47, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1504/10016 [5:23:18<30:29:47, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1505/10016 [5:23:31<30:29:34, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1505/10016 [5:23:31<30:29:34, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1506/10016 [5:23:44<30:29:21, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1506/10016 [5:23:44<30:29:21, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1507/10016 [5:23:57<30:29:08, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1507/10016 [5:23:57<30:29:08, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1508/10016 [5:24:10<30:28:55, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1508/10016 [5:24:10<30:28:55, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1509/10016 [5:24:23<30:28:42, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1509/10016 [5:24:23<30:28:42, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1510/10016 [5:24:35<30:28:29, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1510/10016 [5:24:35<30:28:29, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1511/10016 [5:24:48<30:28:17, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1511/10016 [5:24:48<30:28:17, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1512/10016 [5:25:01<30:28:04, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1512/10016 [5:25:01<30:28:04, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1513/10016 [5:25:14<30:27:51, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1513/10016 [5:25:14<30:27:51, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1514/10016 [5:25:27<30:27:38, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1514/10016 [5:25:27<30:27:38, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1515/10016 [5:25:40<30:27:25, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1515/10016 [5:25:40<30:27:25, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1516/10016 [5:25:53<30:27:12, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1516/10016 [5:25:53<30:27:12, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1517/10016 [5:26:06<30:26:59, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1517/10016 [5:26:06<30:26:59, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1518/10016 [5:26:19<30:26:46, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1518/10016 [5:26:19<30:26:46, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1519/10016 [5:26:31<30:26:33, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1519/10016 [5:26:31<30:26:33, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1520/10016 [5:26:44<30:26:20, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1520/10016 [5:26:44<30:26:20, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1521/10016 [5:26:57<30:26:07, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1521/10016 [5:26:57<30:26:07, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1522/10016 [5:27:10<30:25:54, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1522/10016 [5:27:10<30:25:54, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1523/10016 [5:27:23<30:25:42, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1523/10016 [5:27:23<30:25:42, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1524/10016 [5:27:36<30:25:29, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1524/10016 [5:27:36<30:25:29, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1525/10016 [5:27:49<30:25:16, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1525/10016 [5:27:49<30:25:16, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1526/10016 [5:28:02<30:25:03, 12.90s/it, avr_loss=0.0248]
steps:  15%|█▌        | 1526/10016 [5:28:02<30:25:03, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1527/10016 [5:28:15<30:24:50, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1527/10016 [5:28:15<30:24:50, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1528/10016 [5:28:28<30:24:37, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1528/10016 [5:28:28<30:24:37, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1529/10016 [5:28:40<30:24:24, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1529/10016 [5:28:40<30:24:24, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1530/10016 [5:28:53<30:24:11, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1530/10016 [5:28:53<30:24:11, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1531/10016 [5:29:06<30:23:59, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1531/10016 [5:29:06<30:23:59, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1532/10016 [5:29:19<30:23:46, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1532/10016 [5:29:19<30:23:46, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1533/10016 [5:29:32<30:23:33, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1533/10016 [5:29:32<30:23:33, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1534/10016 [5:29:45<30:23:20, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1534/10016 [5:29:45<30:23:20, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1535/10016 [5:29:58<30:23:07, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1535/10016 [5:29:58<30:23:07, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1536/10016 [5:30:11<30:22:55, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1536/10016 [5:30:11<30:22:55, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1537/10016 [5:30:24<30:22:42, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1537/10016 [5:30:24<30:22:42, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1538/10016 [5:30:37<30:22:29, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1538/10016 [5:30:37<30:22:29, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1539/10016 [5:30:50<30:22:16, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1539/10016 [5:30:50<30:22:16, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1540/10016 [5:31:02<30:22:03, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1540/10016 [5:31:02<30:22:03, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1541/10016 [5:31:15<30:21:50, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1541/10016 [5:31:15<30:21:50, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1542/10016 [5:31:28<30:21:37, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1542/10016 [5:31:28<30:21:37, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1543/10016 [5:31:41<30:21:24, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1543/10016 [5:31:41<30:21:24, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1544/10016 [5:31:54<30:21:11, 12.90s/it, avr_loss=0.0247]
steps:  15%|█▌        | 1544/10016 [5:31:54<30:21:11, 12.90s/it, avr_loss=0.0249]
steps:  15%|█▌        | 1545/10016 [5:32:07<30:20:58, 12.90s/it, avr_loss=0.0249]
steps:  15%|█▌        | 1545/10016 [5:32:07<30:20:58, 12.90s/it, avr_loss=0.0249]
steps:  15%|█▌        | 1546/10016 [5:32:20<30:20:46, 12.90s/it, avr_loss=0.0249]
steps:  15%|█▌        | 1546/10016 [5:32:20<30:20:46, 12.90s/it, avr_loss=0.0249]
steps:  15%|█▌        | 1547/10016 [5:32:33<30:20:33, 12.90s/it, avr_loss=0.0249]
steps:  15%|█▌        | 1547/10016 [5:32:33<30:20:33, 12.90s/it, avr_loss=0.0245]
steps:  15%|█▌        | 1548/10016 [5:32:46<30:20:20, 12.90s/it, avr_loss=0.0245]
steps:  15%|█▌        | 1548/10016 [5:32:46<30:20:20, 12.90s/it, avr_loss=0.0245]
steps:  15%|█▌        | 1549/10016 [5:32:58<30:20:07, 12.90s/it, avr_loss=0.0245]
steps:  15%|█▌        | 1549/10016 [5:32:58<30:20:07, 12.90s/it, avr_loss=0.0245]
steps:  15%|█▌        | 1550/10016 [5:33:11<30:19:54, 12.90s/it, avr_loss=0.0245]
steps:  15%|█▌        | 1550/10016 [5:33:11<30:19:54, 12.90s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1551/10016 [5:33:24<30:19:41, 12.90s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1551/10016 [5:33:24<30:19:41, 12.90s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1552/10016 [5:33:37<30:19:28, 12.90s/it, avr_loss=0.0246]
steps:  15%|█▌        | 1552/10016 [5:33:37<30:19:28, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1553/10016 [5:33:50<30:19:15, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1553/10016 [5:33:50<30:19:15, 12.90s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1554/10016 [5:34:03<30:19:02, 12.90s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1554/10016 [5:34:03<30:19:02, 12.90s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1555/10016 [5:34:16<30:18:49, 12.90s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1555/10016 [5:34:16<30:18:49, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1556/10016 [5:34:29<30:18:36, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1556/10016 [5:34:29<30:18:36, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1557/10016 [5:34:42<30:18:23, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1557/10016 [5:34:42<30:18:23, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1558/10016 [5:34:54<30:18:10, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1558/10016 [5:34:54<30:18:10, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1559/10016 [5:35:07<30:17:57, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1559/10016 [5:35:07<30:17:57, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1560/10016 [5:35:20<30:17:44, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1560/10016 [5:35:20<30:17:44, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1561/10016 [5:35:33<30:17:31, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1561/10016 [5:35:33<30:17:31, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1562/10016 [5:35:46<30:17:18, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1562/10016 [5:35:46<30:17:18, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1563/10016 [5:35:59<30:17:05, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1563/10016 [5:35:59<30:17:05, 12.90s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1564/10016 [5:36:12<30:16:52, 12.90s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1564/10016 [5:36:12<30:16:52, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1565/10016 [5:36:25<30:16:40, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1565/10016 [5:36:25<30:16:40, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1566/10016 [5:36:38<30:16:27, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1566/10016 [5:36:38<30:16:27, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1567/10016 [5:36:50<30:16:14, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1567/10016 [5:36:50<30:16:14, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1568/10016 [5:37:03<30:16:01, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1568/10016 [5:37:03<30:16:01, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1569/10016 [5:37:16<30:15:48, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1569/10016 [5:37:16<30:15:48, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1570/10016 [5:37:29<30:15:35, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1570/10016 [5:37:29<30:15:35, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1571/10016 [5:37:42<30:15:22, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1571/10016 [5:37:42<30:15:22, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1572/10016 [5:37:55<30:15:09, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1572/10016 [5:37:55<30:15:09, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1573/10016 [5:38:08<30:14:56, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1573/10016 [5:38:08<30:14:56, 12.90s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1574/10016 [5:38:21<30:14:43, 12.90s/it, avr_loss=0.0245]
steps:  16%|█▌        | 1574/10016 [5:38:21<30:14:43, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1575/10016 [5:38:34<30:14:30, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1575/10016 [5:38:34<30:14:30, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1576/10016 [5:38:47<30:14:17, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1576/10016 [5:38:47<30:14:17, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1577/10016 [5:38:59<30:14:04, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1577/10016 [5:38:59<30:14:04, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1578/10016 [5:39:12<30:13:51, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1578/10016 [5:39:12<30:13:51, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1579/10016 [5:39:25<30:13:39, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1579/10016 [5:39:25<30:13:39, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1580/10016 [5:39:38<30:13:26, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1580/10016 [5:39:38<30:13:26, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1581/10016 [5:39:51<30:13:13, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1581/10016 [5:39:51<30:13:13, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1582/10016 [5:40:04<30:13:00, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1582/10016 [5:40:04<30:13:00, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1583/10016 [5:40:17<30:12:47, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1583/10016 [5:40:17<30:12:47, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1584/10016 [5:40:30<30:12:34, 12.90s/it, avr_loss=0.0246]
steps:  16%|█▌        | 1584/10016 [5:40:30<30:12:34, 12.90s/it, avr_loss=0.0246]W0709 05:40:12.810000 466677 site-packages/torch/distributed/elastic/agent/server/api.py:719] Received Signals.SIGHUP death signal, shutting down workers
W0709 05:40:12.817000 466677 site-packages/torch/distributed/elastic/multiprocessing/api.py:900] Sending process 466847 closing signal SIGHUP
W0709 05:40:12.819000 466677 site-packages/torch/distributed/elastic/multiprocessing/api.py:900] Sending process 466848 closing signal SIGHUP
W0709 05:40:12.820000 466677 site-packages/torch/distributed/elastic/multiprocessing/api.py:900] Sending process 466849 closing signal SIGHUP
W0709 05:40:12.820000 466677 site-packages/torch/distributed/elastic/multiprocessing/api.py:900] Sending process 466850 closing signal SIGHUP
Traceback (most recent call last):
  File "/media/miniconda3/envs/musubi_copy/bin/accelerate", line 8, in <module>
    sys.exit(main())
  File "/media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/accelerate/commands/accelerate_cli.py", line 50, in main
    args.func(args)
  File "/media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/accelerate/commands/launch.py", line 1204, in launch_command
    multi_gpu_launcher(args)
  File "/media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/accelerate/commands/launch.py", line 825, in multi_gpu_launcher
    distrib_run.run(args)
  File "/media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/distributed/run.py", line 883, in run
    elastic_launch(
  File "/media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/distributed/launcher/api.py", line 139, in __call__
    return launch_agent(self._config, self._entrypoint, list(args))
  File "/media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/distributed/launcher/api.py", line 261, in launch_agent
    result = agent.run()
  File "/media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/distributed/elastic/metrics/api.py", line 138, in wrapper
    result = f(*args, **kwargs)
  File "/media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/distributed/elastic/agent/server/api.py", line 711, in run
    result = self._invoke_run(role)
  File "/media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/distributed/elastic/agent/server/api.py", line 870, in _invoke_run
    time.sleep(monitor_interval)
  File "/media/miniconda3/envs/musubi_copy/lib/python3.10/site-packages/torch/distributed/elastic/multiprocessing/api.py", line 84, in _terminate_process_handler
    raise SignalException(f"Process {os.getpid()} got signal: {sigval}", sigval=sigval)
torch.distributed.elastic.multiprocessing.api.SignalException: Process 466677 got signal: 1
