nohup: ignoring input
The following values were not passed to `accelerate launch` and had defaults used instead:
	`--num_machines` was set to a value of `1`
	`--mixed_precision` was set to a value of `'no'`
	`--dynamo_backend` was set to a value of `'no'`
To avoid this warning pass in values for each of the problematic parameters or run `accelerate config`.
Xformers is not installed!
Flash Attn is not installed!
Sage Attn is not installed!
Trying to import sageattention
Failed to import sageattention
INFO:hv_train_network:Load dataset config from /media/jiayueru/yuwanhe/musubi-tuner/data/test_video/config.toml
INFO:dataset.image_video_dataset:load video jsonl from /media/jiayueru/yuwanhe/musubi-tuner/data/test_video/metadata.jsonl
INFO:dataset.image_video_dataset:loaded 1 videos
INFO:dataset.config_utils:[Dataset 0]
  is_image_dataset: False
  resolution: (1146, 720)
  batch_size: 1
  num_repeats: 1
  caption_extension: "None"
  enable_bucket: True
  bucket_no_upscale: False
  cache_directory: "/media/jiayueru/yuwanhe/musubi-tuner/data/test_cache"
  debug_dataset: False
    video_directory: "None"
    video_jsonl_file: "/media/jiayueru/yuwanhe/musubi-tuner/data/test_video/metadata.jsonl"
    control_directory: "None"
    target_frames: (1,)
    frame_extraction: full
    frame_stride: 1
    frame_sample: 1
    max_frames: 300
    source_fps: 30.0


INFO:dataset.image_video_dataset:bucket: (1136, 720, 153), count: 4
INFO:dataset.image_video_dataset:total batches: 4
INFO:hv_train_network:preparing accelerator
accelerator device: cuda:1
INFO:hv_train_network:DiT precision: torch.bfloat16, weight precision: torch.bfloat16
INFO:hv_train_network:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:__main__:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:frame_pack.hunyuan_video_packed:Creating HunyuanVideoTransformer3DModelPacked
INFO:frame_pack.hunyuan_video_packed:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, device=cuda:1
Xformers is not installed!
Flash Attn is not installed!
Sage Attn is not installed!
Trying to import sageattention
Failed to import sageattention
INFO:hv_train_network:Load dataset config from /media/jiayueru/yuwanhe/musubi-tuner/data/test_video/config.toml
INFO:dataset.image_video_dataset:load video jsonl from /media/jiayueru/yuwanhe/musubi-tuner/data/test_video/metadata.jsonl
INFO:dataset.image_video_dataset:loaded 1 videos
INFO:dataset.config_utils:[Dataset 0]
  is_image_dataset: False
  resolution: (1146, 720)
  batch_size: 1
  num_repeats: 1
  caption_extension: "None"
  enable_bucket: True
  bucket_no_upscale: False
  cache_directory: "/media/jiayueru/yuwanhe/musubi-tuner/data/test_cache"
  debug_dataset: False
    video_directory: "None"
    video_jsonl_file: "/media/jiayueru/yuwanhe/musubi-tuner/data/test_video/metadata.jsonl"
    control_directory: "None"
    target_frames: (1,)
    frame_extraction: full
    frame_stride: 1
    frame_sample: 1
    max_frames: 300
    source_fps: 30.0


INFO:dataset.image_video_dataset:bucket: (1136, 720, 153), count: 4
INFO:dataset.image_video_dataset:total batches: 4
INFO:hv_train_network:preparing accelerator
accelerator device: cuda:0
INFO:hv_train_network:DiT precision: torch.bfloat16, weight precision: torch.bfloat16
INFO:hv_train_network:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:__main__:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:frame_pack.hunyuan_video_packed:Creating HunyuanVideoTransformer3DModelPacked
INFO:frame_pack.hunyuan_video_packed:Loading DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, device=cuda:0
INFO:frame_pack.hunyuan_video_packed:Loaded DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, info=<All keys matched successfully>
INFO:networks.lora:create LoRA network. base dim (rank): 32, alpha: 1
INFO:networks.lora:neuron dropout: p=None, rank dropout: p=None, module dropout: p=None
INFO:networks.lora:create LoRA for U-Net/DiT: 440 modules.
INFO:networks.lora:enable LoRA for U-Net: 440 modules
Gradient checkpointing enabled for HunyuanVideoTransformer3DModelPacked.
INFO:hv_train_network:use 8-bit AdamW optimizer | {}
INFO:frame_pack.hunyuan_video_packed:Loaded DiT model from /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7/diffusion_pytorch_model-00001-of-00003.safetensors, info=<All keys matched successfully>
import network module: networks.lora_framepack
INFO:networks.lora:create LoRA network. base dim (rank): 32, alpha: 1
INFO:networks.lora:neuron dropout: p=None, rank dropout: p=None, module dropout: p=None
INFO:networks.lora:create LoRA for U-Net/DiT: 440 modules.
INFO:networks.lora:enable LoRA for U-Net: 440 modules
Gradient checkpointing enabled for HunyuanVideoTransformer3DModelPacked.
prepare optimizer, data loader etc.
INFO:hv_train_network:use 8-bit AdamW optimizer | {}
override steps. steps for 16 epochs is / 指定エポックまでのステップ数: 32
running training / 学習開始
  num train items / 学習画像、動画数: 4
  num batches per epoch / 1epochのバッチ数: 2
  num epochs / epoch数: 16
  batch size per device / バッチサイズ: 1
  gradient accumulation steps / 勾配を合計するステップ数 = 1
  total optimization steps / 学習ステップ数: 32
INFO:hv_train_network:set DiT model name for metadata: /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:hv_train_network:set VAE model name for metadata: /media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/vae/diffusion_pytorch_model.safetensors
INFO:hv_train_network:set DiT model name for metadata: /media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
INFO:hv_train_network:set VAE model name for metadata: /media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/vae/diffusion_pytorch_model.safetensors
INFO:hv_train_network:DiT dtype: torch.bfloat16, device: cuda:1

steps:   0%|          | 0/32 [00:00<?, ?it/s]INFO:hv_train_network:DiT dtype: torch.bfloat16, device: cuda:0

epoch 1/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 0, epoch: 1

steps:   3%|▎         | 1/32 [00:39<20:12, 39.11s/it]
steps:   3%|▎         | 1/32 [00:39<20:12, 39.12s/it, avr_loss=0.0279]
steps:   6%|▋         | 2/32 [01:17<19:18, 38.61s/it, avr_loss=0.0279]
steps:   6%|▋         | 2/32 [01:17<19:18, 38.61s/it, avr_loss=0.0244]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_test_tensorboard/train_lora-000001.safetensors

epoch 2/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 1, epoch: 2

steps:   9%|▉         | 3/32 [01:57<18:55, 39.17s/it, avr_loss=0.0244]
steps:   9%|▉         | 3/32 [01:57<18:55, 39.17s/it, avr_loss=0.0196]
steps:  12%|█▎        | 4/32 [02:35<18:09, 38.91s/it, avr_loss=0.0196]
steps:  12%|█▎        | 4/32 [02:35<18:09, 38.91s/it, avr_loss=0.0188]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_test_tensorboard/train_lora-000002.safetensors

epoch 3/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 2, epoch: 3

steps:  16%|█▌        | 5/32 [03:16<17:40, 39.28s/it, avr_loss=0.0188]
steps:  16%|█▌        | 5/32 [03:16<17:40, 39.28s/it, avr_loss=0.0205]
steps:  19%|█▉        | 6/32 [03:54<16:56, 39.10s/it, avr_loss=0.0205]
steps:  19%|█▉        | 6/32 [03:54<16:56, 39.10s/it, avr_loss=0.026] 
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_test_tensorboard/train_lora-000003.safetensors

epoch 4/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 3, epoch: 4

steps:  22%|██▏       | 7/32 [04:35<16:22, 39.30s/it, avr_loss=0.026]
steps:  22%|██▏       | 7/32 [04:35<16:22, 39.30s/it, avr_loss=0.0255]
steps:  25%|██▌       | 8/32 [05:13<15:39, 39.16s/it, avr_loss=0.0255]
steps:  25%|██▌       | 8/32 [05:13<15:39, 39.16s/it, avr_loss=0.021] 
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_test_tensorboard/train_lora-000004.safetensors

epoch 5/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 4, epoch: 5

steps:  28%|██▊       | 9/32 [05:54<15:05, 39.37s/it, avr_loss=0.021]
steps:  28%|██▊       | 9/32 [05:54<15:05, 39.37s/it, avr_loss=0.0212]
steps:  31%|███▏      | 10/32 [06:32<14:23, 39.26s/it, avr_loss=0.0212]
steps:  31%|███▏      | 10/32 [06:32<14:23, 39.26s/it, avr_loss=0.0214]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_test_tensorboard/train_lora-000005.safetensors

epoch 6/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 5, epoch: 6

steps:  34%|███▍      | 11/32 [07:13<13:46, 39.37s/it, avr_loss=0.0214]
steps:  34%|███▍      | 11/32 [07:13<13:46, 39.37s/it, avr_loss=0.0214]
steps:  38%|███▊      | 12/32 [07:51<13:05, 39.28s/it, avr_loss=0.0214]
steps:  38%|███▊      | 12/32 [07:51<13:05, 39.28s/it, avr_loss=0.0202]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_test_tensorboard/train_lora-000006.safetensors

epoch 7/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 6, epoch: 7

steps:  41%|████      | 13/32 [08:31<12:27, 39.35s/it, avr_loss=0.0202]
steps:  41%|████      | 13/32 [08:31<12:27, 39.35s/it, avr_loss=0.0205]
steps:  44%|████▍     | 14/32 [09:09<11:46, 39.26s/it, avr_loss=0.0205]
steps:  44%|████▍     | 14/32 [09:09<11:46, 39.26s/it, avr_loss=0.0246]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_test_tensorboard/train_lora-000007.safetensors

epoch 8/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 7, epoch: 8

steps:  47%|████▋     | 15/32 [09:49<11:08, 39.31s/it, avr_loss=0.0246]
steps:  47%|████▋     | 15/32 [09:49<11:08, 39.31s/it, avr_loss=0.0304]
steps:  50%|█████     | 16/32 [10:27<10:27, 39.24s/it, avr_loss=0.0304]
steps:  50%|█████     | 16/32 [10:27<10:27, 39.24s/it, avr_loss=0.0335]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_test_tensorboard/train_lora-000008.safetensors

epoch 9/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 8, epoch: 9

steps:  53%|█████▎    | 17/32 [11:07<09:49, 39.29s/it, avr_loss=0.0335]
steps:  53%|█████▎    | 17/32 [11:07<09:49, 39.29s/it, avr_loss=0.0313]
steps:  56%|█████▋    | 18/32 [11:46<09:09, 39.23s/it, avr_loss=0.0313]
steps:  56%|█████▋    | 18/32 [11:46<09:09, 39.23s/it, avr_loss=0.0245]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_test_tensorboard/train_lora-000009.safetensors

epoch 10/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 9, epoch: 10

steps:  59%|█████▉    | 19/32 [12:26<08:31, 39.31s/it, avr_loss=0.0245]
steps:  59%|█████▉    | 19/32 [12:26<08:31, 39.31s/it, avr_loss=0.0194]
steps:  62%|██████▎   | 20/32 [13:05<07:51, 39.25s/it, avr_loss=0.0194]
steps:  62%|██████▎   | 20/32 [13:05<07:51, 39.25s/it, avr_loss=0.021] 
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_test_tensorboard/train_lora-000010.safetensors

epoch 11/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 10, epoch: 11

steps:  66%|██████▌   | 21/32 [13:45<07:12, 39.30s/it, avr_loss=0.021]
steps:  66%|██████▌   | 21/32 [13:45<07:12, 39.30s/it, avr_loss=0.021]
steps:  69%|██████▉   | 22/32 [14:23<06:32, 39.25s/it, avr_loss=0.021]
steps:  69%|██████▉   | 22/32 [14:23<06:32, 39.25s/it, avr_loss=0.0193]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_test_tensorboard/train_lora-000011.safetensors

epoch 12/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 11, epoch: 12

steps:  72%|███████▏  | 23/32 [15:03<05:53, 39.30s/it, avr_loss=0.0193]
steps:  72%|███████▏  | 23/32 [15:03<05:53, 39.30s/it, avr_loss=0.0438]
steps:  75%|███████▌  | 24/32 [15:41<05:13, 39.25s/it, avr_loss=0.0438]
steps:  75%|███████▌  | 24/32 [15:41<05:13, 39.25s/it, avr_loss=0.0461]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_test_tensorboard/train_lora-000012.safetensors

epoch 13/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 12, epoch: 13

steps:  78%|███████▊  | 25/32 [16:22<04:35, 39.30s/it, avr_loss=0.0461]
steps:  78%|███████▊  | 25/32 [16:22<04:35, 39.30s/it, avr_loss=0.0209]
steps:  81%|████████▏ | 26/32 [17:00<03:55, 39.25s/it, avr_loss=0.0209]
steps:  81%|████████▏ | 26/32 [17:00<03:55, 39.25s/it, avr_loss=0.0175]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_test_tensorboard/train_lora-000013.safetensors

epoch 14/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 13, epoch: 14

steps:  84%|████████▍ | 27/32 [17:40<03:16, 39.28s/it, avr_loss=0.0175]
steps:  84%|████████▍ | 27/32 [17:40<03:16, 39.28s/it, avr_loss=0.0181]
steps:  88%|████████▊ | 28/32 [18:18<02:36, 39.24s/it, avr_loss=0.0181]
steps:  88%|████████▊ | 28/32 [18:18<02:36, 39.24s/it, avr_loss=0.0187]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_test_tensorboard/train_lora-000014.safetensors

epoch 15/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 14, epoch: 15

steps:  91%|█████████ | 29/32 [18:59<01:57, 39.28s/it, avr_loss=0.0187]
steps:  91%|█████████ | 29/32 [18:59<01:57, 39.28s/it, avr_loss=0.0202]
steps:  94%|█████████▍| 30/32 [19:37<01:18, 39.24s/it, avr_loss=0.0202]
steps:  94%|█████████▍| 30/32 [19:37<01:18, 39.24s/it, avr_loss=0.0241]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_test_tensorboard/train_lora-000015.safetensors

epoch 16/16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16
INFO:dataset.image_video_dataset:epoch is incremented. current_epoch: 15, epoch: 16

steps:  97%|█████████▋| 31/32 [20:17<00:39, 39.28s/it, avr_loss=0.0241]
steps:  97%|█████████▋| 31/32 [20:17<00:39, 39.28s/it, avr_loss=0.0223]
steps: 100%|██████████| 32/32 [20:55<00:00, 39.25s/it, avr_loss=0.0223]
steps: 100%|██████████| 32/32 [20:55<00:00, 39.25s/it, avr_loss=0.0222]
saving checkpoint: /media/jiayueru/yuwanhe/musubi-tuner/ckpts/output_test_tensorboard/train_lora.safetensors
INFO:hv_train_network:model saved.

steps: 100%|██████████| 32/32 [20:58<00:00, 39.33s/it, avr_loss=0.0222]
